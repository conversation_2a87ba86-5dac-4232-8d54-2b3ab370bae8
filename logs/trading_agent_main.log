2025-06-09 12:46:59.910 | INFO     | utils.logging:setup_logging:153 | [PID:453061] Logging initialized - Level: INFO, File: logs/trading_agent_main.log, Worker ID: main
2025-06-09 12:46:59.910 | INFO     | utils.logging:setup_logging:157 | [PID:453061] Worker logging setup complete - Worker ID: main
2025-06-09 12:46:59.910 | INFO     | __main__:cli:98 | FinRL Trading Agent CLI initialized
2025-06-09 12:47:03.345 | INFO     | __main__:tune:312 | Starting hyperparameter tuning
2025-06-09 12:47:03.453 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-09 12:47:03.453 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-09 12:47:03.455 | INFO     | utils.logging:info:191 | Processing stock data: 23370 records
2025-06-09 12:47:03.471 | INFO     | utils.logging:info:191 | [PID:453061] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 12:47:05.229 | INFO     | utils.logging:info:191 | Added turbulence index: mean=1.1040, max=32.7216
2025-06-09 12:47:05.233 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 12:47:05.248 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.015s
2025-06-09 12:47:05.249 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 12:47:05.249 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.016s
2025-06-09 12:47:05.249 | INFO     | utils.logging:info:191 | [PID:453061] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 12:47:05.249 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 12:47:05.263 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 12:47:05.263 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.014s
2025-06-09 12:47:05.264 | INFO     | utils.logging:info:191 | [PID:453061] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: ['vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 12:47:05.274 | INFO     | utils.logging:info:191 | [PID:453061] DataProcessor.merge_with_vix: Extended tech_indicator_list with VIX features (len: 36): ['vix_regime']
2025-06-09 12:47:05.279 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 23370 records
2025-06-09 12:47:05.279 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 12:47:05.299 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 12:47:05.325 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 23370 records, 45 features
2025-06-09 12:47:05.325 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 1.871s
2025-06-09 12:47:05.326 | INFO     | __main__:tune:333 | --- Quick Stats for processed_for_env_df (after DataProcessor.process_stock_data) ---
2025-06-09 12:47:05.326 | INFO     | __main__:tune:334 | Shape: (23370, 45)
2025-06-09 12:47:05.326 | INFO     | __main__:tune:336 | Columns: ['date', 'open', 'high', 'low', 'close', 'volume', 'dividends', 'stock splits', 'tic', 'sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'rsi_14', 'cci_20', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'turbulence', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 12:47:05.334 | INFO     | __main__:tune:339 | NaN counts per column (if any):
Series([], dtype: int64)
2025-06-09 12:47:05.335 | INFO     | __main__:tune:340 | Inf counts per column (if any):
Series([], dtype: int64)
2025-06-09 12:47:05.336 | INFO     | __main__:tune:342 | Date range in processed_for_env_df ('date'): 2016-01-04 00:00:00 to 2025-04-17 00:00:00
2025-06-09 12:47:05.336 | INFO     | __main__:tune:344 | Unique 'tic' in processed_for_env_df: 10
2025-06-09 12:47:05.336 | INFO     | __main__:tune:347 | --- End Quick Stats for processed_for_env_df ---
2025-06-09 12:47:05.337 | INFO     | __main__:tune:358 | Assuming VIX indicators are pre-merged and correctly named in the loaded data.
2025-06-09 12:47:05.347 | INFO     | __main__:tune:377 | Training data: 18690 records, Validation data: 4680 records
2025-06-09 12:47:05.347 | INFO     | __main__:tune:424 | Transforming data format for FinRL environment compatibility
2025-06-09 12:47:05.365 | INFO     | __main__:tune:428 | Train_df columns before deduplication (showing only duplicated ones): []
2025-06-09 12:47:05.367 | INFO     | __main__:tune:430 | Train_df columns after deduplication: 45
2025-06-09 12:47:05.367 | INFO     | __main__:tune:432 | Val_df columns before deduplication (showing only duplicated ones): []
2025-06-09 12:47:05.368 | INFO     | __main__:tune:434 | Val_df columns after deduplication: 45
2025-06-09 12:47:05.368 | INFO     | __main__:tune:436 | Transformed training data: 18690 records, Validation data: 4680 records
2025-06-09 12:47:05.369 | INFO     | __main__:tune:447 | Training data index: day, shape: (18690, 45)
2025-06-09 12:47:05.369 | INFO     | __main__:tune:448 | Validation data index: day, shape: (4680, 45)
2025-06-09 12:47:05.369 | INFO     | __main__:tune:457 | ✓ training data has correct 'day' index for FinRL (range: 0 to 1868)
2025-06-09 12:47:05.370 | INFO     | __main__:tune:457 | ✓ validation data has correct 'day' index for FinRL (range: 0 to 467)
2025-06-09 12:47:05.370 | INFO     | __main__:tune:460 | Applying NaN fill and type check for technical indicators in training data.
2025-06-09 12:47:05.385 | INFO     | __main__:tune:475 | Applying NaN fill and type check for technical indicators in validation data.
2025-06-09 12:47:05.413 | INFO     | __main__:tune:588 | Environment created: state_dim=431, action_dim=10
2025-06-09 12:47:05.413 | INFO     | utils.logging:__enter__:344 | Starting Hyperparameter Optimization | Context: 
2025-06-09 12:47:05.413 | INFO     | utils.logging:info:191 | Starting optimization with config: {'n_trials': 10, 'timeout': None, 'n_jobs': 1, 'sampler': 'tpe', 'pruner': 'median', 'direction': 'maximize', 'study_name': 'sac_trading_optimization', 'storage': 'sqlite:///optuna_studies.db', 'load_if_exists': False, 'directions': None, 'pruning_warmup_steps': 10, 'pruning_interval_steps': 50, 'early_stopping_rounds': None, 'early_stopping_threshold': None, 'learning_rate_range': (1e-05, 0.01), 'batch_size_choices': [64, 128, 256, 512], 'gamma_range': (0.9, 0.999), 'tau_range': (0.001, 0.01), 'alpha_range': (0.1, 0.5), 'hidden_sizes_choices': [[128, 128], [256, 256], [512, 256], [256, 256, 128]]}
2025-06-09 12:47:05.660 | INFO     | utils.logging:info:191 | Applied additional config overrides: {'learning_rate': 0.00010334384169604976, 'batch_size': 128, 'net_dims': [128, 64], 'gamma': 0.993394706416294, 'soft_update_tau': 0.0015707173338914732, 'alpha': 0.13139917306023538}
2025-06-09 12:47:05.660 | INFO     | utils.logging:info:191 | SAC Agent initialized: state_dim=431, action_dim=10, device=cpu
2025-06-09 12:47:05.660 | INFO     | utils.logging:info:191 | Creating SAC agent
2025-06-09 12:47:05.660 | INFO     | utils.logging:info:191 | SACAgent _create_agent: Determined gpu_id_to_pass: -1 from device setting: cpu
2025-06-09 12:47:06.429 | INFO     | utils.logging:info:191 | SAC agent created successfully with params: {'net_dims': [128, 64], 'state_dim': 431, 'action_dim': 10, 'gpu_id': -1, 'args': {'num_envs': 1, 'agent_class': None, 'if_off_policy': True, 'env_class': None, 'env_args': None, 'env_name': None, 'max_step': 12345, 'state_dim': None, 'action_dim': None, 'if_discrete': None, 'gamma': 0.993394706416294, 'reward_scale': 1, 'net_dims': [128, 128], 'learning_rate': 0.00010334384169604976, 'clip_grad_norm': 3.0, 'state_value_tau': 0, 'soft_update_tau': 0.005, 'continue_train': False, 'batch_size': 128, 'horizon_len': 512, 'buffer_size': 1000000, 'repeat_times': 1.0, 'if_use_per': False, 'lambda_fit_cum_r': 0.0, 'buffer_init_size': 512, 'gpu_id': 0, 'num_workers': 2, 'num_threads': 8, 'random_seed': None, 'learner_gpu_ids': (), 'cwd': None, 'if_remove': True, 'break_step': 100000, 'break_score': inf, 'if_keep_save': True, 'if_over_write': False, 'if_save_buffer': False, 'save_gap': 8, 'eval_times': 3, 'eval_per_step': 20000, 'eval_env_class': None, 'eval_env_args': None, 'eval_record_step': 0, 'target_entropy': -10.0}}
2025-06-09 12:47:06.429 | INFO     | utils.logging:info:191 | Starting SAC training: 10000 timesteps
2025-06-09 12:47:06.431 | INFO     | utils.logging:info:191 | SACAgent train: Set ElegantRL config.gpu_id: -1 from device setting: cpu
2025-06-09 12:47:06.432 | INFO     | utils.logging:info:191 | === TRAINING CONFIGURATION ===
2025-06-09 12:47:06.432 | INFO     | utils.logging:info:191 | total_timesteps = 10000 (main training control - when to stop)
2025-06-09 12:47:06.432 | INFO     | utils.logging:info:191 | break_step = 10000 (ElegantRL equivalent of total_timesteps)
2025-06-09 12:47:06.433 | INFO     | utils.logging:info:191 | max_step = 1869 (per-episode limit - max steps per trading episode)
2025-06-09 12:47:06.433 | INFO     | utils.logging:info:191 | explore_step = 10000 (learning starts - from config)
2025-06-09 12:47:06.433 | INFO     | utils.logging:info:191 | eval_gap = 2500 (evaluation frequency - from eval_freq parameter)
2025-06-09 12:47:06.433 | INFO     | utils.logging:info:191 | horizon_len = 512 (steps per iteration - default)
2025-06-09 12:47:06.433 | INFO     | utils.logging:info:191 | === END CONFIGURATION ===
2025-06-09 12:49:08.244 | SUCCESS  | utils.logging:success:216 | SAC training completed: 10000 timesteps
2025-06-09 12:49:08.245 | INFO     | utils.logging:wrapper:392 | Performance - SACAgent.train: 122.585s
2025-06-09 12:49:23.613 | INFO     | utils.logging:info:191 | Applied additional config overrides: {'learning_rate': 0.0009093264040654015, 'batch_size': 512, 'net_dims': [64, 64], 'gamma': 0.9771867179217213, 'soft_update_tau': 0.008078412321386215, 'alpha': 0.2982166613196865}
2025-06-09 12:49:23.614 | INFO     | utils.logging:info:191 | SAC Agent initialized: state_dim=431, action_dim=10, device=cpu
2025-06-09 12:49:23.614 | INFO     | utils.logging:info:191 | Creating SAC agent
2025-06-09 12:49:23.614 | INFO     | utils.logging:info:191 | SACAgent _create_agent: Determined gpu_id_to_pass: -1 from device setting: cpu
2025-06-09 12:49:23.619 | INFO     | utils.logging:info:191 | SAC agent created successfully with params: {'net_dims': [64, 64], 'state_dim': 431, 'action_dim': 10, 'gpu_id': -1, 'args': {'num_envs': 1, 'agent_class': None, 'if_off_policy': True, 'env_class': None, 'env_args': None, 'env_name': None, 'max_step': 12345, 'state_dim': None, 'action_dim': None, 'if_discrete': None, 'gamma': 0.9771867179217213, 'reward_scale': 1, 'net_dims': [128, 128], 'learning_rate': 0.0009093264040654015, 'clip_grad_norm': 3.0, 'state_value_tau': 0, 'soft_update_tau': 0.005, 'continue_train': False, 'batch_size': 512, 'horizon_len': 512, 'buffer_size': 1000000, 'repeat_times': 1.0, 'if_use_per': False, 'lambda_fit_cum_r': 0.0, 'buffer_init_size': 512, 'gpu_id': 0, 'num_workers': 2, 'num_threads': 8, 'random_seed': None, 'learner_gpu_ids': (), 'cwd': None, 'if_remove': True, 'break_step': 100000, 'break_score': inf, 'if_keep_save': True, 'if_over_write': False, 'if_save_buffer': False, 'save_gap': 8, 'eval_times': 3, 'eval_per_step': 20000, 'eval_env_class': None, 'eval_env_args': None, 'eval_record_step': 0, 'target_entropy': -10.0}}
2025-06-09 12:49:23.619 | INFO     | utils.logging:info:191 | Starting SAC training: 10000 timesteps
2025-06-09 12:49:23.623 | INFO     | utils.logging:info:191 | SACAgent train: Set ElegantRL config.gpu_id: -1 from device setting: cpu
2025-06-09 12:49:23.623 | INFO     | utils.logging:info:191 | === TRAINING CONFIGURATION ===
2025-06-09 12:49:23.624 | INFO     | utils.logging:info:191 | total_timesteps = 10000 (main training control - when to stop)
2025-06-09 12:49:23.624 | INFO     | utils.logging:info:191 | break_step = 10000 (ElegantRL equivalent of total_timesteps)
2025-06-09 12:49:23.624 | INFO     | utils.logging:info:191 | max_step = 1869 (per-episode limit - max steps per trading episode)
2025-06-09 12:49:23.624 | INFO     | utils.logging:info:191 | explore_step = 10000 (learning starts - from config)
2025-06-09 12:49:23.624 | INFO     | utils.logging:info:191 | eval_gap = 2500 (evaluation frequency - from eval_freq parameter)
2025-06-09 12:49:23.625 | INFO     | utils.logging:info:191 | horizon_len = 512 (steps per iteration - default)
2025-06-09 12:49:23.625 | INFO     | utils.logging:info:191 | === END CONFIGURATION ===
2025-06-09 12:52:04.798 | SUCCESS  | utils.logging:success:216 | SAC training completed: 10000 timesteps
2025-06-09 12:52:04.798 | INFO     | utils.logging:wrapper:392 | Performance - SACAgent.train: 161.184s
2025-06-09 12:52:20.167 | INFO     | utils.logging:info:191 | Applied additional config overrides: {'learning_rate': 0.0027094959198005306, 'batch_size': 64, 'net_dims': [256, 256], 'gamma': 0.9214290005383855, 'soft_update_tau': 0.0048995940750611025, 'alpha': 0.17971306797571196}
2025-06-09 12:52:20.168 | INFO     | utils.logging:info:191 | SAC Agent initialized: state_dim=431, action_dim=10, device=cpu
2025-06-09 12:52:20.168 | INFO     | utils.logging:info:191 | Creating SAC agent
2025-06-09 12:52:20.169 | INFO     | utils.logging:info:191 | SACAgent _create_agent: Determined gpu_id_to_pass: -1 from device setting: cpu
2025-06-09 12:52:20.182 | INFO     | utils.logging:info:191 | SAC agent created successfully with params: {'net_dims': [256, 256], 'state_dim': 431, 'action_dim': 10, 'gpu_id': -1, 'args': {'num_envs': 1, 'agent_class': None, 'if_off_policy': True, 'env_class': None, 'env_args': None, 'env_name': None, 'max_step': 12345, 'state_dim': None, 'action_dim': None, 'if_discrete': None, 'gamma': 0.9214290005383855, 'reward_scale': 1, 'net_dims': [128, 128], 'learning_rate': 0.0027094959198005306, 'clip_grad_norm': 3.0, 'state_value_tau': 0, 'soft_update_tau': 0.005, 'continue_train': False, 'batch_size': 64, 'horizon_len': 512, 'buffer_size': 1000000, 'repeat_times': 1.0, 'if_use_per': False, 'lambda_fit_cum_r': 0.0, 'buffer_init_size': 512, 'gpu_id': 0, 'num_workers': 2, 'num_threads': 8, 'random_seed': None, 'learner_gpu_ids': (), 'cwd': None, 'if_remove': True, 'break_step': 100000, 'break_score': inf, 'if_keep_save': True, 'if_over_write': False, 'if_save_buffer': False, 'save_gap': 8, 'eval_times': 3, 'eval_per_step': 20000, 'eval_env_class': None, 'eval_env_args': None, 'eval_record_step': 0, 'target_entropy': -10.0}}
2025-06-09 12:52:20.182 | INFO     | utils.logging:info:191 | Starting SAC training: 10000 timesteps
2025-06-09 12:52:20.187 | INFO     | utils.logging:info:191 | SACAgent train: Set ElegantRL config.gpu_id: -1 from device setting: cpu
2025-06-09 12:52:20.188 | INFO     | utils.logging:info:191 | === TRAINING CONFIGURATION ===
2025-06-09 12:52:20.189 | INFO     | utils.logging:info:191 | total_timesteps = 10000 (main training control - when to stop)
2025-06-09 12:52:20.189 | INFO     | utils.logging:info:191 | break_step = 10000 (ElegantRL equivalent of total_timesteps)
2025-06-09 12:52:20.190 | INFO     | utils.logging:info:191 | max_step = 1869 (per-episode limit - max steps per trading episode)
2025-06-09 12:52:20.190 | INFO     | utils.logging:info:191 | explore_step = 10000 (learning starts - from config)
2025-06-09 12:52:20.190 | INFO     | utils.logging:info:191 | eval_gap = 2500 (evaluation frequency - from eval_freq parameter)
2025-06-09 12:52:20.191 | INFO     | utils.logging:info:191 | horizon_len = 512 (steps per iteration - default)
2025-06-09 12:52:20.191 | INFO     | utils.logging:info:191 | === END CONFIGURATION ===
2025-06-09 12:54:33.911 | SUCCESS  | utils.logging:success:216 | SAC training completed: 10000 timesteps
2025-06-09 12:54:33.911 | INFO     | utils.logging:wrapper:392 | Performance - SACAgent.train: 133.743s
2025-06-09 12:54:56.990 | INFO     | utils.logging:info:191 | Applied additional config overrides: {'learning_rate': 0.007240980366816915, 'batch_size': 64, 'net_dims': [256, 256], 'gamma': 0.914572971715059, 'soft_update_tau': 0.008724040751133345, 'alpha': 0.29756694504201947}
2025-06-09 12:54:56.991 | INFO     | utils.logging:info:191 | SAC Agent initialized: state_dim=431, action_dim=10, device=cpu
2025-06-09 12:54:56.991 | INFO     | utils.logging:info:191 | Creating SAC agent
2025-06-09 12:54:56.992 | INFO     | utils.logging:info:191 | SACAgent _create_agent: Determined gpu_id_to_pass: -1 from device setting: cpu
2025-06-09 12:54:57.005 | INFO     | utils.logging:info:191 | SAC agent created successfully with params: {'net_dims': [256, 256], 'state_dim': 431, 'action_dim': 10, 'gpu_id': -1, 'args': {'num_envs': 1, 'agent_class': None, 'if_off_policy': True, 'env_class': None, 'env_args': None, 'env_name': None, 'max_step': 12345, 'state_dim': None, 'action_dim': None, 'if_discrete': None, 'gamma': 0.914572971715059, 'reward_scale': 1, 'net_dims': [128, 128], 'learning_rate': 0.007240980366816915, 'clip_grad_norm': 3.0, 'state_value_tau': 0, 'soft_update_tau': 0.005, 'continue_train': False, 'batch_size': 64, 'horizon_len': 512, 'buffer_size': 1000000, 'repeat_times': 1.0, 'if_use_per': False, 'lambda_fit_cum_r': 0.0, 'buffer_init_size': 512, 'gpu_id': 0, 'num_workers': 2, 'num_threads': 8, 'random_seed': None, 'learner_gpu_ids': (), 'cwd': None, 'if_remove': True, 'break_step': 100000, 'break_score': inf, 'if_keep_save': True, 'if_over_write': False, 'if_save_buffer': False, 'save_gap': 8, 'eval_times': 3, 'eval_per_step': 20000, 'eval_env_class': None, 'eval_env_args': None, 'eval_record_step': 0, 'target_entropy': -10.0}}
2025-06-09 12:54:57.005 | INFO     | utils.logging:info:191 | Starting SAC training: 10000 timesteps
2025-06-09 12:54:57.009 | INFO     | utils.logging:info:191 | SACAgent train: Set ElegantRL config.gpu_id: -1 from device setting: cpu
2025-06-09 12:54:57.011 | INFO     | utils.logging:info:191 | === TRAINING CONFIGURATION ===
2025-06-09 12:54:57.011 | INFO     | utils.logging:info:191 | total_timesteps = 10000 (main training control - when to stop)
2025-06-09 12:54:57.011 | INFO     | utils.logging:info:191 | break_step = 10000 (ElegantRL equivalent of total_timesteps)
2025-06-09 12:54:57.012 | INFO     | utils.logging:info:191 | max_step = 1869 (per-episode limit - max steps per trading episode)
2025-06-09 12:54:57.012 | INFO     | utils.logging:info:191 | explore_step = 10000 (learning starts - from config)
2025-06-09 12:54:57.013 | INFO     | utils.logging:info:191 | eval_gap = 2500 (evaluation frequency - from eval_freq parameter)
2025-06-09 12:54:57.013 | INFO     | utils.logging:info:191 | horizon_len = 512 (steps per iteration - default)
2025-06-09 12:54:57.014 | INFO     | utils.logging:info:191 | === END CONFIGURATION ===
2025-06-09 12:57:25.241 | SUCCESS  | utils.logging:success:216 | SAC training completed: 10000 timesteps
2025-06-09 12:57:25.242 | INFO     | utils.logging:wrapper:392 | Performance - SACAgent.train: 148.251s
2025-06-09 12:57:40.576 | INFO     | utils.logging:info:191 | Applied additional config overrides: {'learning_rate': 0.0003206070687251814, 'batch_size': 64, 'net_dims': [128, 64], 'gamma': 0.9846974926048303, 'soft_update_tau': 0.006053930146735184, 'alpha': 0.24060668701354754}
2025-06-09 12:57:40.576 | INFO     | utils.logging:info:191 | SAC Agent initialized: state_dim=431, action_dim=10, device=cpu
2025-06-09 12:57:40.577 | INFO     | utils.logging:info:191 | Creating SAC agent
2025-06-09 12:57:40.577 | INFO     | utils.logging:info:191 | SACAgent _create_agent: Determined gpu_id_to_pass: -1 from device setting: cpu
2025-06-09 12:57:40.583 | INFO     | utils.logging:info:191 | SAC agent created successfully with params: {'net_dims': [128, 64], 'state_dim': 431, 'action_dim': 10, 'gpu_id': -1, 'args': {'num_envs': 1, 'agent_class': None, 'if_off_policy': True, 'env_class': None, 'env_args': None, 'env_name': None, 'max_step': 12345, 'state_dim': None, 'action_dim': None, 'if_discrete': None, 'gamma': 0.9846974926048303, 'reward_scale': 1, 'net_dims': [128, 128], 'learning_rate': 0.0003206070687251814, 'clip_grad_norm': 3.0, 'state_value_tau': 0, 'soft_update_tau': 0.005, 'continue_train': False, 'batch_size': 64, 'horizon_len': 512, 'buffer_size': 1000000, 'repeat_times': 1.0, 'if_use_per': False, 'lambda_fit_cum_r': 0.0, 'buffer_init_size': 512, 'gpu_id': 0, 'num_workers': 2, 'num_threads': 8, 'random_seed': None, 'learner_gpu_ids': (), 'cwd': None, 'if_remove': True, 'break_step': 100000, 'break_score': inf, 'if_keep_save': True, 'if_over_write': False, 'if_save_buffer': False, 'save_gap': 8, 'eval_times': 3, 'eval_per_step': 20000, 'eval_env_class': None, 'eval_env_args': None, 'eval_record_step': 0, 'target_entropy': -10.0}}
2025-06-09 12:57:40.584 | INFO     | utils.logging:info:191 | Starting SAC training: 10000 timesteps
2025-06-09 12:57:40.587 | INFO     | utils.logging:info:191 | SACAgent train: Set ElegantRL config.gpu_id: -1 from device setting: cpu
2025-06-09 12:57:40.588 | INFO     | utils.logging:info:191 | === TRAINING CONFIGURATION ===
2025-06-09 12:57:40.588 | INFO     | utils.logging:info:191 | total_timesteps = 10000 (main training control - when to stop)
2025-06-09 12:57:40.588 | INFO     | utils.logging:info:191 | break_step = 10000 (ElegantRL equivalent of total_timesteps)
2025-06-09 12:57:40.588 | INFO     | utils.logging:info:191 | max_step = 1869 (per-episode limit - max steps per trading episode)
2025-06-09 12:57:40.589 | INFO     | utils.logging:info:191 | explore_step = 10000 (learning starts - from config)
2025-06-09 12:57:40.589 | INFO     | utils.logging:info:191 | eval_gap = 2500 (evaluation frequency - from eval_freq parameter)
2025-06-09 12:57:40.589 | INFO     | utils.logging:info:191 | horizon_len = 512 (steps per iteration - default)
2025-06-09 12:57:40.589 | INFO     | utils.logging:info:191 | === END CONFIGURATION ===
2025-06-09 13:00:03.049 | SUCCESS  | utils.logging:success:216 | SAC training completed: 10000 timesteps
2025-06-09 13:00:03.053 | INFO     | utils.logging:wrapper:392 | Performance - SACAgent.train: 142.477s
2025-06-09 13:00:24.840 | INFO     | utils.logging:info:191 | Applied additional config overrides: {'learning_rate': 0.0018588198830765722, 'batch_size': 256, 'net_dims': [64, 64], 'gamma': 0.9790923302417917, 'soft_update_tau': 0.0043319720662352255, 'alpha': 0.1062816732756839}
2025-06-09 13:00:24.841 | INFO     | utils.logging:info:191 | SAC Agent initialized: state_dim=431, action_dim=10, device=cpu
2025-06-09 13:00:24.841 | INFO     | utils.logging:info:191 | Creating SAC agent
2025-06-09 13:00:24.841 | INFO     | utils.logging:info:191 | SACAgent _create_agent: Determined gpu_id_to_pass: -1 from device setting: cpu
2025-06-09 13:00:24.848 | INFO     | utils.logging:info:191 | SAC agent created successfully with params: {'net_dims': [64, 64], 'state_dim': 431, 'action_dim': 10, 'gpu_id': -1, 'args': {'num_envs': 1, 'agent_class': None, 'if_off_policy': True, 'env_class': None, 'env_args': None, 'env_name': None, 'max_step': 12345, 'state_dim': None, 'action_dim': None, 'if_discrete': None, 'gamma': 0.9790923302417917, 'reward_scale': 1, 'net_dims': [128, 128], 'learning_rate': 0.0018588198830765722, 'clip_grad_norm': 3.0, 'state_value_tau': 0, 'soft_update_tau': 0.005, 'continue_train': False, 'batch_size': 256, 'horizon_len': 512, 'buffer_size': 1000000, 'repeat_times': 1.0, 'if_use_per': False, 'lambda_fit_cum_r': 0.0, 'buffer_init_size': 512, 'gpu_id': 0, 'num_workers': 2, 'num_threads': 8, 'random_seed': None, 'learner_gpu_ids': (), 'cwd': None, 'if_remove': True, 'break_step': 100000, 'break_score': inf, 'if_keep_save': True, 'if_over_write': False, 'if_save_buffer': False, 'save_gap': 8, 'eval_times': 3, 'eval_per_step': 20000, 'eval_env_class': None, 'eval_env_args': None, 'eval_record_step': 0, 'target_entropy': -10.0}}
2025-06-09 13:00:24.849 | INFO     | utils.logging:info:191 | Starting SAC training: 10000 timesteps
2025-06-09 13:00:24.852 | INFO     | utils.logging:info:191 | SACAgent train: Set ElegantRL config.gpu_id: -1 from device setting: cpu
2025-06-09 13:00:24.853 | INFO     | utils.logging:info:191 | === TRAINING CONFIGURATION ===
2025-06-09 13:00:24.853 | INFO     | utils.logging:info:191 | total_timesteps = 10000 (main training control - when to stop)
2025-06-09 13:00:24.854 | INFO     | utils.logging:info:191 | break_step = 10000 (ElegantRL equivalent of total_timesteps)
2025-06-09 13:00:24.854 | INFO     | utils.logging:info:191 | max_step = 1869 (per-episode limit - max steps per trading episode)
2025-06-09 13:00:24.854 | INFO     | utils.logging:info:191 | explore_step = 10000 (learning starts - from config)
2025-06-09 13:00:24.854 | INFO     | utils.logging:info:191 | eval_gap = 2500 (evaluation frequency - from eval_freq parameter)
2025-06-09 13:00:24.855 | INFO     | utils.logging:info:191 | horizon_len = 512 (steps per iteration - default)
2025-06-09 13:00:24.855 | INFO     | utils.logging:info:191 | === END CONFIGURATION ===
2025-06-09 13:02:36.371 | SUCCESS  | utils.logging:success:216 | SAC training completed: 10000 timesteps
2025-06-09 13:02:36.371 | INFO     | utils.logging:wrapper:392 | Performance - SACAgent.train: 131.530s
2025-06-09 13:02:57.768 | INFO     | utils.logging:info:191 | Applied additional config overrides: {'learning_rate': 0.0006590711772769139, 'batch_size': 512, 'net_dims': [64, 64], 'gamma': 0.9384680932701329, 'soft_update_tau': 0.006695058253062235, 'alpha': 0.18508942254867694}
2025-06-09 13:02:57.769 | INFO     | utils.logging:info:191 | SAC Agent initialized: state_dim=431, action_dim=10, device=cpu
2025-06-09 13:02:57.769 | INFO     | utils.logging:info:191 | Creating SAC agent
2025-06-09 13:02:57.769 | INFO     | utils.logging:info:191 | SACAgent _create_agent: Determined gpu_id_to_pass: -1 from device setting: cpu
2025-06-09 13:02:57.774 | INFO     | utils.logging:info:191 | SAC agent created successfully with params: {'net_dims': [64, 64], 'state_dim': 431, 'action_dim': 10, 'gpu_id': -1, 'args': {'num_envs': 1, 'agent_class': None, 'if_off_policy': True, 'env_class': None, 'env_args': None, 'env_name': None, 'max_step': 12345, 'state_dim': None, 'action_dim': None, 'if_discrete': None, 'gamma': 0.9384680932701329, 'reward_scale': 1, 'net_dims': [128, 128], 'learning_rate': 0.0006590711772769139, 'clip_grad_norm': 3.0, 'state_value_tau': 0, 'soft_update_tau': 0.005, 'continue_train': False, 'batch_size': 512, 'horizon_len': 512, 'buffer_size': 1000000, 'repeat_times': 1.0, 'if_use_per': False, 'lambda_fit_cum_r': 0.0, 'buffer_init_size': 512, 'gpu_id': 0, 'num_workers': 2, 'num_threads': 8, 'random_seed': None, 'learner_gpu_ids': (), 'cwd': None, 'if_remove': True, 'break_step': 100000, 'break_score': inf, 'if_keep_save': True, 'if_over_write': False, 'if_save_buffer': False, 'save_gap': 8, 'eval_times': 3, 'eval_per_step': 20000, 'eval_env_class': None, 'eval_env_args': None, 'eval_record_step': 0, 'target_entropy': -10.0}}
2025-06-09 13:02:57.775 | INFO     | utils.logging:info:191 | Starting SAC training: 10000 timesteps
2025-06-09 13:02:57.780 | INFO     | utils.logging:info:191 | SACAgent train: Set ElegantRL config.gpu_id: -1 from device setting: cpu
2025-06-09 13:02:57.781 | INFO     | utils.logging:info:191 | === TRAINING CONFIGURATION ===
2025-06-09 13:02:57.782 | INFO     | utils.logging:info:191 | total_timesteps = 10000 (main training control - when to stop)
2025-06-09 13:02:57.782 | INFO     | utils.logging:info:191 | break_step = 10000 (ElegantRL equivalent of total_timesteps)
2025-06-09 13:02:57.782 | INFO     | utils.logging:info:191 | max_step = 1869 (per-episode limit - max steps per trading episode)
2025-06-09 13:02:57.783 | INFO     | utils.logging:info:191 | explore_step = 10000 (learning starts - from config)
2025-06-09 13:02:57.783 | INFO     | utils.logging:info:191 | eval_gap = 2500 (evaluation frequency - from eval_freq parameter)
2025-06-09 13:02:57.783 | INFO     | utils.logging:info:191 | horizon_len = 512 (steps per iteration - default)
2025-06-09 13:02:57.783 | INFO     | utils.logging:info:191 | === END CONFIGURATION ===
2025-06-09 13:05:09.538 | SUCCESS  | utils.logging:success:216 | SAC training completed: 10000 timesteps
2025-06-09 13:05:09.539 | INFO     | utils.logging:wrapper:392 | Performance - SACAgent.train: 131.770s
2025-06-09 13:05:25.589 | INFO     | utils.logging:info:191 | Applied additional config overrides: {'learning_rate': 0.00010714437525817493, 'batch_size': 256, 'net_dims': [256, 128], 'gamma': 0.9380218065887349, 'soft_update_tau': 0.001102064687015839, 'alpha': 0.2396563324833316}
2025-06-09 13:05:25.589 | INFO     | utils.logging:info:191 | SAC Agent initialized: state_dim=431, action_dim=10, device=cpu
2025-06-09 13:05:25.590 | INFO     | utils.logging:info:191 | Creating SAC agent
2025-06-09 13:05:25.591 | INFO     | utils.logging:info:191 | SACAgent _create_agent: Determined gpu_id_to_pass: -1 from device setting: cpu
2025-06-09 13:05:25.598 | INFO     | utils.logging:info:191 | SAC agent created successfully with params: {'net_dims': [256, 128], 'state_dim': 431, 'action_dim': 10, 'gpu_id': -1, 'args': {'num_envs': 1, 'agent_class': None, 'if_off_policy': True, 'env_class': None, 'env_args': None, 'env_name': None, 'max_step': 12345, 'state_dim': None, 'action_dim': None, 'if_discrete': None, 'gamma': 0.9380218065887349, 'reward_scale': 1, 'net_dims': [128, 128], 'learning_rate': 0.00010714437525817493, 'clip_grad_norm': 3.0, 'state_value_tau': 0, 'soft_update_tau': 0.005, 'continue_train': False, 'batch_size': 256, 'horizon_len': 512, 'buffer_size': 1000000, 'repeat_times': 1.0, 'if_use_per': False, 'lambda_fit_cum_r': 0.0, 'buffer_init_size': 512, 'gpu_id': 0, 'num_workers': 2, 'num_threads': 8, 'random_seed': None, 'learner_gpu_ids': (), 'cwd': None, 'if_remove': True, 'break_step': 100000, 'break_score': inf, 'if_keep_save': True, 'if_over_write': False, 'if_save_buffer': False, 'save_gap': 8, 'eval_times': 3, 'eval_per_step': 20000, 'eval_env_class': None, 'eval_env_args': None, 'eval_record_step': 0, 'target_entropy': -10.0}}
2025-06-09 13:05:25.598 | INFO     | utils.logging:info:191 | Starting SAC training: 10000 timesteps
2025-06-09 13:05:25.604 | INFO     | utils.logging:info:191 | SACAgent train: Set ElegantRL config.gpu_id: -1 from device setting: cpu
2025-06-09 13:05:25.605 | INFO     | utils.logging:info:191 | === TRAINING CONFIGURATION ===
2025-06-09 13:05:25.605 | INFO     | utils.logging:info:191 | total_timesteps = 10000 (main training control - when to stop)
2025-06-09 13:05:25.605 | INFO     | utils.logging:info:191 | break_step = 10000 (ElegantRL equivalent of total_timesteps)
2025-06-09 13:05:25.606 | INFO     | utils.logging:info:191 | max_step = 1869 (per-episode limit - max steps per trading episode)
2025-06-09 13:05:25.606 | INFO     | utils.logging:info:191 | explore_step = 10000 (learning starts - from config)
2025-06-09 13:05:25.606 | INFO     | utils.logging:info:191 | eval_gap = 2500 (evaluation frequency - from eval_freq parameter)
2025-06-09 13:05:25.606 | INFO     | utils.logging:info:191 | horizon_len = 512 (steps per iteration - default)
2025-06-09 13:05:25.607 | INFO     | utils.logging:info:191 | === END CONFIGURATION ===
2025-06-09 13:07:26.550 | SUCCESS  | utils.logging:success:216 | SAC training completed: 10000 timesteps
2025-06-09 13:07:26.550 | INFO     | utils.logging:wrapper:392 | Performance - SACAgent.train: 120.960s
2025-06-09 13:07:49.385 | INFO     | utils.logging:info:191 | Applied additional config overrides: {'learning_rate': 6.997207545114835e-05, 'batch_size': 128, 'net_dims': [128, 64], 'gamma': 0.9988320973000764, 'soft_update_tau': 0.0010011362350858977, 'alpha': 0.10109743594894482}
2025-06-09 13:07:49.386 | INFO     | utils.logging:info:191 | SAC Agent initialized: state_dim=431, action_dim=10, device=cpu
2025-06-09 13:07:49.386 | INFO     | utils.logging:info:191 | Creating SAC agent
2025-06-09 13:07:49.386 | INFO     | utils.logging:info:191 | SACAgent _create_agent: Determined gpu_id_to_pass: -1 from device setting: cpu
2025-06-09 13:07:49.396 | INFO     | utils.logging:info:191 | SAC agent created successfully with params: {'net_dims': [128, 64], 'state_dim': 431, 'action_dim': 10, 'gpu_id': -1, 'args': {'num_envs': 1, 'agent_class': None, 'if_off_policy': True, 'env_class': None, 'env_args': None, 'env_name': None, 'max_step': 12345, 'state_dim': None, 'action_dim': None, 'if_discrete': None, 'gamma': 0.9988320973000764, 'reward_scale': 1, 'net_dims': [128, 128], 'learning_rate': 6.997207545114835e-05, 'clip_grad_norm': 3.0, 'state_value_tau': 0, 'soft_update_tau': 0.005, 'continue_train': False, 'batch_size': 128, 'horizon_len': 512, 'buffer_size': 1000000, 'repeat_times': 1.0, 'if_use_per': False, 'lambda_fit_cum_r': 0.0, 'buffer_init_size': 512, 'gpu_id': 0, 'num_workers': 2, 'num_threads': 8, 'random_seed': None, 'learner_gpu_ids': (), 'cwd': None, 'if_remove': True, 'break_step': 100000, 'break_score': inf, 'if_keep_save': True, 'if_over_write': False, 'if_save_buffer': False, 'save_gap': 8, 'eval_times': 3, 'eval_per_step': 20000, 'eval_env_class': None, 'eval_env_args': None, 'eval_record_step': 0, 'target_entropy': -10.0}}
2025-06-09 13:07:49.396 | INFO     | utils.logging:info:191 | Starting SAC training: 10000 timesteps
2025-06-09 13:07:49.400 | INFO     | utils.logging:info:191 | SACAgent train: Set ElegantRL config.gpu_id: -1 from device setting: cpu
2025-06-09 13:07:49.401 | INFO     | utils.logging:info:191 | === TRAINING CONFIGURATION ===
2025-06-09 13:07:49.401 | INFO     | utils.logging:info:191 | total_timesteps = 10000 (main training control - when to stop)
2025-06-09 13:07:49.401 | INFO     | utils.logging:info:191 | break_step = 10000 (ElegantRL equivalent of total_timesteps)
2025-06-09 13:07:49.402 | INFO     | utils.logging:info:191 | max_step = 1869 (per-episode limit - max steps per trading episode)
2025-06-09 13:07:49.402 | INFO     | utils.logging:info:191 | explore_step = 10000 (learning starts - from config)
2025-06-09 13:07:49.402 | INFO     | utils.logging:info:191 | eval_gap = 2500 (evaluation frequency - from eval_freq parameter)
2025-06-09 13:07:49.402 | INFO     | utils.logging:info:191 | horizon_len = 512 (steps per iteration - default)
2025-06-09 13:07:49.402 | INFO     | utils.logging:info:191 | === END CONFIGURATION ===
2025-06-09 13:10:11.763 | SUCCESS  | utils.logging:success:216 | SAC training completed: 10000 timesteps
2025-06-09 13:10:11.764 | INFO     | utils.logging:wrapper:392 | Performance - SACAgent.train: 142.378s
2025-06-09 13:10:35.441 | INFO     | utils.logging:info:191 | Applied additional config overrides: {'learning_rate': 0.00010369577914160006, 'batch_size': 128, 'net_dims': [128, 64], 'gamma': 0.9593599459911554, 'soft_update_tau': 0.0016425298715648116, 'alpha': 0.14016819028396355}
2025-06-09 13:10:35.441 | INFO     | utils.logging:info:191 | SAC Agent initialized: state_dim=431, action_dim=10, device=cpu
2025-06-09 13:10:35.442 | INFO     | utils.logging:info:191 | Creating SAC agent
2025-06-09 13:10:35.442 | INFO     | utils.logging:info:191 | SACAgent _create_agent: Determined gpu_id_to_pass: -1 from device setting: cpu
2025-06-09 13:10:35.453 | INFO     | utils.logging:info:191 | SAC agent created successfully with params: {'net_dims': [128, 64], 'state_dim': 431, 'action_dim': 10, 'gpu_id': -1, 'args': {'num_envs': 1, 'agent_class': None, 'if_off_policy': True, 'env_class': None, 'env_args': None, 'env_name': None, 'max_step': 12345, 'state_dim': None, 'action_dim': None, 'if_discrete': None, 'gamma': 0.9593599459911554, 'reward_scale': 1, 'net_dims': [128, 128], 'learning_rate': 0.00010369577914160006, 'clip_grad_norm': 3.0, 'state_value_tau': 0, 'soft_update_tau': 0.005, 'continue_train': False, 'batch_size': 128, 'horizon_len': 512, 'buffer_size': 1000000, 'repeat_times': 1.0, 'if_use_per': False, 'lambda_fit_cum_r': 0.0, 'buffer_init_size': 512, 'gpu_id': 0, 'num_workers': 2, 'num_threads': 8, 'random_seed': None, 'learner_gpu_ids': (), 'cwd': None, 'if_remove': True, 'break_step': 100000, 'break_score': inf, 'if_keep_save': True, 'if_over_write': False, 'if_save_buffer': False, 'save_gap': 8, 'eval_times': 3, 'eval_per_step': 20000, 'eval_env_class': None, 'eval_env_args': None, 'eval_record_step': 0, 'target_entropy': -10.0}}
2025-06-09 13:10:35.453 | INFO     | utils.logging:info:191 | Starting SAC training: 10000 timesteps
2025-06-09 13:10:35.457 | INFO     | utils.logging:info:191 | SACAgent train: Set ElegantRL config.gpu_id: -1 from device setting: cpu
2025-06-09 13:10:35.458 | INFO     | utils.logging:info:191 | === TRAINING CONFIGURATION ===
2025-06-09 13:10:35.459 | INFO     | utils.logging:info:191 | total_timesteps = 10000 (main training control - when to stop)
2025-06-09 13:10:35.459 | INFO     | utils.logging:info:191 | break_step = 10000 (ElegantRL equivalent of total_timesteps)
2025-06-09 13:10:35.459 | INFO     | utils.logging:info:191 | max_step = 1869 (per-episode limit - max steps per trading episode)
2025-06-09 13:10:35.459 | INFO     | utils.logging:info:191 | explore_step = 10000 (learning starts - from config)
2025-06-09 13:10:35.460 | INFO     | utils.logging:info:191 | eval_gap = 2500 (evaluation frequency - from eval_freq parameter)
2025-06-09 13:10:35.460 | INFO     | utils.logging:info:191 | horizon_len = 512 (steps per iteration - default)
2025-06-09 13:10:35.460 | INFO     | utils.logging:info:191 | === END CONFIGURATION ===
2025-06-09 13:12:40.078 | SUCCESS  | utils.logging:success:216 | SAC training completed: 10000 timesteps
2025-06-09 13:12:40.078 | INFO     | utils.logging:wrapper:392 | Performance - SACAgent.train: 124.636s
2025-06-09 13:12:54.719 | INFO     | utils.logging:info:191 | Optimization results saved to models/checkpoints/optimization
2025-06-09 13:12:55.115 | WARNING  | utils.logging:warning:196 | Failed to generate some plots: unhashable type: 'list'
2025-06-09 13:12:55.118 | SUCCESS  | utils.logging:success:216 | Optimization completed: 16 trials, best value: 0.0
2025-06-09 13:12:55.120 | SUCCESS  | utils.logging:__exit__:352 | Completed Hyperparameter Optimization in 1549.707s
2025-06-09 13:12:55.120 | INFO     | utils.logging:wrapper:392 | Performance - HyperparameterOptimizer.optimize: 1549.707s
2025-06-09 13:12:55.120 | SUCCESS  | __main__:tune:635 | Hyperparameter tuning completed. Best params: {'learning_rate': 0.002190661086403672, 'batch_size': 256, 'net_dims': [64, 64], 'gamma': 0.9067800051547014, 'soft_update_tau': 0.00108709998601217, 'alpha': 0.2655372017763429}
2025-06-09 13:12:55.121 | INFO     | __main__:tune:644 | Best hyperparameters saved to: models/checkpoints/best_hyperparameters.json
2025-06-09 17:59:28.467 | INFO     | utils.logging:setup_logging:153 | [PID:479897] Logging initialized - Level: INFO, File: logs/trading_agent_main.log, Worker ID: main
2025-06-09 17:59:28.471 | INFO     | utils.logging:setup_logging:157 | [PID:479897] Worker logging setup complete - Worker ID: main
2025-06-09 17:59:28.473 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-09 18:00:24.717 | INFO     | __main__:train:645 | Starting SAC agent training
2025-06-09 18:00:24.720 | INFO     | __main__:train:649 | NumPy error reporting set to 'raise' for all floating point issues.
2025-06-09 18:00:26.115 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-09 18:00:26.118 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-09 18:00:26.150 | INFO     | utils.logging:info:191 | Processing stock data: 23370 records
2025-06-09 18:00:26.343 | INFO     | utils.logging:info:191 | [PID:479897] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 18:00:44.108 | INFO     | utils.logging:info:191 | Added turbulence index: mean=1.1040, max=32.7216
2025-06-09 18:00:44.114 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 18:00:44.155 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.039s
2025-06-09 18:00:44.155 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 18:00:44.156 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.042s
2025-06-09 18:00:44.157 | INFO     | utils.logging:info:191 | [PID:479897] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 18:00:44.157 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 18:00:44.178 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 18:00:44.179 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.022s
2025-06-09 18:00:44.182 | INFO     | utils.logging:info:191 | [PID:479897] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: ['vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 18:00:44.198 | INFO     | utils.logging:info:191 | [PID:479897] DataProcessor.merge_with_vix: Extended tech_indicator_list with VIX features (len: 36): ['vix_regime']
2025-06-09 18:00:44.208 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 23370 records
2025-06-09 18:00:44.209 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 18:00:44.246 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 18:00:44.291 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 23370 records, 45 features
2025-06-09 18:00:44.291 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 18.142s
2025-06-09 18:00:44.292 | INFO     | __main__:train:697 | --- Quick Stats for processed_for_env_df (after DataProcessor.process_stock_data) ---
2025-06-09 18:00:44.292 | INFO     | __main__:train:698 | Shape: (23370, 45)
2025-06-09 18:00:44.293 | INFO     | __main__:train:700 | Columns: ['date', 'open', 'high', 'low', 'close', 'volume', 'dividends', 'stock splits', 'tic', 'sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'rsi_14', 'cci_20', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'turbulence', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 18:00:44.304 | INFO     | __main__:train:703 | NaN counts per column (if any):
Series([], dtype: int64)
2025-06-09 18:00:44.305 | INFO     | __main__:train:704 | Inf counts per column (if any):
Series([], dtype: int64)
2025-06-09 18:00:44.306 | INFO     | __main__:train:706 | Date range in processed_for_env_df ('date'): 2016-01-04 00:00:00 to 2025-04-17 00:00:00
2025-06-09 18:00:44.307 | INFO     | __main__:train:708 | Unique 'tic' in processed_for_env_df: 10
2025-06-09 18:00:44.307 | INFO     | __main__:train:711 | --- End Quick Stats for processed_for_env_df ---
2025-06-09 18:00:44.308 | INFO     | __main__:train:722 | Assuming VIX indicators are pre-merged and correctly named in the loaded data.
2025-06-09 18:00:44.318 | INFO     | __main__:train:741 | Training data: 18690 records, Validation data: 4680 records
2025-06-09 18:00:44.319 | INFO     | __main__:train:788 | Transforming data format for FinRL environment compatibility
2025-06-09 18:00:44.347 | INFO     | __main__:train:792 | Train_df columns before deduplication (showing only duplicated ones): []
2025-06-09 18:00:44.350 | INFO     | __main__:train:794 | Train_df columns after deduplication: 45
2025-06-09 18:00:44.351 | INFO     | __main__:train:796 | Val_df columns before deduplication (showing only duplicated ones): []
2025-06-09 18:00:44.352 | INFO     | __main__:train:798 | Val_df columns after deduplication: 45
2025-06-09 18:00:44.352 | INFO     | __main__:train:800 | Transformed training data: 18690 records, Validation data: 4680 records
2025-06-09 18:00:44.357 | INFO     | __main__:train:819 | Skipping set_index('date') as 'day' index is already set by prepare_finrl_data.
2025-06-09 18:00:44.357 | INFO     | __main__:train:822 | Applying NaN fill and type check for technical indicators in training data.
2025-06-09 18:00:44.377 | INFO     | __main__:train:852 | Applying NaN fill and type check for technical indicators in validation data.
2025-06-09 18:00:44.395 | INFO     | __main__:train:927 | About to create AsymmetricTradingEnv with config: stock_dim=10, action_space=10, df_shape=(18690, 45)
2025-06-09 18:00:44.408 | INFO     | __main__:train:929 | AsymmetricTradingEnv created successfully
2025-06-09 18:00:44.409 | INFO     | __main__:train:932 | About to reset training environment
2025-06-09 18:00:44.417 | INFO     | __main__:train:934 | Training environment reset successfully
2025-06-09 18:00:44.418 | INFO     | __main__:train:935 | About to get state from training environment
2025-06-09 18:00:44.421 | INFO     | __main__:train:938 | Got actual state from training environment: dim=431
2025-06-09 18:00:44.421 | INFO     | __main__:train:943 | Train_env actual_state_dim after init: 431
2025-06-09 18:00:44.422 | INFO     | __main__:train:957 | About to create validation AsymmetricTradingEnv with config: stock_dim=10, action_space=10
2025-06-09 18:00:44.428 | INFO     | __main__:train:959 | Validation AsymmetricTradingEnv created successfully
2025-06-09 18:00:44.429 | INFO     | __main__:train:962 | About to reset validation environment
2025-06-09 18:00:44.434 | INFO     | __main__:train:964 | Validation environment reset successfully
2025-06-09 18:00:44.434 | INFO     | __main__:train:965 | About to get state from validation environment
2025-06-09 18:00:44.436 | INFO     | __main__:train:968 | Got actual state from validation environment: dim=431
2025-06-09 18:00:44.436 | INFO     | __main__:train:971 | Val_env actual_state_dim after init: 431
2025-06-09 18:00:44.437 | INFO     | __main__:train:978 | Environment created: state_dim=431, action_dim=10
2025-06-09 18:00:44.437 | INFO     | __main__:train:995 | Creating SAC agent with state_dim=431, action_dim=10
2025-06-09 18:00:44.438 | ERROR    | __main__:train:1024 | SAC agent creation failed: 'SACConfig' object has no attribute 'target_step'
2025-06-09 18:00:44.438 | ERROR    | __main__:train:1072 | Training process failed: cannot access local variable 'agent_config' where it is not associated with a value
Traceback (most recent call last):

  File "/app/workspaces/finrl-bot/sonet/main.py", line 1005, in train
    'target_step': best_params.get('target_step', settings.sac.target_step),
                   │           │                  │        └ SACConfig(actor_hidden_sizes=[128, 128], critic_hidden_sizes=[128, 128], net_dims=[128, 128], learning_rate=0.0003, batch_siz...
                   │           │                  └ Settings(environment='development', debug=False, project_root=PosixPath('/app/workspaces/finrl-bot/sonet'), logging=LoggingCo...
                   │           └ <method 'get' of 'dict' objects>
                   └ {}

  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/pydantic/main.py", line 991, in __getattr__
    raise AttributeError(f'{type(self).__name__!r} object has no attribute {item!r}')

AttributeError: 'SACConfig' object has no attribute 'target_step'


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "/app/workspaces/finrl-bot/sonet/main.py", line 1140, in <module>
    cli()
    └ <Group cli>

  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/click/core.py", line 1442, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function Command.main at 0x712cecf52700>
           └ <Group cli>
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/click/core.py", line 1363, in main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x712cdebc3d50>
         │    └ <function Group.invoke at 0x712cecf53600>
         └ <Group cli>
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/click/core.py", line 1830, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x712cdebc3fd0>
           │               │       │       └ <function Command.invoke at 0x712cecf523e0>
           │               │       └ <Command train>
           │               └ <click.core.Context object at 0x712cdebc3fd0>
           └ <function Group.invoke.<locals>._process_result at 0x712cdf7b3240>
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/click/core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'config_override': None, 'resume': False, 'use_best_params': False}
           │   │      │    │           └ <click.core.Context object at 0x712cdebc3fd0>
           │   │      │    └ <function train at 0x712cdeb998a0>
           │   │      └ <Command train>
           │   └ <function Context.invoke at 0x712cecf51620>
           └ <click.core.Context object at 0x712cdebc3fd0>
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/click/core.py", line 794, in invoke
    return callback(*args, **kwargs)
           │         │       └ {'config_override': None, 'resume': False, 'use_best_params': False}
           │         └ ()
           └ <function train at 0x712cdeb998a0>
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/click/decorators.py", line 34, in new_func
    return f(get_current_context(), *args, **kwargs)
           │ │                       │       └ {'config_override': None, 'resume': False, 'use_best_params': False}
           │ │                       └ ()
           │ └ <function get_current_context at 0x712cecf26660>
           └ <function train at 0x712cdeb99800>

> File "/app/workspaces/finrl-bot/sonet/main.py", line 1025, in train
    logger.error(f"Agent config: {agent_config}")
    │      └ <function Logger.error at 0x712ce115f2e0>
    └ <loguru.logger handlers=[(id=1, level=20, sink=<stdout>), (id=2, level=20, sink='logs/trading_agent_main.log')]>

UnboundLocalError: cannot access local variable 'agent_config' where it is not associated with a value
2025-06-09 18:15:16.421 | INFO     | utils.logging:setup_logging:153 | [PID:480919] Logging initialized - Level: INFO, File: logs/trading_agent_main.log, Worker ID: main
2025-06-09 18:15:16.422 | INFO     | utils.logging:setup_logging:157 | [PID:480919] Worker logging setup complete - Worker ID: main
2025-06-09 18:15:16.422 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-09 18:15:24.478 | INFO     | __main__:train:693 | Starting SAC agent training
2025-06-09 18:15:24.711 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-09 18:15:24.711 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-09 18:15:24.712 | INFO     | utils.logging:info:191 | Processing stock data: 23370 records
2025-06-09 18:15:24.738 | INFO     | utils.logging:info:191 | [PID:480919] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 18:15:28.836 | INFO     | utils.logging:info:191 | Added turbulence index: mean=1.1040, max=32.7216
2025-06-09 18:15:28.843 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 18:15:28.886 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.042s
2025-06-09 18:15:28.886 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 18:15:28.887 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.044s
2025-06-09 18:15:28.888 | INFO     | utils.logging:info:191 | [PID:480919] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 18:15:28.889 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 18:15:28.907 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 18:15:28.907 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.019s
2025-06-09 18:15:28.912 | INFO     | utils.logging:info:191 | [PID:480919] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: ['vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 18:15:28.927 | INFO     | utils.logging:info:191 | [PID:480919] DataProcessor.merge_with_vix: Extended tech_indicator_list with VIX features (len: 36): ['vix_regime']
2025-06-09 18:15:28.938 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 23370 records
2025-06-09 18:15:28.938 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 18:15:28.977 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 18:15:29.029 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 23370 records, 45 features
2025-06-09 18:15:29.030 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 4.317s
2025-06-09 18:15:29.031 | INFO     | __main__:train:747 | Assuming VIX indicators are pre-merged and correctly named in the loaded data.
2025-06-09 18:15:29.045 | INFO     | __main__:train:775 | Final training data: 14090 records, Validation data: 3530 records
2025-06-09 18:15:29.045 | INFO     | __main__:train:815 | Transforming data format for FinRL environment compatibility
2025-06-09 18:15:29.071 | INFO     | __main__:train:819 | Transformed training data: 14090 records, Validation data: 3530 records
2025-06-09 18:15:29.093 | ERROR    | __main__:train:888 | Environment creation failed: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.
2025-06-09 18:17:00.318 | INFO     | utils.logging:setup_logging:153 | [PID:481042] Logging initialized - Level: INFO, File: logs/trading_agent_main.log, Worker ID: main
2025-06-09 18:17:00.318 | INFO     | utils.logging:setup_logging:157 | [PID:481042] Worker logging setup complete - Worker ID: main
2025-06-09 18:17:00.319 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-09 18:17:00.947 | INFO     | __main__:process_data:137 | Starting data processing
2025-06-09 18:17:00.947 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-09 18:17:00.947 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-09 18:17:00.947 | INFO     | __main__:process_data:157 | Fetching and processing data for 10 symbols...
2025-06-09 18:17:00.947 | INFO     | __main__:process_data:159 | Processing data for AAPL
2025-06-09 18:17:00.948 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.000s
2025-06-09 18:17:00.949 | INFO     | utils.logging:info:191 | Fetching data for AAPL from 2016-01-01 to 2025-04-19
2025-06-09 18:17:01.643 | INFO     | utils.logging:log_data_quality:270 | Data Quality - AAPL: 2337 points, 91.33% missing, 
2025-06-09 18:17:01.685 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.cache_data: 0.042s
2025-06-09 18:17:01.686 | SUCCESS  | utils.logging:success:216 | Successfully fetched 2337 records for AAPL
2025-06-09 18:17:01.687 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.739s
2025-06-09 18:17:01.688 | INFO     | utils.logging:info:191 | Processing stock data: 2337 records
2025-06-09 18:17:01.688 | INFO     | utils.logging:info:191 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-09 18:17:01.698 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 18:17:01.759 | WARNING  | utils.logging:warning:196 | Need at least 2 tickers for turbulence calculation (after potential aggregation)
2025-06-09 18:17:01.762 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 18:17:01.763 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.000s
2025-06-09 18:17:02.005 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.cache_data: 0.042s
2025-06-09 18:17:02.005 | SUCCESS  | utils.logging:success:216 | Successfully fetched 2336 VIX records
2025-06-09 18:17:02.006 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.245s
2025-06-09 18:17:02.006 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 18:17:02.007 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 18:17:02.020 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 18:17:02.020 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.014s
2025-06-09 18:17:02.026 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.merge_with_vix: Extended tech_indicator_list with VIX features (len: 36): ['vix_regime']
2025-06-09 18:17:02.030 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 2337 records
2025-06-09 18:17:02.031 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 18:17:02.039 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 18:17:02.050 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 2337 records, 45 features
2025-06-09 18:17:02.050 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 0.363s
2025-06-09 18:17:02.051 | SUCCESS  | __main__:process_data:180 | Successfully processed data for AAPL
2025-06-09 18:17:02.051 | INFO     | __main__:process_data:159 | Processing data for MSFT
2025-06-09 18:17:02.051 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.000s
2025-06-09 18:17:02.052 | INFO     | utils.logging:info:191 | Fetching data for MSFT from 2016-01-01 to 2025-04-19
2025-06-09 18:17:02.262 | INFO     | utils.logging:log_data_quality:270 | Data Quality - MSFT: 2337 points, 92.04% missing, 
2025-06-09 18:17:02.291 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.cache_data: 0.028s
2025-06-09 18:17:02.292 | SUCCESS  | utils.logging:success:216 | Successfully fetched 2337 records for MSFT
2025-06-09 18:17:02.292 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.241s
2025-06-09 18:17:02.293 | INFO     | utils.logging:info:191 | Processing stock data: 2337 records
2025-06-09 18:17:02.294 | INFO     | utils.logging:info:191 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-09 18:17:02.301 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-09 18:17:02.356 | WARNING  | utils.logging:warning:196 | Need at least 2 tickers for turbulence calculation (after potential aggregation)
2025-06-09 18:17:02.358 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 18:17:02.394 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.036s
2025-06-09 18:17:02.394 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 18:17:02.395 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.037s
2025-06-09 18:17:02.396 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-09 18:17:02.396 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 18:17:02.412 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 18:17:02.412 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.016s
2025-06-09 18:17:02.423 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 2337 records
2025-06-09 18:17:02.423 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 18:17:02.434 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 18:17:02.451 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 2337 records, 45 features
2025-06-09 18:17:02.452 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 0.159s
2025-06-09 18:17:02.452 | SUCCESS  | __main__:process_data:180 | Successfully processed data for MSFT
2025-06-09 18:17:02.453 | INFO     | __main__:process_data:159 | Processing data for GOOGL
2025-06-09 18:17:02.453 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.000s
2025-06-09 18:17:02.453 | INFO     | utils.logging:info:191 | Fetching data for GOOGL from 2016-01-01 to 2025-04-19
2025-06-09 18:17:02.663 | INFO     | utils.logging:log_data_quality:270 | Data Quality - GOOGL: 2337 points, 90.82% missing, 
2025-06-09 18:17:02.694 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.cache_data: 0.031s
2025-06-09 18:17:02.695 | SUCCESS  | utils.logging:success:216 | Successfully fetched 2337 records for GOOGL
2025-06-09 18:17:02.695 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.243s
2025-06-09 18:17:02.696 | INFO     | utils.logging:info:191 | Processing stock data: 2337 records
2025-06-09 18:17:02.697 | INFO     | utils.logging:info:191 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-09 18:17:02.706 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-09 18:17:02.761 | WARNING  | utils.logging:warning:196 | Need at least 2 tickers for turbulence calculation (after potential aggregation)
2025-06-09 18:17:02.762 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 18:17:02.790 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.026s
2025-06-09 18:17:02.790 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 18:17:02.791 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.028s
2025-06-09 18:17:02.791 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-09 18:17:02.792 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 18:17:02.806 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 18:17:02.806 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.015s
2025-06-09 18:17:02.815 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 2337 records
2025-06-09 18:17:02.815 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 18:17:02.823 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 18:17:02.836 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 2337 records, 45 features
2025-06-09 18:17:02.837 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 0.141s
2025-06-09 18:17:02.837 | SUCCESS  | __main__:process_data:180 | Successfully processed data for GOOGL
2025-06-09 18:17:02.837 | INFO     | __main__:process_data:159 | Processing data for AMZN
2025-06-09 18:17:02.838 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.000s
2025-06-09 18:17:02.839 | INFO     | utils.logging:info:191 | Fetching data for AMZN from 2016-01-01 to 2025-04-19
2025-06-09 18:17:03.060 | INFO     | utils.logging:log_data_quality:270 | Data Quality - AMZN: 2337 points, 91.40% missing, 
2025-06-09 18:17:03.091 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.cache_data: 0.031s
2025-06-09 18:17:03.091 | SUCCESS  | utils.logging:success:216 | Successfully fetched 2337 records for AMZN
2025-06-09 18:17:03.092 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.254s
2025-06-09 18:17:03.093 | INFO     | utils.logging:info:191 | Processing stock data: 2337 records
2025-06-09 18:17:03.093 | INFO     | utils.logging:info:191 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-09 18:17:03.102 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-09 18:17:03.181 | WARNING  | utils.logging:warning:196 | Need at least 2 tickers for turbulence calculation (after potential aggregation)
2025-06-09 18:17:03.182 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 18:17:03.200 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.018s
2025-06-09 18:17:03.200 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 18:17:03.201 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.019s
2025-06-09 18:17:03.201 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-09 18:17:03.201 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 18:17:03.211 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 18:17:03.211 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.010s
2025-06-09 18:17:03.217 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 2337 records
2025-06-09 18:17:03.217 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 18:17:03.223 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 18:17:03.234 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 2337 records, 45 features
2025-06-09 18:17:03.235 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 0.142s
2025-06-09 18:17:03.235 | SUCCESS  | __main__:process_data:180 | Successfully processed data for AMZN
2025-06-09 18:17:03.235 | INFO     | __main__:process_data:159 | Processing data for META
2025-06-09 18:17:03.235 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.000s
2025-06-09 18:17:03.236 | INFO     | utils.logging:info:191 | Fetching data for META from 2016-01-01 to 2025-04-19
2025-06-09 18:17:03.395 | INFO     | utils.logging:log_data_quality:270 | Data Quality - META: 2337 points, 91.91% missing, 
2025-06-09 18:17:03.429 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.cache_data: 0.034s
2025-06-09 18:17:03.430 | SUCCESS  | utils.logging:success:216 | Successfully fetched 2337 records for META
2025-06-09 18:17:03.431 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.195s
2025-06-09 18:17:03.431 | INFO     | utils.logging:info:191 | Processing stock data: 2337 records
2025-06-09 18:17:03.432 | INFO     | utils.logging:info:191 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-09 18:17:03.443 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-09 18:17:03.495 | WARNING  | utils.logging:warning:196 | Need at least 2 tickers for turbulence calculation (after potential aggregation)
2025-06-09 18:17:03.497 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 18:17:03.521 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.024s
2025-06-09 18:17:03.521 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 18:17:03.521 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.025s
2025-06-09 18:17:03.521 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-09 18:17:03.522 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 18:17:03.535 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 18:17:03.535 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.014s
2025-06-09 18:17:03.543 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 2337 records
2025-06-09 18:17:03.543 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 18:17:03.550 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 18:17:03.560 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 2337 records, 45 features
2025-06-09 18:17:03.561 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 0.129s
2025-06-09 18:17:03.561 | SUCCESS  | __main__:process_data:180 | Successfully processed data for META
2025-06-09 18:17:03.562 | INFO     | __main__:process_data:159 | Processing data for NVDA
2025-06-09 18:17:03.562 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.000s
2025-06-09 18:17:03.562 | INFO     | utils.logging:info:191 | Fetching data for NVDA from 2016-01-01 to 2025-04-19
2025-06-09 18:17:03.788 | INFO     | utils.logging:log_data_quality:270 | Data Quality - NVDA: 2337 points, 92.49% missing, 
2025-06-09 18:17:03.826 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.cache_data: 0.038s
2025-06-09 18:17:03.827 | SUCCESS  | utils.logging:success:216 | Successfully fetched 2337 records for NVDA
2025-06-09 18:17:03.828 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.266s
2025-06-09 18:17:03.829 | INFO     | utils.logging:info:191 | Processing stock data: 2337 records
2025-06-09 18:17:03.830 | INFO     | utils.logging:info:191 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-09 18:17:03.840 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-09 18:17:03.896 | WARNING  | utils.logging:warning:196 | Need at least 2 tickers for turbulence calculation (after potential aggregation)
2025-06-09 18:17:03.898 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 18:17:03.925 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.027s
2025-06-09 18:17:03.925 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 18:17:03.926 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.028s
2025-06-09 18:17:03.926 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-09 18:17:03.927 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 18:17:03.941 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 18:17:03.942 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.016s
2025-06-09 18:17:03.953 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 2337 records
2025-06-09 18:17:03.954 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 18:17:03.964 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 18:17:03.978 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 2337 records, 45 features
2025-06-09 18:17:03.978 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 0.149s
2025-06-09 18:17:03.979 | SUCCESS  | __main__:process_data:180 | Successfully processed data for NVDA
2025-06-09 18:17:03.979 | INFO     | __main__:process_data:159 | Processing data for TSLA
2025-06-09 18:17:03.980 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.000s
2025-06-09 18:17:03.980 | INFO     | utils.logging:info:191 | Fetching data for TSLA from 2016-01-01 to 2025-04-19
2025-06-09 18:17:04.167 | INFO     | utils.logging:log_data_quality:270 | Data Quality - TSLA: 2337 points, 91.78% missing, 
2025-06-09 18:17:04.193 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.cache_data: 0.025s
2025-06-09 18:17:04.193 | SUCCESS  | utils.logging:success:216 | Successfully fetched 2337 records for TSLA
2025-06-09 18:17:04.194 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.214s
2025-06-09 18:17:04.195 | INFO     | utils.logging:info:191 | Processing stock data: 2337 records
2025-06-09 18:17:04.195 | INFO     | utils.logging:info:191 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-09 18:17:04.203 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-09 18:17:04.245 | WARNING  | utils.logging:warning:196 | Need at least 2 tickers for turbulence calculation (after potential aggregation)
2025-06-09 18:17:04.246 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 18:17:04.267 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.020s
2025-06-09 18:17:04.267 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 18:17:04.267 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.021s
2025-06-09 18:17:04.267 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-09 18:17:04.267 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 18:17:04.277 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 18:17:04.277 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.010s
2025-06-09 18:17:04.284 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 2337 records
2025-06-09 18:17:04.284 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 18:17:04.291 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 18:17:04.301 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 2337 records, 45 features
2025-06-09 18:17:04.301 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 0.107s
2025-06-09 18:17:04.302 | SUCCESS  | __main__:process_data:180 | Successfully processed data for TSLA
2025-06-09 18:17:04.302 | INFO     | __main__:process_data:159 | Processing data for AVGO
2025-06-09 18:17:04.302 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.000s
2025-06-09 18:17:04.302 | INFO     | utils.logging:info:191 | Fetching data for AVGO from 2016-01-01 to 2025-04-19
2025-06-09 18:17:04.516 | INFO     | utils.logging:log_data_quality:270 | Data Quality - AVGO: 2337 points, 93.07% missing, 
2025-06-09 18:17:04.548 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.cache_data: 0.032s
2025-06-09 18:17:04.548 | SUCCESS  | utils.logging:success:216 | Successfully fetched 2337 records for AVGO
2025-06-09 18:17:04.549 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.247s
2025-06-09 18:17:04.550 | INFO     | utils.logging:info:191 | Processing stock data: 2337 records
2025-06-09 18:17:04.551 | INFO     | utils.logging:info:191 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-09 18:17:04.559 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-09 18:17:04.604 | WARNING  | utils.logging:warning:196 | Need at least 2 tickers for turbulence calculation (after potential aggregation)
2025-06-09 18:17:04.606 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 18:17:04.627 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.021s
2025-06-09 18:17:04.627 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 18:17:04.627 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.022s
2025-06-09 18:17:04.628 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-09 18:17:04.628 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 18:17:04.636 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 18:17:04.637 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.009s
2025-06-09 18:17:04.642 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 2337 records
2025-06-09 18:17:04.643 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 18:17:04.649 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 18:17:04.658 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 2337 records, 45 features
2025-06-09 18:17:04.659 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 0.109s
2025-06-09 18:17:04.659 | SUCCESS  | __main__:process_data:180 | Successfully processed data for AVGO
2025-06-09 18:17:04.659 | INFO     | __main__:process_data:159 | Processing data for ADBE
2025-06-09 18:17:04.659 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.000s
2025-06-09 18:17:04.660 | INFO     | utils.logging:info:191 | Fetching data for ADBE from 2016-01-01 to 2025-04-19
2025-06-09 18:17:04.883 | INFO     | utils.logging:log_data_quality:270 | Data Quality - ADBE: 2337 points, 90.37% missing, 
2025-06-09 18:17:04.912 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.cache_data: 0.029s
2025-06-09 18:17:04.912 | SUCCESS  | utils.logging:success:216 | Successfully fetched 2337 records for ADBE
2025-06-09 18:17:04.913 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.254s
2025-06-09 18:17:04.914 | INFO     | utils.logging:info:191 | Processing stock data: 2337 records
2025-06-09 18:17:04.915 | INFO     | utils.logging:info:191 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-09 18:17:04.924 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-09 18:17:04.977 | WARNING  | utils.logging:warning:196 | Need at least 2 tickers for turbulence calculation (after potential aggregation)
2025-06-09 18:17:04.979 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 18:17:05.005 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.026s
2025-06-09 18:17:05.005 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 18:17:05.006 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.027s
2025-06-09 18:17:05.006 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-09 18:17:05.007 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 18:17:05.028 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 18:17:05.029 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.023s
2025-06-09 18:17:05.040 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 2337 records
2025-06-09 18:17:05.041 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 18:17:05.052 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 18:17:05.070 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 2337 records, 45 features
2025-06-09 18:17:05.071 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 0.157s
2025-06-09 18:17:05.071 | SUCCESS  | __main__:process_data:180 | Successfully processed data for ADBE
2025-06-09 18:17:05.071 | INFO     | __main__:process_data:159 | Processing data for ASML
2025-06-09 18:17:05.072 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.000s
2025-06-09 18:17:05.072 | INFO     | utils.logging:info:191 | Fetching data for ASML from 2016-01-01 to 2025-04-19
2025-06-09 18:17:05.255 | INFO     | utils.logging:log_data_quality:270 | Data Quality - ASML: 2337 points, 92.42% missing, 
2025-06-09 18:17:05.296 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.cache_data: 0.041s
2025-06-09 18:17:05.297 | SUCCESS  | utils.logging:success:216 | Successfully fetched 2337 records for ASML
2025-06-09 18:17:05.298 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.226s
2025-06-09 18:17:05.299 | INFO     | utils.logging:info:191 | Processing stock data: 2337 records
2025-06-09 18:17:05.300 | INFO     | utils.logging:info:191 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-09 18:17:05.312 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-09 18:17:05.380 | WARNING  | utils.logging:warning:196 | Need at least 2 tickers for turbulence calculation (after potential aggregation)
2025-06-09 18:17:05.381 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 18:17:05.410 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.028s
2025-06-09 18:17:05.410 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 18:17:05.410 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.029s
2025-06-09 18:17:05.411 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-09 18:17:05.411 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 18:17:05.425 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 18:17:05.425 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.014s
2025-06-09 18:17:05.436 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 2337 records
2025-06-09 18:17:05.436 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 18:17:05.447 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 18:17:05.462 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 2337 records, 45 features
2025-06-09 18:17:05.463 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 0.164s
2025-06-09 18:17:05.463 | SUCCESS  | __main__:process_data:180 | Successfully processed data for ASML
2025-06-09 18:17:05.507 | INFO     | __main__:process_data:200 | Combined data for all symbols: 23370 records
2025-06-09 18:17:05.508 | INFO     | __main__:process_data:203 | Fetching and processing VIX data
2025-06-09 18:17:05.508 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-01 to 2025-04-19
2025-06-09 18:17:05.508 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.000s
2025-06-09 18:17:05.705 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.cache_data: 0.028s
2025-06-09 18:17:05.705 | SUCCESS  | utils.logging:success:216 | Successfully fetched 2337 VIX records
2025-06-09 18:17:05.706 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.198s
2025-06-09 18:17:05.706 | INFO     | utils.logging:info:191 | [PID:481042] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-09 18:17:05.707 | INFO     | utils.logging:info:191 | Processing VIX data: 2337 records
2025-06-09 18:17:05.722 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2337 records
2025-06-09 18:17:05.723 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.017s
2025-06-09 18:17:05.723 | SUCCESS  | __main__:process_data:215 | Successfully processed VIX data
2025-06-09 18:17:05.723 | INFO     | __main__:process_data:221 | Merging stock data with VIX data
2025-06-09 18:17:05.725 | INFO     | utils.logging:info:191 | [PID:481042] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: ['vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 18:17:05.744 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 23370 records
2025-06-09 18:17:05.745 | SUCCESS  | __main__:process_data:226 | Successfully merged data: 23370 records
2025-06-09 18:17:05.745 | INFO     | __main__:process_data:257 | Saving processed data to data/processed/processed_data.csv
2025-06-09 18:17:07.424 | SUCCESS  | __main__:process_data:259 | Processed data saved successfully
2025-06-09 18:17:07.425 | INFO     | __main__:process_data:262 | Final processed data summary:
2025-06-09 18:17:07.425 | INFO     | __main__:process_data:263 |   - Total records: 23370
2025-06-09 18:17:07.427 | INFO     | __main__:process_data:264 |   - Unique symbols: 10
2025-06-09 18:17:07.428 | INFO     | __main__:process_data:265 |   - Date range: 2016-01-04 00:00:00 to 2025-04-17 00:00:00
2025-06-09 18:17:07.428 | INFO     | __main__:process_data:266 |   - Columns: 45
2025-06-09 18:17:07.429 | INFO     | __main__:process_data:267 |   - Sample symbols: ['AAPL', 'ADBE', 'AMZN', 'ASML', 'AVGO']
2025-06-09 18:17:07.430 | SUCCESS  | __main__:process_data:269 | Data processing completed successfully
2025-06-09 18:17:15.051 | INFO     | utils.logging:setup_logging:153 | [PID:481070] Logging initialized - Level: INFO, File: logs/trading_agent_main.log, Worker ID: main
2025-06-09 18:17:15.051 | INFO     | utils.logging:setup_logging:157 | [PID:481070] Worker logging setup complete - Worker ID: main
2025-06-09 18:17:15.052 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-09 18:17:21.172 | INFO     | __main__:train:693 | Starting SAC agent training
2025-06-09 18:17:21.428 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-09 18:17:21.429 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-09 18:17:21.432 | INFO     | utils.logging:info:191 | Processing stock data: 23370 records
2025-06-09 18:17:21.464 | INFO     | utils.logging:info:191 | [PID:481070] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 18:17:25.260 | INFO     | utils.logging:info:191 | Added turbulence index: mean=1.1040, max=32.7217
2025-06-09 18:17:25.265 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 18:17:25.301 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.035s
2025-06-09 18:17:25.302 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 18:17:25.302 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.037s
2025-06-09 18:17:25.303 | INFO     | utils.logging:info:191 | [PID:481070] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 18:17:25.303 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 18:17:25.329 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 18:17:25.330 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.027s
2025-06-09 18:17:25.334 | INFO     | utils.logging:info:191 | [PID:481070] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: ['vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 18:17:25.354 | INFO     | utils.logging:info:191 | [PID:481070] DataProcessor.merge_with_vix: Extended tech_indicator_list with VIX features (len: 36): ['vix_regime']
2025-06-09 18:17:25.366 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 23370 records
2025-06-09 18:17:25.367 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 18:17:25.411 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 18:17:25.460 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 23370 records, 45 features
2025-06-09 18:17:25.461 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 4.029s
2025-06-09 18:17:25.461 | INFO     | __main__:train:747 | Assuming VIX indicators are pre-merged and correctly named in the loaded data.
2025-06-09 18:17:25.475 | INFO     | __main__:train:775 | Final training data: 14090 records, Validation data: 3530 records
2025-06-09 18:17:25.475 | INFO     | __main__:train:815 | Transforming data format for FinRL environment compatibility
2025-06-09 18:17:25.506 | INFO     | __main__:train:819 | Transformed training data: 14090 records, Validation data: 3530 records
2025-06-09 18:17:25.514 | ERROR    | __main__:train:888 | Environment creation failed: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.
2025-06-09 18:23:56.338 | INFO     | utils.logging:setup_logging:153 | [PID:481392] Logging initialized - Level: INFO, File: logs/trading_agent_main.log, Worker ID: main
2025-06-09 18:23:56.342 | INFO     | utils.logging:setup_logging:157 | [PID:481392] Worker logging setup complete - Worker ID: main
2025-06-09 18:23:56.345 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-09 18:24:30.696 | INFO     | __main__:train:693 | Starting SAC agent training
2025-06-09 18:24:31.972 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-09 18:24:31.974 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-09 18:24:32.000 | INFO     | utils.logging:info:191 | Processing stock data: 23370 records
2025-06-09 18:24:32.166 | INFO     | utils.logging:info:191 | [PID:481392] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 18:24:39.682 | INFO     | utils.logging:info:191 | Added turbulence index: mean=1.1040, max=32.7217
2025-06-09 18:24:39.687 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 18:24:39.713 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.026s
2025-06-09 18:24:39.714 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 18:24:39.714 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.028s
2025-06-09 18:24:39.714 | INFO     | utils.logging:info:191 | [PID:481392] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 18:24:39.714 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 18:24:39.729 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 18:24:39.729 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.015s
2025-06-09 18:24:39.732 | INFO     | utils.logging:info:191 | [PID:481392] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: ['vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 18:24:39.743 | INFO     | utils.logging:info:191 | [PID:481392] DataProcessor.merge_with_vix: Extended tech_indicator_list with VIX features (len: 36): ['vix_regime']
2025-06-09 18:24:39.752 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 23370 records
2025-06-09 18:24:39.752 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 18:24:39.787 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 18:24:39.832 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 23370 records, 45 features
2025-06-09 18:24:39.832 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 7.832s
2025-06-09 18:24:39.833 | INFO     | __main__:train:747 | Assuming VIX indicators are pre-merged and correctly named in the loaded data.
2025-06-09 18:24:39.851 | INFO     | __main__:train:775 | Final training data: 14090 records, Validation data: 3530 records
2025-06-09 18:24:39.851 | INFO     | __main__:train:815 | Transforming data format for FinRL environment compatibility
2025-06-09 18:24:39.877 | INFO     | __main__:train:819 | Transformed training data: 14090 records, Validation data: 3530 records
2025-06-09 18:24:39.881 | ERROR    | __main__:train:888 | Environment creation failed: 'NoneType' object is not subscriptable
2025-06-09 18:37:20.680 | INFO     | utils.logging:setup_logging:153 | [PID:481894] Logging initialized - Level: INFO, File: logs/trading_agent_main.log, Worker ID: main
2025-06-09 18:37:20.681 | INFO     | utils.logging:setup_logging:157 | [PID:481894] Worker logging setup complete - Worker ID: main
2025-06-09 18:37:20.681 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-09 18:37:25.482 | INFO     | __main__:train:693 | Starting SAC agent training
2025-06-09 18:37:25.668 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-09 18:37:25.668 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-09 18:37:25.670 | INFO     | utils.logging:info:191 | Processing stock data: 23370 records
2025-06-09 18:37:25.695 | INFO     | utils.logging:info:191 | [PID:481894] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 18:37:29.223 | INFO     | utils.logging:info:191 | Added turbulence index: mean=1.1040, max=32.7217
2025-06-09 18:37:29.229 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 18:37:29.263 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.033s
2025-06-09 18:37:29.263 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 18:37:29.264 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.035s
2025-06-09 18:37:29.265 | INFO     | utils.logging:info:191 | [PID:481894] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 18:37:29.265 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 18:37:29.289 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 18:37:29.290 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.025s
2025-06-09 18:37:29.295 | INFO     | utils.logging:info:191 | [PID:481894] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: ['vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 18:37:29.313 | INFO     | utils.logging:info:191 | [PID:481894] DataProcessor.merge_with_vix: Extended tech_indicator_list with VIX features (len: 36): ['vix_regime']
2025-06-09 18:37:29.327 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 23370 records
2025-06-09 18:37:29.328 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 18:37:29.365 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 18:37:29.412 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 23370 records, 45 features
2025-06-09 18:37:29.413 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 3.743s
2025-06-09 18:37:29.413 | INFO     | __main__:train:747 | Assuming VIX indicators are pre-merged and correctly named in the loaded data.
2025-06-09 18:37:29.429 | INFO     | __main__:train:775 | Final training data: 14090 records, Validation data: 3530 records
2025-06-09 18:37:29.429 | INFO     | __main__:train:815 | Transforming data format for FinRL environment compatibility
2025-06-09 18:37:29.460 | INFO     | __main__:train:819 | Transformed training data: 14090 records, Validation data: 3530 records
2025-06-09 18:37:29.472 | ERROR    | __main__:train:888 | Environment creation failed: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.
2025-06-09 18:56:14.264 | INFO     | utils.logging:setup_logging:153 | [PID:483155] Logging initialized - Level: INFO, File: logs/trading_agent_main.log, Worker ID: main
2025-06-09 18:56:14.265 | INFO     | utils.logging:setup_logging:157 | [PID:483155] Worker logging setup complete - Worker ID: main
2025-06-09 18:56:14.265 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-09 18:56:18.702 | INFO     | __main__:train:693 | Starting SAC agent training
2025-06-09 18:56:18.894 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-09 18:56:18.895 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-09 18:56:18.898 | INFO     | utils.logging:info:191 | Processing stock data: 23370 records
2025-06-09 18:56:18.921 | INFO     | utils.logging:info:191 | [PID:483155] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 18:56:21.873 | INFO     | utils.logging:info:191 | Added turbulence index: mean=1.1040, max=32.7217
2025-06-09 18:56:21.878 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 18:56:21.910 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.032s
2025-06-09 18:56:21.910 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 18:56:21.911 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.034s
2025-06-09 18:56:21.912 | INFO     | utils.logging:info:191 | [PID:483155] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 18:56:21.912 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 18:56:21.928 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 18:56:21.928 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.016s
2025-06-09 18:56:21.930 | INFO     | utils.logging:info:191 | [PID:483155] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: ['vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 18:56:21.943 | INFO     | utils.logging:info:191 | [PID:483155] DataProcessor.merge_with_vix: Extended tech_indicator_list with VIX features (len: 36): ['vix_regime']
2025-06-09 18:56:21.951 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 23370 records
2025-06-09 18:56:21.951 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 18:56:21.984 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 18:56:22.026 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 23370 records, 45 features
2025-06-09 18:56:22.026 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 3.129s
2025-06-09 18:56:22.027 | INFO     | __main__:train:747 | Assuming VIX indicators are pre-merged and correctly named in the loaded data.
2025-06-09 18:56:22.039 | INFO     | __main__:train:775 | Final training data: 14090 records, Validation data: 3530 records
2025-06-09 18:56:22.039 | INFO     | __main__:train:815 | Transforming data format for FinRL environment compatibility
2025-06-09 18:56:22.060 | INFO     | __main__:train:819 | Transformed training data: 14090 records, Validation data: 3530 records
2025-06-09 18:56:22.066 | ERROR    | __main__:train:888 | Environment creation failed: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.
