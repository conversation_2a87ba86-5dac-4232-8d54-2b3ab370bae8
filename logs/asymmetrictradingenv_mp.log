2025-06-09 12:47:05 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 12:47:05 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 12:47:05 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 12:47:05 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 12:47:05 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 12:47:05 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 12:47:11 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 12:47:11 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 12:47:11 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 12:47:15 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 12:47:15 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 12:47:15 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 12:47:20 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 12:47:20 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 12:47:20 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 12:49:30 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 12:49:30 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 12:49:30 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 12:49:35 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 12:49:35 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 12:49:35 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 12:49:40 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 12:49:40 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 12:49:40 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 12:52:27 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 12:52:27 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 12:52:27 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 12:52:32 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 12:52:32 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 12:52:32 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 12:52:39 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 12:52:39 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 12:52:39 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 12:55:03 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 12:55:03 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 12:55:03 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 12:55:07 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 12:55:07 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 12:55:07 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 12:55:13 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 12:55:13 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 12:55:13 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 12:57:47 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 12:57:47 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 12:57:47 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 12:57:52 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 12:57:52 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 12:57:52 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 12:57:59 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 12:57:59 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 12:57:59 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 13:00:31 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 13:00:31 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 13:00:31 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 13:00:36 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 13:00:36 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 13:00:36 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 13:00:41 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 13:00:41 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 13:00:41 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 13:03:05 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 13:03:05 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 13:03:05 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 13:03:10 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 13:03:10 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 13:03:10 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 13:03:15 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 13:03:15 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 13:03:15 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 13:05:32 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 13:05:32 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 13:05:32 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 13:05:37 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 13:05:37 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 13:05:37 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 13:05:43 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 13:05:43 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 13:05:43 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 13:07:56 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 13:07:56 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 13:07:56 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 13:08:02 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 13:08:02 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 13:08:02 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 13:08:08 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 13:08:08 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 13:08:08 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 13:10:42 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 13:10:42 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 13:10:42 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 13:10:48 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 13:10:48 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 13:10:48 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 13:10:54 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 13:10:54 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 13:10:54 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 18:00:44 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 18:00:44 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 18:00:44 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 18:00:44 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-09 18:00:44 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-09 18:00:44 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-09 18:15:29 | ERROR    | AsymmetricTradingEnv:__init__:200 | Failed to initialize parent StockTradingEnv: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.
2025-06-09 18:15:29 | ERROR    | AsymmetricTradingEnv:__init__:201 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 169, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 88, in __init__
    np.array(self.num_stock_shares)
TypeError: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.

2025-06-09 18:17:25 | ERROR    | AsymmetricTradingEnv:__init__:200 | Failed to initialize parent StockTradingEnv: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.
2025-06-09 18:17:25 | ERROR    | AsymmetricTradingEnv:__init__:201 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 169, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 88, in __init__
    np.array(self.num_stock_shares)
TypeError: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.

2025-06-09 18:24:39 | ERROR    | AsymmetricTradingEnv:_initiate_state:504 | Error in _initiate_state: operands could not be broadcast together with shapes (11,) (10,) 
2025-06-09 18:24:39 | ERROR    | AsymmetricTradingEnv:_initiate_state:505 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 489, in _initiate_state
    base_state = super()._initiate_state()
                 ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 408, in _initiate_state
    [self.initial_amount]
ValueError: operands could not be broadcast together with shapes (11,) (10,) 

2025-06-09 18:24:39 | ERROR    | AsymmetricTradingEnv:__init__:205 | Failed to initialize parent StockTradingEnv: 'NoneType' object is not subscriptable
2025-06-09 18:24:39 | ERROR    | AsymmetricTradingEnv:__init__:206 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 174, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 89, in __init__
    * np.array(self.state[1 : 1 + self.stock_dim])
               ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'NoneType' object is not subscriptable

2025-06-09 18:37:29 | ERROR    | AsymmetricTradingEnv:__init__:200 | Failed to initialize parent StockTradingEnv: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.
2025-06-09 18:37:29 | ERROR    | AsymmetricTradingEnv:__init__:201 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 169, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 88, in __init__
    np.array(self.num_stock_shares)
TypeError: The 'out' kwarg is necessary. Use numpy.strings.multiply without it.

