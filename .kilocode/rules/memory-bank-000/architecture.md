# System Architecture - FinRL Trading Agent

## Architecture Overview

### High-Level Design
```
finrl-bot3/
├── main.py                 # CLI entry point
├── config/
│   ├── __init__.py
│   ├── settings.py         # Centralized configuration
│   └── constants.py        # Static constants
├── src/
│   ├── __init__.py
│   ├── data/
│   │   ├── __init__.py
│   │   ├── fetcher.py      # Data acquisition
│   │   ├── processor.py    # Data preprocessing
│   │   └── cache.py        # Caching mechanism
│   ├── models/
│   │   ├── __init__.py
│   │   ├── sac_agent.py    # SAC implementation
│   │   └── environment.py  # Trading environment
│   ├── strategies/
│   │   ├── __init__.py
│   │   └── asymmetric.py   # Asymmetric return strategy
│   ├── trading/
│   │   ├── __init__.py
│   │   ├── alpaca_client.py # Alpaca integration
│   │   └── executor.py     # Trade execution
│   ├── backtesting/
│   │   ├── __init__.py
│   │   ├── engine.py       # Backtesting framework
│   │   └── metrics.py      # Performance metrics
│   └── utils/
│       ├── __init__.py
│       ├── logging.py      # Logging utilities
│       └── helpers.py      # Common utilities
├── data/
│   └── cache/              # CSV cache files
├── models/
│   └── saved/              # Trained model artifacts
├── results/
│   ├── backtests/          # Backtest results
│   └── reports/            # Performance reports
├── tests/
├── requirements.txt
├── environment.yml         # Conda environment
├── .env.example
└── README.md
```

## Core Design Patterns

### 1. Command Pattern
**Implementation**: CLI interface with distinct commands
```python
# main.py structure
class CommandHandler:
    def execute_get_data(self)
    def execute_process_data(self)
    def execute_tune(self)
    def execute_train(self)
    def execute_backtest(self)
    def execute_papertrade(self)
```

### 2. Strategy Pattern
**Implementation**: Pluggable trading strategies
```python
class TradingStrategy(ABC):
    @abstractmethod
    def generate_signals(self, state) -> Dict[str, float]
    
class AsymmetricStrategy(TradingStrategy):
    def generate_signals(self, state) -> Dict[str, float]
```

### 3. Factory Pattern
**Implementation**: Environment and model creation
```python
class EnvironmentFactory:
    @staticmethod
    def create_trading_env(config: Config) -> TradingEnvironment
    
class ModelFactory:
    @staticmethod
    def create_sac_agent(env_config: Dict) -> SACAgent
```

### 4. Observer Pattern
**Implementation**: Event-driven logging and monitoring
```python
class TradingEventObserver(ABC):
    @abstractmethod
    def on_trade_executed(self, trade_info: Dict)
    
class LoggingObserver(TradingEventObserver):
    def on_trade_executed(self, trade_info: Dict)
```

### 5. Singleton Pattern
**Implementation**: Configuration and cache management
```python
class ConfigManager:
    _instance = None
    
class CacheManager:
    _instance = None
```

## Component Relationships

### Data Flow Architecture
```
Alpaca API → DataFetcher → CacheManager → DataProcessor → TradingEnvironment
                                                              ↓
TradingExecutor ← SACAgent ← AsymmetricStrategy ← EnvironmentState
```

### Training Pipeline
```
HistoricalData → FeatureEngineering → TradingEnvironment → SACAgent → TrainedModel
                                            ↓
                                    HyperparameterTuning
```

### Backtesting Pipeline
```
TrainedModel → BacktestEngine → PerformanceMetrics → ReportGenerator
                    ↓
              TradingSimulator
```

## Key Technical Decisions

### 1. ElegantRL Integration
- **Rationale**: Superior SAC implementation for continuous action spaces
- **Implementation**: Custom wrapper for FinRL compatibility
- **Benefits**: Better convergence, more stable training

### 2. Asymmetric Return Implementation
- **Approach**: Dynamic position sizing based on market regime detection
- **Mechanism**: Volatility-adjusted allocation with downside protection
- **Formula**: `position_size = base_size * regime_multiplier * volatility_factor`

### 3. Caching Strategy
- **Format**: CSV files with timestamp-based invalidation
- **Structure**: Separate files per symbol and timeframe
- **Benefits**: Reduced API calls, faster development iteration

### 4. Environment Design
- **State Space**: OHLCV + Technical indicators + Market regime
- **Action Space**: Continuous allocation weights [-1, 1] per asset
- **Reward Function**: Risk-adjusted returns with asymmetric penalty

### 5. Configuration Management
- **Centralized**: Single settings.py file
- **Environment-aware**: Development/production configurations
- **Type-safe**: Pydantic models for validation

## Critical Implementation Paths

### 1. Data Pipeline
```python
# Critical path: Data consistency and quality
raw_data → validation → feature_engineering → normalization → environment_state
```

### 2. Training Loop
```python
# Critical path: Model convergence
initialization → episode_loop → experience_collection → model_update → evaluation
```

### 3. Trading Execution
```python
# Critical path: Real-time decision making
market_data → state_preparation → model_inference → signal_generation → order_execution
```

## Error Handling Patterns

### 1. Graceful Degradation
- **Data Failures**: Use cached data with staleness warnings
- **API Failures**: Retry with exponential backoff
- **Model Failures**: Fallback to conservative strategy

### 2. Circuit Breaker
- **Implementation**: Stop trading on consecutive failures
- **Thresholds**: 3 consecutive API failures, 5% daily loss
- **Recovery**: Manual intervention required

### 3. Comprehensive Logging
- **Levels**: DEBUG, INFO, WARNING, ERROR, CRITICAL
- **Structured**: JSON format for machine parsing
- **Rotation**: Daily rotation with 30-day retention

## Performance Optimization

### 1. Vectorized Operations
- **NumPy/Pandas**: Batch processing for feature engineering
- **GPU Acceleration**: CUDA support for model training

### 2. Memory Management
- **Lazy Loading**: Load data on demand
- **Chunked Processing**: Process large datasets in chunks
- **Memory Monitoring**: Track and limit memory usage

### 3. Parallel Processing
- **Multiprocessing**: Parallel backtesting scenarios
- **Async I/O**: Non-blocking API calls
- **Thread Safety**: Proper synchronization for shared resources