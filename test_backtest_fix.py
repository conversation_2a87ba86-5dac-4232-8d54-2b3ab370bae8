#!/usr/bin/env python3
"""
Test script to verify the backtesting portfolio value tracking fix.
"""

import sys
import os
import numpy as np
import pandas as pd
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_backtest_portfolio_tracking():
    """Test that backtesting now properly tracks portfolio values."""
    print("🔍 Testing backtesting portfolio value tracking fix...")
    
    try:
        from backtesting.engine import BacktestEngine
        from trading.asymmetric_env import AsymmetricTradingEnv
        from strategies.asymmetric_strategy import AsymmetricConfig
        from config.settings import Settings
        
        print("✅ Successfully imported backtesting components")
        
        # Create test data with price movements to ensure portfolio should change
        dates = pd.date_range('2023-01-01', periods=20, freq='D')
        test_data = []
        
        for i, date in enumerate(dates):
            # Create varying prices - some up, some down
            price_change = 1 + (i % 5 - 2) * 0.02  # Varies between 0.96 and 1.04
            base_price = 100 * price_change
            
            test_data.append({
                'date': date,
                'tic': 'TEST',
                'open': base_price,
                'high': base_price + 1,
                'low': base_price - 1,
                'close': base_price + 0.5,
                'volume': 1000000,
                # Add technical indicators
                'sma_5': base_price,
                'ema_12': base_price,
                'rsi_14': 50.0,
                'macd_12_26_9': 0.1,
                'adx_14': 25.0,
                'bbl_20_2.0': base_price - 2,
                'bbm_20_2.0': base_price,
                'bbu_20_2.0': base_price + 2,
                'obv': 1000000 * (i + 1)
            })
        
        df = pd.DataFrame(test_data)
        print(f"✅ Created test data with {len(df)} records and varying prices")
        
        # Create environment
        asymmetric_config = AsymmetricConfig(
            target_upside_downside_ratio=2.0,
            volatility_lookback=5
        )
        
        env = AsymmetricTradingEnv(
            df=df,
            stock_dim=1,
            hmax=100,
            initial_amount=100000,
            num_stock_shares=[0],
            buy_cost_pct=[0.001],
            sell_cost_pct=[0.001],
            reward_scaling=1e-4,
            state_space=50,
            action_space=1,
            tech_indicator_list=['sma_5', 'ema_12', 'rsi_14'],
            asymmetric_config=asymmetric_config,
            log_level="ERROR"  # Reduce logging for test
        )
        
        print("✅ Successfully created test environment")
        
        # Create a simple mock settings object
        class MockSettings:
            def __init__(self):
                self.data = type('obj', (object,), {
                    'symbols': ['TEST'],
                    'tech_indicator_list': ['sma_5', 'ema_12', 'rsi_14']
                })()
        
        settings = MockSettings()
        
        # Create backtest engine
        engine = BacktestEngine(settings)
        print("✅ Successfully created BacktestEngine")
        
        # Run simulation directly (without model - will use random actions)
        print("\n🧪 Running backtest simulation...")
        results = engine._run_simulation(env, model=None)
        
        # Analyze results
        portfolio_values = results['portfolio_values']
        actions = results['actions']
        
        print(f"\n📊 Backtest Results:")
        print(f"   Total steps: {results['total_steps']}")
        print(f"   Total actions: {len(actions)}")
        print(f"   Initial portfolio: ${portfolio_values[0]:,.2f}")
        print(f"   Final portfolio: ${portfolio_values[-1]:,.2f}")
        print(f"   Portfolio values count: {len(portfolio_values)}")
        
        # Check if portfolio values are varying
        portfolio_std = np.std(portfolio_values)
        portfolio_range = max(portfolio_values) - min(portfolio_values)
        total_return = (portfolio_values[-1] - portfolio_values[0]) / portfolio_values[0]
        
        print(f"   Portfolio std: ${portfolio_std:,.2f}")
        print(f"   Portfolio range: ${portfolio_range:,.2f}")
        print(f"   Total return: {total_return:.4%}")
        
        # Show first few and last few portfolio values
        print(f"   First 5 values: {[f'${v:,.0f}' for v in portfolio_values[:5]]}")
        print(f"   Last 5 values: {[f'${v:,.0f}' for v in portfolio_values[-5:]]}")
        
        # Test the fix
        if portfolio_std > 1.0:  # Portfolio should vary by at least $1
            print("✅ SUCCESS: Portfolio values are varying! Backtest fix is working.")
            return True
        else:
            print("❌ ISSUE: Portfolio values are still constant or barely changing.")
            print("   The backtest fix may need further investigation.")
            return False
            
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🚀 Starting backtesting portfolio tracking fix verification...")
    print("=" * 70)
    
    success = test_backtest_portfolio_tracking()
    
    print("=" * 70)
    if success:
        print("🎉 TEST PASSED: Backtesting portfolio tracking fix is working!")
        print("   You should now see non-zero returns in your backtest results.")
    else:
        print("⚠️  TEST FAILED: Backtesting may still have portfolio tracking issues.")
        print("   Further investigation needed.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
