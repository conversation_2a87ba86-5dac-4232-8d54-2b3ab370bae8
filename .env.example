# FinRL Trading Agent Environment Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# ALPACA API CONFIGURATION
# =============================================================================
# Get your API keys from: https://app.alpaca.markets/
ALPACA_API_KEY=your_alpaca_api_key_here
ALPACA_API_SECRET=your_alpaca_api_secret_here
ALPACA_BASE_URL=https://paper-api.alpaca.markets
ALPACA_DATA_URL=https://data.alpaca.markets

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=INFO
LOG_FILE_PATH=logs/trading_agent.log
LOG_ROTATION=10 MB
LOG_RETENTION=30 days
LOG_CONSOLE_ENABLED=true
LOG_FILE_ENABLED=true

# =============================================================================
# DATA CONFIGURATION
# =============================================================================
# Data sources and caching
DATA_PRIMARY_SOURCE=yfinance
DATA_SECONDARY_SOURCE=alpaca
DATA_CACHE_DIR=data/cache
DATA_PROCESSED_DIR=data/processed
DATA_CACHE_EXPIRY_HOURS=24
DATA_YFINANCE_THREADS=4

# Trading symbols (comma-separated)
DATA_SYMBOLS=AAPL,MSFT,GOOGL,AMZN,META,NVDA,TSLA,AVGO,ADBE,ASML
DATA_VIX_SYMBOL=^VIX

# Date ranges for training/validation/testing
DATA_TRAIN_START_DATE=2016-01-01
DATA_TRAIN_END_DATE=2021-12-31
DATA_VALIDATION_START_DATE=2022-01-01
DATA_VALIDATION_END_DATE=2022-12-31
DATA_TEST_START_DATE=2023-01-01
DATA_TEST_END_DATE=2025-04-19

# Data quality thresholds
DATA_MIN_DATA_POINTS=252
DATA_MAX_MISSING_RATIO=0.05

# =============================================================================
# TECHNICAL INDICATORS CONFIGURATION
# =============================================================================
# Moving averages
INDICATORS_SMA_PERIODS=5,10,20,50
INDICATORS_EMA_PERIODS=12,26

# Momentum indicators
INDICATORS_RSI_PERIOD=14
INDICATORS_MACD_FAST=12
INDICATORS_MACD_SLOW=26
INDICATORS_MACD_SIGNAL=9

# Volatility indicators
INDICATORS_BB_PERIOD=20
INDICATORS_BB_STD=2.0
INDICATORS_ATR_PERIOD=14

# Volume indicators
INDICATORS_OBV_ENABLED=true
INDICATORS_AD_ENABLED=true
INDICATORS_CMF_PERIOD=20

# VIX indicators
INDICATORS_VIX_MA_PERIODS=5,20
INDICATORS_VIX_PERCENTILE_WINDOW=252

# =============================================================================
# OPTUNA HYPERPARAMETER TUNING
# =============================================================================
OPTUNA_N_TRIALS=100
OPTUNA_STUDY_NAME=sac_trading_optimization
OPTUNA_STORAGE_URL=sqlite:///optuna_studies.db

# SAC hyperparameter ranges (format: min,max)
OPTUNA_LEARNING_RATE_RANGE=0.00001,0.01
OPTUNA_BATCH_SIZE_CHOICES=64,128,256,512
OPTUNA_GAMMA_RANGE=0.9,0.999
OPTUNA_TAU_RANGE=0.001,0.01
OPTUNA_ALPHA_RANGE=0.1,0.3

# Pruning configuration
OPTUNA_PRUNER_TYPE=MedianPruner
OPTUNA_PRUNER_N_STARTUP_TRIALS=10
OPTUNA_PRUNER_N_WARMUP_STEPS=50

# =============================================================================
# SAC MODEL CONFIGURATION
# =============================================================================
# Network architecture
SAC_ACTOR_HIDDEN_SIZES=128,128
SAC_CRITIC_HIDDEN_SIZES=128,128

# Training parameters
SAC_LEARNING_RATE=0.0003
SAC_BATCH_SIZE=256
SAC_BUFFER_SIZE=1000000
SAC_GAMMA=0.99
SAC_TAU=0.005
SAC_ALPHA=0.2

# Training schedule
SAC_TOTAL_TIMESTEPS=1000000
SAC_LEARNING_STARTS=10000
SAC_TRAIN_FREQ=1
SAC_GRADIENT_STEPS=1

# Evaluation and checkpointing
SAC_EVAL_FREQ=10000
SAC_EVAL_EPISODES=10
SAC_SAVE_FREQ=50000
SAC_CHECKPOINT_DIR=models/checkpoints

# =============================================================================
# TRADING CONFIGURATION
# =============================================================================
# Portfolio settings
TRADING_INITIAL_CAPITAL=100000.0
TRADING_MAX_POSITION_SIZE=0.2
TRADING_TRANSACTION_COST=0.001

# Risk management
TRADING_MAX_DRAWDOWN=0.15
TRADING_STOP_LOSS=0.05
TRADING_TAKE_PROFIT=0.10

# Execution settings
TRADING_REBALANCE_FREQUENCY=daily
TRADING_MARKET_HOURS_ONLY=true

# =============================================================================
# BACKTESTING CONFIGURATION
# =============================================================================
BACKTEST_BENCHMARK_SYMBOL=SPY
BACKTEST_RISK_FREE_RATE=0.02
BACKTEST_RESULTS_DIR=results/backtests
BACKTEST_SAVE_TRADES=true
BACKTEST_SAVE_PORTFOLIO=true
BACKTEST_PLOT_RESULTS=true
BACKTEST_PLOT_FORMAT=png

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
ENVIRONMENT=development
DEBUG=false

# =============================================================================
# OPTIONAL: ADVANCED SETTINGS
# =============================================================================
# Uncomment and modify these if you need custom settings

# Custom model save directory
# MODEL_SAVE_DIR=models/custom

# Custom results directory
# RESULTS_DIR=results/custom

# Custom cache settings
# CACHE_BACKEND=redis
# REDIS_URL=redis://localhost:6379/0

# Performance monitoring
# ENABLE_PROFILING=false
# PROFILING_OUTPUT_DIR=profiling

# Notification settings (for alerts)
# SLACK_WEBHOOK_URL=https://hooks.slack.com/services/...
# EMAIL_SMTP_SERVER=smtp.gmail.com
# EMAIL_SMTP_PORT=587
# EMAIL_USERNAME=<EMAIL>
# EMAIL_PASSWORD=your_app_password