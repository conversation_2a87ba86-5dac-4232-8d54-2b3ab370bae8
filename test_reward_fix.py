#!/usr/bin/env python3
"""
Test script to verify the AsymmetricTradingEnv reward calculation fix.
This script tests that rewards are now varying instead of constant.
"""

import sys
import os
import numpy as np
import pandas as pd
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_reward_calculation():
    """Test that the reward calculation fix is working."""
    print("🔍 Testing AsymmetricTradingEnv reward calculation fix...")
    
    try:
        # Import the fixed environment
        from trading.asymmetric_env import AsymmetricTradingEnv
        from strategies.asymmetric_strategy import AsymmetricConfig
        
        print("✅ Successfully imported AsymmetricTradingEnv")
        
        # Create minimal test data
        dates = pd.date_range('2023-01-01', periods=10, freq='D')
        test_data = []
        
        for i, date in enumerate(dates):
            # Create varying price data to ensure rewards will vary
            base_price = 100 + i * 2  # Increasing prices
            test_data.append({
                'date': date,
                'tic': 'TEST',
                'open': base_price,
                'high': base_price + 1,
                'low': base_price - 1,
                'close': base_price + 0.5,
                'volume': 1000000,
                # Add some basic technical indicators
                'sma_5': base_price,
                'ema_12': base_price,
                'rsi_14': 50.0,
                'macd_12_26_9': 0.1,
                'adx_14': 25.0,
                'bbl_20_2.0': base_price - 2,
                'bbm_20_2.0': base_price,
                'bbu_20_2.0': base_price + 2,
                'obv': 1000000 * (i + 1)
            })
        
        df = pd.DataFrame(test_data)
        print(f"✅ Created test data with {len(df)} records")
        
        # Create asymmetric config
        asymmetric_config = AsymmetricConfig(
            target_upside_downside_ratio=2.0,
            volatility_lookback=5
        )
        
        # Create environment
        env = AsymmetricTradingEnv(
            df=df,
            stock_dim=1,
            hmax=100,
            initial_amount=100000,
            num_stock_shares=[0],
            buy_cost_pct=[0.001],
            sell_cost_pct=[0.001],
            reward_scaling=1e-4,
            state_space=50,  # Approximate
            action_space=1,
            tech_indicator_list=['sma_5', 'ema_12', 'rsi_14'],
            asymmetric_config=asymmetric_config,
            log_level="ERROR"  # Reduce logging for test
        )
        
        print("✅ Successfully created AsymmetricTradingEnv")
        
        # Test the step method with different actions
        state, info = env.reset()
        print(f"✅ Environment reset successful, initial state shape: {state.shape}")
        
        rewards = []
        actions_tested = [
            np.array([0.1]),   # Small buy
            np.array([0.5]),   # Medium buy  
            np.array([-0.2]),  # Small sell
            np.array([0.0]),   # Hold
            np.array([0.3])    # Another buy
        ]
        
        print("\n🧪 Testing reward calculation with different actions:")
        for i, action in enumerate(actions_tested):
            try:
                state, reward, terminated, truncated, info = env.step(action)
                rewards.append(reward)
                print(f"   Step {i+1}: Action={action[0]:6.2f}, Reward={reward:10.6f}")
                
                if terminated:
                    print("   Environment terminated, resetting...")
                    state, info = env.reset()
                    
            except Exception as e:
                print(f"   ❌ Error in step {i+1}: {e}")
                return False
        
        # Analyze rewards
        rewards = np.array(rewards)
        reward_std = np.std(rewards)
        reward_mean = np.mean(rewards)
        
        print(f"\n📊 Reward Analysis:")
        print(f"   Rewards: {rewards}")
        print(f"   Mean: {reward_mean:.6f}")
        print(f"   Std:  {reward_std:.6f}")
        
        # Check if rewards are varying (not constant)
        if reward_std > 1e-10:  # Allow for small numerical differences
            print("✅ SUCCESS: Rewards are varying! Fix is working correctly.")
            print("   The constant avgR issue has been resolved.")
            return True
        else:
            print("❌ ISSUE: Rewards are still constant.")
            print("   The fix may not be working as expected.")
            return False
            
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🚀 Starting AsymmetricTradingEnv reward fix verification...")
    print("=" * 60)
    
    success = test_reward_calculation()
    
    print("=" * 60)
    if success:
        print("🎉 TEST PASSED: Reward calculation fix is working!")
        print("   You can now run training and expect varying avgR values.")
    else:
        print("⚠️  TEST FAILED: There may still be issues with reward calculation.")
        print("   Please check the implementation.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
