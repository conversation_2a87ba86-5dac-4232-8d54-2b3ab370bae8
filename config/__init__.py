"""Configuration package for FinRL Trading Agent

This package provides centralized configuration management with type safety
and validation using Pydantic.
"""

from .settings import Settings, settings
from .constants import (
    TECH_STOCKS,
    MARKET_INDICES,
    VIX_REGIMES,
    DEFAULT_INDICATORS,
    TECH_INDICATORS,
    <PERSON><PERSON>K_LIMITS,
    AS<PERSON><PERSON><PERSON><PERSON><PERSON>_PARAMS,
    DATA_QUALITY,
    MARKET_HOURS,
    SAC_DEFAULTS
)

# Export VIX_SYMBOL from settings
VIX_SYMBOL = settings.data.vix_symbol

# Alias DATA_QUALITY as DATA_QUALITY_THRESHOLDS for backward compatibility
DATA_QUALITY_THRESHOLDS = DATA_QUALITY

__all__ = [
    'Settings',
    'settings',
    'TECH_STOCKS',
    'MARKET_INDICES', 
    'VIX_REGIMES',
    'DEFAULT_INDICATORS',
    'TECH_INDICATORS',
    'RISK_LIMITS',
    'ASY<PERSON>ET<PERSON>C_PARAMS',
    'VIX_SYMBOL',
    'DATA_QUALITY_THRESHOLDS',
    'MARKET_HOURS',
    'SAC_DEFAULTS'
]