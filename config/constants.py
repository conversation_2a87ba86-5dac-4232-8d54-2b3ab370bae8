"""Static Constants for FinRL Trading Agent

This module contains all static constants used throughout the application.
These values should not change during runtime and represent fixed parameters.
"""

from typing import Dict, List

# Market Constants
MARKET_HOURS = {
    'open': '09:30',
    'close': '16:00',
    'timezone': 'US/Eastern'
}

TRADING_DAYS_PER_YEAR = 252
HOURS_PER_TRADING_DAY = 6.5
MINUTES_PER_TRADING_DAY = 390

# Asset Information
TECH_STOCKS = [
    'AAPL',   # Apple Inc.
    'MSFT',   # Microsoft Corporation
    'GOOGL',  # Alphabet Inc. Class A
    'AMZN',   # Amazon.com Inc.
    'META',   # Meta Platforms Inc.
    'NVDA',   # NVIDIA Corporation
    'TSLA',   # Tesla Inc.
    'AVGO',   # Broadcom Inc.
    'ADBE',   # Adobe Inc.
    'ASML'    # ASML Holding N.V.
]

STOCK_INFO = {
    'AAPL': {'name': 'Apple Inc.', 'sector': 'Technology', 'market_cap': 'Large'},
    'MSFT': {'name': 'Microsoft Corporation', 'sector': 'Technology', 'market_cap': 'Large'},
    'GOOGL': {'name': 'Alphabet Inc. Class A', 'sector': 'Technology', 'market_cap': 'Large'},
    'AMZN': {'name': 'Amazon.com Inc.', 'sector': 'Consumer Discretionary', 'market_cap': 'Large'},
    'META': {'name': 'Meta Platforms Inc.', 'sector': 'Technology', 'market_cap': 'Large'},
    'NVDA': {'name': 'NVIDIA Corporation', 'sector': 'Technology', 'market_cap': 'Large'},
    'TSLA': {'name': 'Tesla Inc.', 'sector': 'Consumer Discretionary', 'market_cap': 'Large'},
    'AVGO': {'name': 'Broadcom Inc.', 'sector': 'Technology', 'market_cap': 'Large'},
    'ADBE': {'name': 'Adobe Inc.', 'sector': 'Technology', 'market_cap': 'Large'},
    'ASML': {'name': 'ASML Holding N.V.', 'sector': 'Technology', 'market_cap': 'Large'}
}

# Market Indices
MARKET_INDICES = {
    'SPY': 'SPDR S&P 500 ETF Trust',
    'QQQ': 'Invesco QQQ Trust',
    'VIX': 'CBOE Volatility Index',
    'DXY': 'US Dollar Index'
}

# VIX Regime Classification
VIX_REGIMES = {
    'low_volatility': {'min': 0, 'max': 20, 'description': 'Low volatility environment'},
    'moderate_volatility': {'min': 20, 'max': 30, 'description': 'Moderate volatility environment'},
    'high_volatility': {'min': 30, 'max': 50, 'description': 'High volatility environment'},
    'extreme_volatility': {'min': 50, 'max': 100, 'description': 'Extreme volatility environment'}
}

# Technical Indicator Defaults
DEFAULT_INDICATORS = {
    'sma': [5, 10, 20, 50, 200],
    'ema': [12, 26, 50],
    'rsi': 14,
    'macd': {'fast': 12, 'slow': 26, 'signal': 9},
    'bollinger_bands': {'period': 20, 'std': 2.0},
    'atr': 14,
    'stochastic': {'k_period': 14, 'd_period': 3},
    'williams_r': 14,
    'cci': 20,
    'adx': 14
}

# Alias for backward compatibility
TECH_INDICATORS = DEFAULT_INDICATORS

# Data Quality Thresholds
DATA_QUALITY = {
    'min_trading_days': 252,  # Minimum 1 year of data
    'max_missing_ratio': 0.05,  # Maximum 5% missing data
    'min_volume': 100000,  # Minimum daily volume
    'max_price_change': 0.5,  # Maximum 50% daily price change (outlier detection)
    'min_price': 1.0,  # Minimum stock price
    'min_quality_score': 0.4 # Minimum acceptable data quality score (0 to 1)
}

# Model Training Constants
TRAINING_CONSTANTS = {
    'random_seed': 42,
    'validation_split': 0.2,
    'test_split': 0.2,
    'early_stopping_patience': 50,
    'learning_rate_decay': 0.95,
    'min_learning_rate': 1e-6
}

# SAC Model Defaults
SAC_DEFAULTS = {
    'actor_lr': 3e-4,
    'critic_lr': 3e-4,
    'alpha_lr': 3e-4,
    'gamma': 0.99,
    'tau': 0.005,
    'alpha': 0.2,
    'batch_size': 256,
    'buffer_size': 1000000,
    'learning_starts': 10000,
    'train_freq': 1,
    'gradient_steps': 1,
    'target_update_interval': 1
}

# Risk Management
RISK_LIMITS = {
    'max_position_size': 0.2,  # 20% max position per asset
    'max_portfolio_leverage': 1.0,  # No leverage
    'max_daily_loss': 0.02,  # 2% max daily loss
    'max_drawdown': 0.15,  # 15% max drawdown
    'min_cash_ratio': 0.05,  # 5% minimum cash
    'correlation_threshold': 0.8  # Maximum correlation between positions
}

# Transaction Costs
TRANSACTION_COSTS = {
    'commission_rate': 0.0,  # Commission per trade (Alpaca is commission-free)
    'spread_cost': 0.0005,  # Bid-ask spread cost (0.05%)
    'market_impact': 0.0001,  # Market impact cost (0.01%)
    'slippage': 0.0002  # Slippage cost (0.02%)
}

# Performance Metrics
PERFORMANCE_METRICS = [
    'total_return',
    'annual_return',
    'volatility',
    'sharpe_ratio',
    'sortino_ratio',
    'max_drawdown',
    'calmar_ratio',
    'win_rate',
    'profit_factor',
    'average_trade',
    'total_trades'
]

# File Extensions
FILE_EXTENSIONS = {
    'data': '.csv',
    'model': '.pkl',
    'config': '.json',
    'log': '.log',
    'plot': '.png',
    'report': '.html'
}

# API Endpoints
API_ENDPOINTS = {
    'alpaca': {
        'paper': 'https://paper-api.alpaca.markets',
        'live': 'https://api.alpaca.markets',
        'data': 'https://data.alpaca.markets'
    },
    'yahoo': {
        'finance': 'https://query1.finance.yahoo.com'
    }
}

# Error Messages
ERROR_MESSAGES = {
    'data_fetch_failed': 'Failed to fetch data for symbol: {}',
    'model_load_failed': 'Failed to load model from: {}',
    'insufficient_data': 'Insufficient data for symbol: {} (got {} days, need {})',
    'api_key_missing': 'API key not found for service: {}',
    'invalid_date_range': 'Invalid date range: start_date {} must be before end_date {}',
    'model_not_trained': 'Model has not been trained yet',
    'cache_corrupted': 'Cache file corrupted for symbol: {}'
}

# Success Messages
SUCCESS_MESSAGES = {
    'data_fetched': 'Successfully fetched {} records for symbol: {}',
    'model_trained': 'Model training completed successfully',
    'model_saved': 'Model saved to: {}',
    'backtest_completed': 'Backtest completed with {} trades',
    'cache_updated': 'Cache updated for symbol: {}'
}

# Environment Variables
REQUIRED_ENV_VARS = [
    'ALPACA_API_KEY',
    'ALPACA_API_SECRET'
]

OPTIONAL_ENV_VARS = [
    'LOG_LEVEL',
    'LOG_FILE_PATH',
    'DATA_CACHE_DIR',
    'MODEL_SAVE_DIR'
]

# Asymmetric Return Profile Parameters
ASYMMETRIC_PARAMS = {
    'bear_market_threshold': -0.1,  # 10% market decline
    'bull_market_threshold': 0.05,  # 5% market gain
    'defensive_allocation': 0.3,  # 30% allocation in defensive mode
    'aggressive_allocation': 0.8,  # 80% allocation in aggressive mode
    'neutral_allocation': 0.6,  # 60% allocation in neutral mode
    'regime_lookback': 20,  # Days to look back for regime classification
    'volatility_threshold': 25  # VIX threshold for high volatility
}

# Logging Formats
LOG_FORMATS = {
    'detailed': '{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} | {message}',
    'simple': '{time:HH:mm:ss} | {level: <8} | {message}',
    'json': '{"timestamp": "{time:YYYY-MM-DD HH:mm:ss.SSS}", "level": "{level}", "module": "{name}", "function": "{function}", "line": {line}, "message": "{message}"}'
}

# Color Schemes for Plots
COLOR_SCHEMES = {
    'default': ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd'],
    'professional': ['#2E86AB', '#A23B72', '#F18F01', '#C73E1D', '#592E83'],
    'colorblind': ['#1b9e77', '#d95f02', '#7570b3', '#e7298a', '#66a61e']
}