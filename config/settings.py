"""Centralized Configuration Management for FinRL Trading Agent

This module provides comprehensive configuration management using Pydantic for
type safety and validation. All application settings are centralized here.
"""

import os
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

from pydantic import Field, field_validator, validator
from pydantic_settings import BaseSettings


class LoggingConfig(BaseSettings):
    """Logging configuration settings"""
    level: str = Field(default="INFO", description="Logging level")
    format: str = Field(
        default="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        description="Log message format"
    )
    file_path: Optional[str] = Field(default="logs/trading_agent.log", description="Log file path")
    rotation: str = Field(default="10 MB", description="Log file rotation size")
    retention: str = Field(default="30 days", description="Log file retention period")
    console_enabled: bool = Field(default=True, description="Enable console logging")
    file_enabled: bool = Field(default=True, description="Enable file logging")
    enable_console_workers: bool = Field(default=True, description="Enable console logging for worker processes")  # New setting for worker console logs
    format_type: str = Field(default="default", description="Log format type (e.g., default, simple, json)")
    enable_console_main: bool = Field(default=True, description="Enable console logging for the main process")
    
    class Config:
        env_prefix = "LOG_"


class DataConfig(BaseSettings):
    """Data acquisition and processing configuration"""
    # Symbols
    symbols: List[str] = Field(
        default=['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'META', 'NVDA', 'TSLA', 'AVGO', 'ADBE', 'ASML'],
        description="Target trading symbols"
    )
    
    @field_validator('symbols', 'tech_indicator_list')
    @classmethod
    def parse_list_fields(cls, v):
        """Parse comma-separated strings into lists"""
        if isinstance(v, str):
            return [item.strip() for item in v.split(',') if item.strip()]
        return v
    
    vix_symbol: str = Field(default="^VIX", description="VIX volatility index symbol")
    
    # Time interval
    interval: str = Field(default="1d", description="Data time interval (e.g., 1d, 1h, 15m)")
    
    # Date ranges
    train_start_date: str = Field(default="2016-01-01", description="Training data start date")
    train_end_date: str = Field(default="2021-12-31", description="Training data end date")
    validation_start_date: str = Field(default="2022-01-01", description="Validation data start date")
    validation_end_date: str = Field(default="2022-12-31", description="Validation data end date")
    test_start_date: str = Field(default="2023-01-01", description="Test data start date")
    test_end_date: str = Field(default="2025-04-19", description="Test data end date")
    
    # Final training combines train_data + validation_data
    final_train_start_date: str = Field(default="2016-01-01", description="Final training start date (same as train_start_date)")
    final_train_end_date: str = Field(default="2022-12-31", description="Final training end date (same as validation_end_date)")
    
    # Data sources
    primary_source: str = Field(default="yfinance", description="Primary data source")
    secondary_source: str = Field(default="alpaca", description="Secondary data source")
    yfinance_threads: int = Field(default=4, description="Number of threads for yfinance")
    
    # Storage
    cache_dir: str = Field(default="data/cache", description="Data cache directory")
    processed_dir: str = Field(default="data/processed", description="Processed data directory")
    processed_file_name: str = Field(default="processed_data.csv", description="Processed data file name")
    cache_expiry_hours: int = Field(default=24, description="Cache expiry in hours")
    
    # Data quality
    min_data_points: int = Field(default=252, description="Minimum data points required")
    max_missing_ratio: float = Field(default=0.05, description="Maximum missing data ratio")
    include_vix: bool = Field(default=True, description="Whether to include VIX data and features")
    vix_features: List[str] = Field(
        default=['vix_ma_5', 'vix_ma_20', 'vix_change', 'vix_percentile_252'],
        description="List of VIX feature names to be used if include_vix is True (lowercase)"
    )

    # Technical Indicators - using lowercase column names (pandas_ta outputs are converted to lowercase)
    tech_indicator_list: List[str] = Field(
        default=[
            'sma_5', 'sma_10', 'sma_20', 'sma_50',
            'ema_12', 'ema_26',
            'rsi_14', 'cci_20',
            'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9',
            'adx_14', 'dmp_14', 'dmn_14',
            'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0',
            'obv',
            'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d',
            'volume_ma_20', 'volume_ratio', 'volatility_20d',
            # VIX features - as produced by DataProcessor (lowercase)
            'vix_ma_5', 'vix_ma_20', 'vix_percentile_252',
            'vix_change', 'vix_change_5d', 'vix_regime_numeric',
            'turbulence'
        ],
        description="List of technical indicators to calculate and use - matched to actual column names from DataProcessor"
    )
    
    class Config:
        env_prefix = "DATA_"


class TechnicalIndicatorsConfig(BaseSettings):
    """Technical indicators configuration using pandas_ta"""
    # Moving averages
    sma_periods: List[int] = Field(default=[5, 10, 20, 50], description="SMA periods")
    ema_periods: List[int] = Field(default=[12, 26], description="EMA periods")
    
    @field_validator('sma_periods', 'ema_periods', 'rsi_periods', 'cci_periods', 'vix_ma_periods')
    @classmethod
    def parse_int_list_fields(cls, v):
        """Parse comma-separated strings into lists of integers"""
        if isinstance(v, str):
            return [int(item.strip()) for item in v.split(',') if item.strip()]
        return v
    
    # Momentum indicators
    rsi_periods: List[int] = Field(default=[14], description="RSI periods") # Changed from rsi_period to rsi_periods
    cci_periods: List[int] = Field(default=[20], description="CCI periods") # Added CCI periods
    macd_fast: int = Field(default=12, description="MACD fast period")
    macd_slow: int = Field(default=26, description="MACD slow period")
    macd_signal: int = Field(default=9, description="MACD signal period")
    adx_length: int = Field(default=14, description="ADX length") # Added ADX length
    
    # Volatility indicators
    bbands_length: int = Field(default=20, description="Bollinger Bands length") # Changed from bb_period to bbands_length
    bbands_std: float = Field(default=2.0, description="Bollinger Bands standard deviation") # Changed from bb_std
    atr_period: int = Field(default=14, description="ATR period")
    
    # Volume indicators
    obv_enabled: bool = Field(default=True, description="Enable OBV indicator")
    ad_enabled: bool = Field(default=True, description="Enable A/D indicator")
    cmf_period: int = Field(default=20, description="CMF period")

    # Minimum data points for calculation
    min_data_points_for_indicators: int = Field(default=20, description="Minimum data points required to calculate indicators")
    
    # VIX indicators
    vix_ma_periods: List[int] = Field(default=[5, 20], description="VIX moving average periods")
    vix_percentile_window: int = Field(default=252, description="VIX percentile calculation window")
    
    class Config:
        env_prefix = "INDICATORS_"


class OptimizationConfig(BaseSettings):
    """Optuna hyperparameter tuning configuration"""
    n_trials: int = Field(default=100, description="Number of optimization trials")
    study_name: str = Field(default="sac_trading_optimization", description="Study name")
    storage_url: Optional[str] = Field(default=None, description="Optuna storage URL")
    
    # SAC hyperparameter ranges
    learning_rate_range: tuple = Field(default=(1e-5, 1e-2), description="Learning rate range")
    batch_size_choices: List[int] = Field(default=[64, 128, 256, 512], description="Batch size choices")
    
    @field_validator('batch_size_choices')
    @classmethod
    def parse_batch_size_choices(cls, v):
        """Parse comma-separated strings into lists of integers"""
        if isinstance(v, str):
            return [int(item.strip()) for item in v.split(',') if item.strip()]
        return v
    hidden_sizes_choices: List[List[int]] = Field(
        default=[[64, 64], [128, 128], [256, 256], [128, 64], [256, 128]],
        description="Hidden layer size choices"
    )
    gamma_range: tuple = Field(default=(0.9, 0.999), description="Discount factor range")
    tau_range: tuple = Field(default=(0.001, 0.01), description="Soft update coefficient range")
    alpha_range: tuple = Field(default=(0.1, 0.3), description="Entropy coefficient range")
    
    # Pruning
    pruner_type: str = Field(default="MedianPruner", description="Pruner type")
    pruner_n_startup_trials: int = Field(default=10, description="Pruner startup trials")
    pruner_n_warmup_steps: int = Field(default=50, description="Pruner warmup steps")
    
    class Config:
        env_prefix = "OPTUNA_"


class SACConfig(BaseSettings):
    """SAC algorithm configuration"""
    # Network architecture
    actor_hidden_sizes: List[int] = Field(default=[128, 128], description="Actor network hidden sizes")
    critic_hidden_sizes: List[int] = Field(default=[128, 128], description="Critic network hidden sizes")
    net_dims: List[int] = Field(default=[128, 128], description="Network dimensions for actor/critic networks")
    
    @field_validator('actor_hidden_sizes', 'critic_hidden_sizes', 'net_dims')
    @classmethod
    def parse_network_sizes(cls, v):
        """Parse comma-separated strings into lists of integers"""
        if isinstance(v, str):
            return [int(item.strip()) for item in v.split(',') if item.strip()]
        return v
    
    # Training parameters
    learning_rate: float = Field(default=3e-4, description="Learning rate")
    batch_size: int = Field(default=256, description="Batch size")
    buffer_size: int = Field(default=1000000, description="Replay buffer size")
    gamma: float = Field(default=0.99, description="Discount factor")
    tau: float = Field(default=0.005, description="Soft update coefficient")
    alpha: float = Field(default=0.2, description="Entropy coefficient")
    
    # Training schedule
    total_timesteps: int = Field(default=100000, description="Total training timesteps - main control parameter")
    learning_starts: int = Field(default=10000, description="Steps before learning starts (explore_step)")
    train_freq: int = Field(default=1, description="Training frequency")
    gradient_steps: int = Field(default=1, description="Gradient steps per update")
    # Removed target_step - was confusing and unused, caused parameter mess
    repeat_times: float = Field(default=1.0, description="Repeat times for optimization")
    reward_scale: float = Field(default=1.0, description="Reward scale for optimization")
    if_per: bool = Field(default=False, description="Whether to use prioritized experience replay")
    if_off_policy: bool = Field(default=True, description="Whether to use off-policy learning")
    
    # Evaluation
    eval_freq: int = Field(default=10000, description="Evaluation frequency")
    eval_episodes: int = Field(default=10, description="Number of evaluation episodes")
    
    # Checkpointing
    save_freq: int = Field(default=50000, description="Model save frequency")
    checkpoint_dir: str = Field(default="models/checkpoints", description="Checkpoint directory")
    
    # Logging
    log_interval: int = Field(default=1000, description="Logging interval for training progress")
    device: str = Field(default="cpu", description="Device to use for training (e.g., 'cpu', 'cuda', 'cuda:0')")

    class Config:
        env_prefix = "SAC_"


class AsymmetricStrategyConfig(BaseSettings):
    """Asymmetric strategy configuration"""
    # Asymmetric parameters
    target_upside_downside_ratio: float = Field(default=2.0, description="Target upside/downside ratio")
    momentum_threshold: float = Field(default=0.02, description="Momentum detection threshold")
    mean_reversion_threshold: float = Field(default=0.05, description="Mean reversion threshold")
    volatility_lookback: int = Field(default=20, description="Volatility calculation window")
    
    # Signal generation
    rsi_period: int = Field(default=14, description="RSI period")
    rsi_oversold: float = Field(default=30, description="RSI oversold level")
    rsi_overbought: float = Field(default=70, description="RSI overbought level")
    
    # Moving averages
    fast_ma_period: int = Field(default=10, description="Fast moving average")
    slow_ma_period: int = Field(default=20, description="Slow moving average")
    
    # Bollinger Bands
    bb_period: int = Field(default=20, description="Bollinger Bands period")
    bb_std: float = Field(default=2.0, description="Bollinger Bands standard deviation")
    
    # Position sizing
    base_position_size: float = Field(default=0.05, description="Base position size")
    max_asymmetric_multiplier: float = Field(default=2.0, description="Maximum asymmetric multiplier")
    min_asymmetric_multiplier: float = Field(default=0.5, description="Minimum asymmetric multiplier")
    
    # Risk management
    asymmetric_stop_loss: float = Field(default=0.03, description="Asymmetric stop loss")
    asymmetric_take_profit: float = Field(default=0.06, description="Asymmetric take profit")
    signal_threshold: float = Field(default=0.5, description="Signal strength threshold")
    
    class Config:
        env_prefix = "ASYMMETRIC_"


class TradingConfig(BaseSettings):
    """Trading execution configuration"""
    # Portfolio
    initial_capital: float = Field(default=100000.0, description="Initial trading capital")
    max_position_size: float = Field(default=0.2, description="Maximum position size per asset")
    transaction_cost: float = Field(default=0.001, description="Transaction cost rate")
    
    # Risk management
    max_drawdown: float = Field(default=0.15, description="Maximum allowed drawdown")
    stop_loss: float = Field(default=0.05, description="Stop loss threshold")
    take_profit: float = Field(default=0.10, description="Take profit threshold")
    
    # Execution
    rebalance_frequency: str = Field(default="daily", description="Rebalancing frequency")
    market_hours_only: bool = Field(default=True, description="Trade only during market hours")
    
    class Config:
        env_prefix = "TRADING_"


class AlpacaConfig(BaseSettings):
    """Alpaca API configuration"""
    api_key: Optional[str] = Field(default=None, description="Alpaca API key")
    api_secret: Optional[str] = Field(default=None, description="Alpaca API secret")
    base_url: str = Field(default="https://paper-api.alpaca.markets", description="Alpaca API base URL")
    data_url: str = Field(default="https://data.alpaca.markets", description="Alpaca data API URL")
    
    # Rate limiting
    max_requests_per_minute: int = Field(default=200, description="Max requests per minute")
    retry_attempts: int = Field(default=3, description="Number of retry attempts")
    retry_delay: float = Field(default=1.0, description="Retry delay in seconds")
    
    class Config:
        env_prefix = "ALPACA_"


class EnvConfig(BaseSettings):
    """Environment configuration settings"""
    initial_amount: float = Field(default=100000.0, description="Initial amount for the environment")
    transaction_cost_pct: float = Field(default=0.001, description="Transaction cost percentage")
    reward_scaling: float = Field(default=1e-4, description="Reward scaling factor")
    initial_buy: bool = Field(default=True, description="Whether to make an initial buy at the start of an episode")
    max_steps_per_episode: int = Field(default=1000, description="Maximum steps per episode")
    hmax: int = Field(default=100, description="Maximum shares to trade per stock")

    class Config:
        env_prefix = "ENV_"


class BacktestingConfig(BaseSettings):
    """Backtesting configuration"""
    # Metrics
    benchmark_symbol: str = Field(default="SPY", description="Benchmark symbol")
    risk_free_rate: float = Field(default=0.02, description="Risk-free rate for Sharpe ratio")
    
    # Output
    results_dir: str = Field(default="results/backtests", description="Backtest results directory")
    save_trades: bool = Field(default=True, description="Save individual trades")
    save_portfolio: bool = Field(default=True, description="Save portfolio history")
    
    # Visualization
    plot_results: bool = Field(default=True, description="Generate result plots")
    plot_format: str = Field(default="png", description="Plot file format")
    
    class Config:
        env_prefix = "BACKTEST_"


class Settings(BaseSettings):
    """Main application settings"""
    # Environment
    environment: str = Field(default="development", description="Application environment")
    debug: bool = Field(default=False, description="Debug mode")
    
    # Project paths
    project_root: Path = Field(default_factory=lambda: Path(__file__).parent.parent)
    
    # Configuration sections
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    data: DataConfig = Field(default_factory=DataConfig)
    indicators: TechnicalIndicatorsConfig = Field(default_factory=TechnicalIndicatorsConfig)
    optuna: OptimizationConfig = Field(default_factory=OptimizationConfig)
    sac: SACConfig = Field(default_factory=SACConfig)
    trading: TradingConfig = Field(default_factory=TradingConfig)
    asymmetric: AsymmetricStrategyConfig = Field(default_factory=AsymmetricStrategyConfig)
    alpaca: AlpacaConfig = Field(default_factory=AlpacaConfig)
    backtesting: BacktestingConfig = Field(default_factory=BacktestingConfig)
    env: EnvConfig = Field(default_factory=EnvConfig)
    
    def __init__(self, config_file: Optional[str] = None, **kwargs):
        """Initialize settings with optional config file override"""
        if config_file and os.path.exists(config_file):
            # Load from config file if provided
            import json
            with open(config_file, 'r') as f:
                file_config = json.load(f)
            kwargs.update(file_config)
        
        super().__init__(**kwargs)
        
        # Ensure directories exist
        self._create_directories()
    
    def _create_directories(self):
        """Create necessary directories"""
        directories = [
            self.data.cache_dir,
            self.data.processed_dir,
            self.sac.checkpoint_dir,
            self.backtesting.results_dir,
            "logs"
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    @validator('project_root', pre=True)
    def validate_project_root(cls, v):
        """Ensure project root is a Path object"""
        return Path(v) if not isinstance(v, Path) else v
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        extra = "allow"
        
    def get_data_path(self, filename: str) -> Path:
        """Get full path for data file"""
        return self.project_root / self.data.cache_dir / filename
    
    def get_model_path(self, filename: str) -> Path:
        """Get full path for model file"""
        return self.project_root / "models" / "saved" / filename
    
    def get_results_path(self, filename: str) -> Path:
        """Get full path for results file"""
        return self.project_root / self.backtesting.results_dir / filename
    
    def apply_overrides(self, overrides: Dict[str, Any]):
        """Apply hierarchical overrides to configuration
        
        Supports both nested format: {'sac': {'total_timesteps': 10}}
        and flat format: {'sac.total_timesteps': 10}
        
        Args:
            overrides: Dictionary of configuration overrides
        """
        from typing import Dict, Any
        
        for key, value in overrides.items():
            if '.' in key:
                # Handle flat keys like 'sac.total_timesteps'
                section, param = key.split('.', 1)
                if hasattr(self, section):
                    section_obj = getattr(self, section)
                    if hasattr(section_obj, param):
                        setattr(section_obj, param, value)
                        print(f"Applied override: {section}.{param} = {value}")
                    else:
                        print(f"Warning: Parameter '{param}' not found in section '{section}'")
                else:
                    print(f"Warning: Section '{section}' not found in settings")
            elif isinstance(value, dict):
                # Handle nested format like {'sac': {'total_timesteps': 10}}
                if hasattr(self, key):
                    section_obj = getattr(self, key)
                    for param, param_value in value.items():
                        if hasattr(section_obj, param):
                            setattr(section_obj, param, param_value)
                            print(f"Applied override: {key}.{param} = {param_value}")
                        else:
                            print(f"Warning: Parameter '{param}' not found in section '{key}'")
                else:
                    print(f"Warning: Section '{key}' not found in settings")
            else:
                # Handle direct top-level parameters
                if hasattr(self, key):
                    setattr(self, key, value)
                    print(f"Applied override: {key} = {value}")
                else:
                    print(f"Warning: Parameter '{key}' not found in settings")


# Global settings instance
settings = Settings()