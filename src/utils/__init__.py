"""Utilities package for FinRL Trading Agent

This package contains utility functions and classes used throughout the application.
"""

from .logging import (
    setup_logging,
    get_logger,
    log_function_call,
    log_performance,
    log_data_quality,
    log_trading_action,
    log_model_metrics,
    log_backtest_results,
    log_error_with_context,
    LoggingContext,
)

log_error = log_error_with_context

__all__ = [
    'setup_logging',
    'get_logger',
    'log_function_call',
    'log_performance',
    'log_data_quality',
    'log_trading_action',
    'log_model_metrics',
    'log_backtest_results',
    'log_error_with_context',
    'log_error',
    'LoggingContext',
]