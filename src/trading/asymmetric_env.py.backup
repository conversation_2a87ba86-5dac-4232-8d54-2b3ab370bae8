import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
import warnings
import os
from utils.logging import get_logger
logger = get_logger(__name__)

try:
    from finrl.meta.env_stock_trading.env_stocktrading import StockTradingEnv
    import gymnasium as gym # Use gymnasium for consistency
    from gymnasium import spaces
except ImportError:
    # Fallback for development/testing
    import gymnasium as gym # Use gymnasium for consistency
    from gymnasium import spaces
    
    class StockTradingEnv_NotInUse(gym.Env):
        """Fallback StockTradingEnv for when FinRL is not available"""
        
        def __init__(self, df, stock_dim, hmax, initial_amount, num_stock_shares,
                     buy_cost_pct, sell_cost_pct, state_space, action_space, 
                     tech_indicator_list, **kwargs):
            super().__init__()
            self.df = df
            self.stock_dim = stock_dim
            self.hmax = hmax
            self.initial_amount = initial_amount
            self.num_stock_shares = num_stock_shares
            self.buy_cost_pct = buy_cost_pct
            self.sell_cost_pct = sell_cost_pct
            self.state_space = state_space
            self.action_space_dim = action_space
            self.tech_indicator_list = tech_indicator_list
            
            # Initialize gym spaces
            self.action_space = spaces.Box(low=-1, high=1, shape=(action_space,), dtype=np.float32)
            self.observation_space = spaces.Box(low=-np.inf, high=np.inf, shape=(state_space,), dtype=np.float32)
            
            # Initialize state variables
            self.day = 0
            self.data = df.loc[self.day, :]
            self.terminal = False
            self.portfolio_value = initial_amount
            self.asset_memory = [initial_amount]
            self.portfolio_return_memory = [0]
            self.actions_memory = []
            self.date_memory = [self.data.date.unique()[0]]
            
        def reset(self):
            """Reset environment to initial state"""
            self.day = 0
            self.data = self.df.loc[self.day, :]
            self.terminal = False
            self.portfolio_value = self.initial_amount
            self.asset_memory = [self.initial_amount]
            self.portfolio_return_memory = [0]
            self.actions_memory = []
            self.date_memory = [self.data.date.unique()[0]]
            return self._get_state()
            
        def step(self, actions):
            """Execute one step in the environment"""
            self.terminal = self.day >= len(self.df.index.unique()) - 1
            
            if self.terminal:
                # Return 5-tuple: observation, reward, terminated, truncated, info
                return self._get_state(), 0, True, False, {}
            
            # Simple step implementation
            self.day += 1
            self.data = self.df.loc[self.day, :]
            
            # Calculate reward (simplified)
            reward = np.random.normal(0, 0.01)  # Placeholder reward
            
            # Update memory
            self.actions_memory.append(actions)
            self.portfolio_value += reward * self.initial_amount
            self.asset_memory.append(self.portfolio_value)
            self.portfolio_return_memory.append(reward)
            self.date_memory.append(self.data.date.unique()[0])
            
            # Return 5-tuple: observation, reward, terminated, truncated, info
            return self._get_state(), reward, self.terminal, False, {}
            
        def _get_state(self):
            """Get current state as NumPy array"""
            # Create a simple state representation
            state = np.zeros(self.state_space, dtype=np.float32)
            
            # Fill with some basic values
            state[0] = self.portfolio_value / self.initial_amount  # Portfolio ratio
            
            # Fill remaining with random values (placeholder)
            if len(state) > 1:
                state[1:] = np.random.normal(0, 0.1, len(state) - 1)
            
            return state.astype(np.float32)

from strategies.asymmetric_strategy import AsymmetricStrategy, AsymmetricConfig
from config.settings import settings
# from utils import get_logger # Keep this for the main logger - replaced by setup_logging call
from utils.logging import setup_logging, get_logger
from loguru import logger as loguru_logger # Import loguru directly for adding sinks

# Initialize logger for this module
root_logger = get_logger(__name__)


class AsymmetricTradingEnv(StockTradingEnv):
    """Enhanced trading environment with asymmetric strategy integration."""
    
    def __init__(
        self,
        df: pd.DataFrame,
        stock_dim: int,
        hmax: int,
        initial_amount: float,
        num_stock_shares: List[int],
        buy_cost_pct: float,
        sell_cost_pct: float,
        state_space: int,
        action_space: int,
        tech_indicator_list: List[str],
        reward_scaling: float = 1e-4,
        asymmetric_config: Optional[AsymmetricConfig] = None,
        enhanced_state_input: bool = False,
        **kwargs
    ):
        """Initialize asymmetric trading environment.
        
        Args:
            df: Market data DataFrame
            stock_dim: Number of stocks
            hmax: Maximum shares per stock
            initial_amount: Initial capital
            num_stock_shares: Initial stock holdings
            buy_cost_pct: Buy transaction cost
            sell_cost_pct: Sell transaction cost
            state_space: State space dimension
            action_space: Action space dimension
            tech_indicator_list: Technical indicators
            reward_scaling: Reward scaling factor
            asymmetric_config: Asymmetric strategy configuration
            **kwargs: Additional arguments, may include log_file_path and log_level
        """
        # Initialize logger for this instance
        # Use get_logger for general class logging, but configure specific sink for workers
        # Setup logging for this environment instance, potentially in a new process
        log_file_path_from_kwargs = kwargs.get('log_file_path')
        log_level_from_kwargs = kwargs.get('log_level', 'INFO') # Default to INFO if not provided

        # Call setup_logging to configure loguru for this specific instance/process
        # setup_logging removes previous handlers, so it's safe to call multiple times if needed by design
        setup_logging(
            level=log_level_from_kwargs,
            log_file=log_file_path_from_kwargs,
            # Pass other relevant params from settings if needed, or use defaults in setup_logging
            # For example, rotation, retention, format_type could come from settings.logging
            rotation=settings.logging.rotation, 
            retention=settings.logging.retention,
            format_type=settings.logging.format, # Corrected attribute name
            enable_console=settings.logging.enable_console_workers # New setting for worker console logs
        )
        
        # Use the globally configured loguru logger after setup
        self.logger = root_logger.bind(name=__name__) # Bind to current module name
        import os # For PID
        pid = os.getpid()
        self.logger.info(f"[PID:{pid}] AsymmetricTradingEnv __init__ (Log Level: {log_level_from_kwargs}, Log File: {log_file_path_from_kwargs}, Console: {settings.logging.enable_console_workers}): Received tech_indicator_list (len: {len(tech_indicator_list)}): {tech_indicator_list}")
        # The setup_logging call above should handle all necessary sink configurations (console and file)
        # based on the provided parameters and settings.logging.enable_console_workers.
        # The redundant loguru_logger.add block has been removed.

        # Harmonize stock_dim with DataFrame content
        actual_tics_in_df = df['tic'].unique()
        actual_stock_dim_from_df = len(actual_tics_in_df)

        if stock_dim != actual_stock_dim_from_df:
            self.logger.warning(
                f"[PID:{pid}] AsymmetricTradingEnv __init__: Provided stock_dim ({stock_dim}) "
                f"does not match unique 'tic's in DataFrame ({actual_stock_dim_from_df}). "
                f"Using DataFrame's unique tic count: {actual_stock_dim_from_df} as stock_dim."
            )
            stock_dim = actual_stock_dim_from_df
        
        self.stock_dim = stock_dim # Store the harmonized stock_dim

        # Initialize asymmetric strategy
        if asymmetric_config is None:
            asymmetric_config = AsymmetricConfig(
                symbols=list(actual_tics_in_df) # Use actual tics from df, length is now harmonized stock_dim
            )
        elif len(asymmetric_config.symbols) != self.stock_dim:
            self.logger.info(
                f"[PID:{pid}] AsymmetricTradingEnv __init__: Adjusting asymmetric_config.symbols. "
                f"Initial count from config ({len(asymmetric_config.symbols)}) did not match unique 'tic's in the environment's DataFrame ({self.stock_dim}). "
                f"This can happen if the DataFrame passed to the environment (e.g., train_df, val_df) results in a different set of unique tickers "
                f"than what was used to create the initial asymmetric_config (e.g., due to date filtering removing all data for some symbols). "
                f"Symbols in asymmetric_config will now be aligned with the {self.stock_dim} unique tickers found in the environment's current DataFrame: {list(actual_tics_in_df)}."
            )
            asymmetric_config.symbols = list(actual_tics_in_df)
        
        self.asymmetric_strategy = AsymmetricStrategy(asymmetric_config)
        self.asymmetric_features_per_stock = 5  # asymmetry_score, rsi, bb_position, momentum, volatility
        # asymmetric_features_size uses the harmonized stock_dim
        asymmetric_features_size = self.asymmetric_features_per_stock * self.stock_dim 

        # Calculate the true original_state_space based on FinRL's structure, using harmonized stock_dim
        true_original_state_space = 1 + (2 * self.stock_dim) + (len(tech_indicator_list) * self.stock_dim)
        
        # Add comprehensive debugging for state dimension calculation
        self.logger.critical(f"[PID:{pid}] STATE DIMENSION BREAKDOWN:")
        self.logger.critical(f"  stock_dim: {self.stock_dim}")
        self.logger.critical(f"  tech_indicator_list length: {len(tech_indicator_list)}")
        self.logger.critical(f"  tech_indicators: {tech_indicator_list}")
        self.logger.critical(f"  calculated true_original_state_space: 1 + (2 * {self.stock_dim}) + ({len(tech_indicator_list)} * {self.stock_dim}) = {true_original_state_space}")
        self.logger.critical(f"  asymmetric_features_per_stock: {self.asymmetric_features_per_stock}")
        self.logger.critical(f"  asymmetric_features_size: {self.asymmetric_features_per_stock} * {self.stock_dim} = {asymmetric_features_size}")
        
        # Determine the state space the DRL agent should be configured with
        # and the state space to initialize the parent class with.
        # Also define self.original_state_space and self.enhanced_state_space authoritatively.
        
        if enhanced_state_input:
            # Agent is intended to use an enhanced state. Parent class uses the original state.
            # The environment calculates its own correct dimensions first.
            self.original_state_space = true_original_state_space
            calculated_asymmetric_env_enhanced_state_space = self.original_state_space + asymmetric_features_size
            
            self.logger.critical(f"[PID:{pid}] ENHANCED STATE MODE:")
            self.logger.critical(f"  original_state_space: {self.original_state_space}")
            self.logger.critical(f"  calculated_enhanced_state_space: {calculated_asymmetric_env_enhanced_state_space}")

            # Validate the input 'state_space' argument (from __init__ args, expected to be an enhanced space)
            # against the environment's own calculation for the enhanced space.
            input_state_space_arg = state_space 
            if input_state_space_arg != calculated_asymmetric_env_enhanced_state_space:
                self.logger.warning( # Changed to warning as we are correcting it.
                    f"[PID:{pid}] AsymmetricTradingEnv STATE SPACE WARNING (enhanced_state_input=True): "
                    f"Input 'state_space' argument ({input_state_space_arg}) is INCONSISTENT with environment's internal calculation. "
                    f"Based on harmonized stock_dim ({self.stock_dim}), tech_indicators, and asymmetric_features_size ({asymmetric_features_size}), "
                    f"the environment's true_original_state_space is {self.original_state_space}, "
                    f"and its calculated enhanced_state_space should be {calculated_asymmetric_env_enhanced_state_space}. "
                    f"USING ENVIRONMENT'S CALCULATED enhanced_state_space ({calculated_asymmetric_env_enhanced_state_space}) for the agent."
                )
            
            self.enhanced_state_space = calculated_asymmetric_env_enhanced_state_space # This is now authoritative for the instance
            parent_init_state_space = self.original_state_space # Parent class gets the original space
            agent_final_state_space = self.enhanced_state_space # Agent gets the enhanced space

        else: # not enhanced_state_input
            # Agent uses original state. Parent class uses original state.
            self.original_state_space = true_original_state_space
            self.enhanced_state_space = self.original_state_space + asymmetric_features_size # Define for completeness
            
            self.logger.critical(f"[PID:{pid}] STANDARD STATE MODE:")
            self.logger.critical(f"  original_state_space: {self.original_state_space}")
            self.logger.critical(f"  enhanced_state_space: {self.enhanced_state_space}")
            
            parent_init_state_space = self.original_state_space
            agent_final_state_space = self.original_state_space
        
        self.logger.critical(f"[PID:{pid}] FINAL STATE SPACE ASSIGNMENTS:")
        self.logger.critical(f"  parent_init_state_space (for super().__init__): {parent_init_state_space}")
        self.logger.critical(f"  agent_final_state_space (for self.state_space): {agent_final_state_space}")
        
        # 'parent_init_state_space' will be passed to super().__init__()
        # 'agent_final_state_space' will be used to set self.state_space AFTER super().__init__()
        
        # Initialize parent class with the correctly calculated parent_state_space
        # Convert scalar buy/sell cost percentages to lists if they are not already
        if isinstance(buy_cost_pct, float):
            buy_cost_pct_list = [buy_cost_pct] * stock_dim
        else:
            buy_cost_pct_list = buy_cost_pct

        if isinstance(sell_cost_pct, float):
            sell_cost_pct_list = [sell_cost_pct] * stock_dim
        else:
            sell_cost_pct_list = sell_cost_pct

        super().__init__(
            df=df,
            stock_dim=stock_dim,
            hmax=hmax,
            initial_amount=initial_amount,
            num_stock_shares=num_stock_shares,
            buy_cost_pct=buy_cost_pct_list,  # Pass the list
            sell_cost_pct=sell_cost_pct_list, # Pass the list
            state_space=parent_init_state_space, # Use the state space determined for parent class initialization
            action_space=action_space,
            tech_indicator_list=tech_indicator_list,
            reward_scaling=reward_scaling
            # Removed unexpected kwargs: initial_buy, daily_information_cols, print_verbosity, random_exploration, make_plots, cache_indicator_data
        )

        # Set the environment's state_space to what the agent will actually use.
        # This might be the original_state_space or enhanced_state_space based on 'enhanced_state_input'.
        self.state_space = agent_final_state_space
        
        # Override observation_space to reflect the state space the agent will observe.
        # gymnasium.spaces is imported at the top of the file now
        self.observation_space = spaces.Box(
            low=-np.inf, 
            high=np.inf, 
            shape=(self.state_space,), # Use the final agent state_space for the shape
            dtype=np.float32
        )
        
        # self.logger = get_logger(__name__) # This was a duplicate, logger already initialized
        self.asymmetric_cache = {}  # Cache for asymmetric calculations
        
        self.logger.info(
            f"AsymmetricTradingEnv initialized: stock_dim={stock_dim}, "
            f"enhanced_state_space={self.enhanced_state_space}, "
            f"asymmetric_features_per_stock={self.asymmetric_features_per_stock}"
        )
        
        # Ensure VIX_Regime is converted to numeric in the full DataFrame
        self._convert_vix_regime_to_numeric()
    
    def _convert_vix_regime_to_numeric(self):
        """Convert VIX_Regime column to numeric values if it exists."""
        if hasattr(self, 'df') and self.df is not None and 'VIX_Regime' in self.df.columns:
            regime_mapping = {
                'low_volatility': 0.0, 
                'moderate_volatility': 1.0, 
                'high_volatility': 2.0, 
                'extreme_volatility': 3.0,
                'medium': 1.0,  # fallback default
                'Normal': 1.0   # handle test data
            }
            # Convert to numeric, handling both string and already numeric values
            if self.df['VIX_Regime'].dtype == 'object':
                self.df['VIX_Regime'] = self.df['VIX_Regime'].map(regime_mapping).fillna(1.0).astype(np.float32)
                self.logger.debug(f"Converted VIX_Regime column from string to numeric in full DataFrame")
    
    def _update_state(self):
        """Override parent's _update_state to ensure VIX_Regime conversion."""
        # Ensure VIX_Regime is numeric before parent processes it
        if hasattr(self, 'data') and self.data is not None and 'VIX_Regime' in self.data.columns:
            regime_mapping = {
                'low_volatility': 0.0, 
                'moderate_volatility': 1.0, 
                'high_volatility': 2.0, 
                'extreme_volatility': 3.0,
                'medium': 1.0,  # fallback default
                'Normal': 1.0   # handle test data
            }
            if self.data['VIX_Regime'].dtype == 'object':
                self.data['VIX_Regime'] = self.data['VIX_Regime'].map(regime_mapping).fillna(1.0).astype(np.float32)
                self.logger.debug(f"Converted VIX_Regime in current data slice before parent _update_state")
        
        # Ensure self.state is initialized before calling parent's _update_state
        # The parent's _update_state method tries to access self.state[0], so we need to ensure it exists
        if not hasattr(self, 'state') or self.state is None:
            self.logger.warning("_update_state: self.state is None, initializing with zeros")
            self.state = np.zeros(self.original_state_space, dtype=np.float32)
        
        # Call parent's _update_state
        super()._update_state()
    
    def _get_state(self) -> np.ndarray:
        """Get enhanced state with asymmetric features.
        
        Returns:
            Enhanced state array including asymmetric signals
        """
        import os # For PID
        pid = os.getpid()

        self.logger.critical(f"[PID:{pid}] _get_state: STARTING STATE CALCULATION")
        self.logger.critical(f"[PID:{pid}] _get_state: original_state_space={self.original_state_space}, enhanced_state_space={self.enhanced_state_space}")
        self.logger.critical(f"[PID:{pid}] _get_state: stock_dim={self.stock_dim}, asymmetric_features_per_stock={self.asymmetric_features_per_stock}")

        # Get base state from parent class state attribute
        parent_state_raw = None
        if hasattr(self, 'state') and self.state is not None:
            parent_state_raw = self.state
            self.logger.critical(f"[PID:{pid}] _get_state: Parent self.state found - type: {type(parent_state_raw)}, length/shape: {len(parent_state_raw) if isinstance(parent_state_raw, list) else parent_state_raw.shape if hasattr(parent_state_raw, 'shape') else 'N/A'}")
            base_state = np.array(self.state, dtype=np.float32) # Ensure float32 early
        else:
            # Fallback: update state first, then get it
            self.logger.critical(f"[PID:{pid}] _get_state: Parent self.state is None or not found. Attempting self._update_state().")
            self._update_state() # This method should set self.state in the parent
            if hasattr(self, 'state') and self.state is not None:
                parent_state_raw = self.state
                self.logger.critical(f"[PID:{pid}] _get_state: Parent self.state after _update_state() - type: {type(parent_state_raw)}, length/shape: {len(parent_state_raw) if isinstance(parent_state_raw, list) else parent_state_raw.shape if hasattr(parent_state_raw, 'shape') else 'N/A'}")
                base_state = np.array(self.state, dtype=np.float32)
            else:
                self.logger.critical(f"[PID:{pid}] _get_state: Parent self.state still None after _update_state(). Falling back to zeros for base_state.")
                base_state = np.zeros(self.original_state_space, dtype=np.float32)
        
        self.logger.critical(f"[PID:{pid}] _get_state: base_state shape: {base_state.shape}, dtype: {base_state.dtype}")

        # Calculate asymmetric features
        asymmetric_features = self._calculate_asymmetric_features() # Already returns np.float32
        self.logger.critical(f"[PID:{pid}] _get_state: asymmetric_features shape: {asymmetric_features.shape}, dtype: {asymmetric_features.dtype}")

        # Pre-concatenation check
        expected_len_base = self.original_state_space
        expected_len_asym = self.asymmetric_features_per_stock * self.stock_dim
        
        self.logger.critical(f"[PID:{pid}] _get_state: DIMENSION VALIDATION:")
        self.logger.critical(f"  expected_len_base (original_state_space): {expected_len_base}")
        self.logger.critical(f"  actual base_state length: {base_state.shape[0]}")
        self.logger.critical(f"  expected_len_asym: {expected_len_asym}")
        self.logger.critical(f"  actual asymmetric_features length: {asymmetric_features.shape[0]}")
        self.logger.critical(f"  total expected: {expected_len_base + expected_len_asym}")
        self.logger.critical(f"  enhanced_state_space: {self.enhanced_state_space}")
        
        if base_state.shape[0] != expected_len_base:
            self.logger.critical(f"[PID:{pid}] _get_state: CRITICAL MISMATCH! base_state length {base_state.shape[0]} != expected original_state_space {expected_len_base}. Parent state: {parent_state_raw}")
        if asymmetric_features.shape[0] != expected_len_asym:
             self.logger.critical(f"[PID:{pid}] _get_state: CRITICAL MISMATCH! asymmetric_features length {asymmetric_features.shape[0]} != expected {expected_len_asym}.")
        
        if base_state.shape[0] + asymmetric_features.shape[0] != self.enhanced_state_space:
            self.logger.critical(
                f"[PID:{pid}] _get_state: CRITICAL DIMENSION MISMATCH BEFORE CONCAT! "
                f"base_state ({base_state.shape[0]}) + asymmetric_features ({asymmetric_features.shape[0]}) = {base_state.shape[0] + asymmetric_features.shape[0]} "
                f"!= enhanced_state_space ({self.enhanced_state_space})"
            )
            # Attempt to pad/truncate base_state if it's the culprit, to prevent crash, but log error
            if base_state.shape[0] != self.original_state_space:
                self.logger.critical(f"[PID:{pid}] _get_state: Adjusting base_state from {base_state.shape[0]} to {self.original_state_space} due to mismatch.")
                new_base_state = np.zeros(self.original_state_space, dtype=np.float32)
                common_len = min(base_state.shape[0], self.original_state_space)
                new_base_state[:common_len] = base_state[:common_len]
                base_state = new_base_state
                self.logger.critical(f"[PID:{pid}] _get_state: Adjusted base_state shape: {base_state.shape}")

        # Combine base state with asymmetric features
        enhanced_state = np.concatenate([base_state, asymmetric_features]).astype(np.float32) # Ensure final dtype
        
        self.logger.critical(f"[PID:{pid}] _get_state: FINAL RESULT - enhanced_state shape: {enhanced_state.shape}, dtype: {enhanced_state.dtype}")

        if enhanced_state.shape[0] != self.enhanced_state_space:
            self.logger.critical(
                f"[PID:{pid}] _get_state: FINAL CRITICAL SHAPE MISMATCH! enhanced_state.shape[0] ({enhanced_state.shape[0]}) "
                f"!= self.enhanced_state_space ({self.enhanced_state_space})."
            )
        
        return enhanced_state
    
    def _calculate_asymmetric_features(self) -> np.ndarray:
        """Calculate asymmetric strategy features for current state.
        
        Returns:
            Array of asymmetric features for all stocks
        """
        features = []
        current_data = self._get_current_market_data() # Relies on self.data and self.day
        
        if current_data is None or current_data.empty: # Added empty check
            self.logger.warning(f"[PID:{os.getpid()}] _calculate_asymmetric_features: No current market data available for day {getattr(self, 'day', 'N/A')}. Returning zeros.")
            return np.zeros(self.stock_dim * self.asymmetric_features_per_stock, dtype=np.float32)
        
        # Ensure self.data is the full DataFrame for _extract_symbol_price_series if it relies on historical data
        # self.data should be df.loc[self.day,:] for current day's info, but history might need full df access or a lookback_df
        # For now, assuming _extract_symbol_price_series can work with self.df and current self.day

        tics_in_current_day_data = self.data.tic.unique() # Use self.data which is df.loc[self.day, :]
        if len(tics_in_current_day_data) == 0 and self.stock_dim > 0:
             self.logger.warning(f"[PID:{os.getpid()}] _calculate_asymmetric_features: self.data for day {self.day} has no tics, but stock_dim={self.stock_dim}")
        
        # Iterate over the symbols expected (from initialization, which should match the columns in df if processed correctly)
        # If df['tic'].unique() was used at init, these are the symbols we expect.
        # However, self.data.tic.unique() at a specific day might be different if data is sparse.
        # The original code used self.data.tic.unique() which might be problematic if not all stocks have data on a given day.
        # Iterate over the symbols defined in the asymmetric_strategy's config.
        # After __init__ harmonization, self.asymmetric_strategy.config.symbols
        # will be consistent with self.stock_dim and self.df['tic'].unique().
        expected_symbols = self.asymmetric_strategy.config.symbols
        
        if len(expected_symbols) != self.stock_dim:
            # This should ideally not happen due to __init__ harmonization.
            self.logger.critical(
                f"[PID:{os.getpid()}] _calculate_asymmetric_features: CRITICAL MISMATCH post-init! "
                f"len(asymmetric_strategy.config.symbols) ({len(expected_symbols)}) != self.stock_dim ({self.stock_dim}). "
                f"This indicates a severe bug. Proceeding with expected_symbols, but results may be incorrect."
            )
            # Fallback: if expected_symbols is shorter, features will be shorter. If longer, it will use more symbols than stock_dim implies for space.
            # The padding logic below will attempt to correct the final array size to stock_dim * features_per_stock.

        for symbol in expected_symbols:
            try:
                symbol_price_series = self._extract_symbol_price_series(symbol)
                
                if symbol_price_series is None or len(symbol_price_series) < 20:
                    self.logger.warning(f"[PID:{os.getpid()}] _calculate_asymmetric_features: Insufficient data for {symbol} on day {self.day} (len: {len(symbol_price_series) if symbol_price_series is not None else 0}). Using default features.")
                    stock_features = [0.0, 50.0, 0.5, 0.0, 0.01]
                else:
                    asymmetry_score = self.asymmetric_strategy._calculate_asymmetry_score(symbol_price_series)
                    indicators = self.asymmetric_strategy._calculate_indicators(symbol_price_series)
                    rsi = indicators.get('rsi', 50.0)
                    bb_position = indicators.get('bb_position', 0.5)
                    momentum = indicators.get('momentum', 0.0)
                    volatility = indicators.get('volatility', 0.01)
                    stock_features = [
                        asymmetry_score, rsi / 100.0, bb_position,
                        np.tanh(momentum), min(volatility * 10, 1.0)
                    ]
                features.extend(stock_features)
            except Exception as e:
                self.logger.error(f"[PID:{os.getpid()}] _calculate_asymmetric_features: Error calculating features for {symbol} on day {self.day}: {e}. Using default features.", exc_info=True)
                features.extend([0.0, 0.5, 0.5, 0.0, 0.01]) # Default values on error
        
        # Ensure the final features array matches the expected size based on self.stock_dim
        expected_feature_length = self.stock_dim * self.asymmetric_features_per_stock
        if len(features) != expected_feature_length:
            self.logger.error(
                f"[PID:{os.getpid()}] _calculate_asymmetric_features: Final features length mismatch. "
                f"Expected {expected_feature_length}, got {len(features)}. Padding/truncating."
            )
            # Pad with zeros if there's a mismatch, or truncate if too long.
            if len(features) < expected_feature_length:
                features.extend([0.0] * (expected_feature_length - len(features)))
            else: # len(features) > expected_feature_length
                features = features[:expected_feature_length]

        return np.array(features, dtype=np.float32)

    def reset(self, *, seed: Optional[int] = None, options: Optional[dict] = None) -> Tuple[np.ndarray, dict]:
        """Reset the environment and return the enhanced state."""
        pid = os.getpid()
        self.logger.info(f"[PID:{pid}] reset: Called. Current day: {self.day if hasattr(self, 'day') else 'N/A'}")
        
        self.asymmetric_cache.clear() # Clear asymmetric cache

        # Call parent's reset logic.
        # FinRL's StockTradingEnv.reset() sets self.state and returns it.
        # It might return just obs, or (obs, info) if updated for Gymnasium
        
        parent_obs = None
        parent_info = {}

        # Standard way to call parent reset in gymnasium
        # super().reset() will set the parent's internal state (e.g., self.state)
        # It also updates self.day to 0 and self.data to df.loc[0,:]
        if hasattr(super(), 'reset') and callable(getattr(super(), 'reset')):
            try:
                # Attempt standard gymnasium call
                # FinRL's base reset returns: self.state, info
                # However, it seems the parent's `reset` in FinRL just returns `self.state` (an array)
                # And the `_get_info()` is an empty dict, so info comes from the wrapper.
                # Let's try calling it without expecting two return values first.
                
                # The FinRL StockTradingEnv reset method looks like:
                # def reset(self, *, seed=None, options=None):
                #   ...
                #   self.state = self._initiate_state()
                #   return self.state, self.get_info()
                # So it *should* return two values.

                raw_reset_output = super().reset(seed=seed, options=options)
                
                if isinstance(raw_reset_output, tuple) and len(raw_reset_output) == 2:
                    parent_obs, parent_info = raw_reset_output
                    self.logger.debug(f"[PID:{pid}] reset: super().reset() returned (obs, info). Obs type: {type(parent_obs)}, Info type: {type(parent_info)}")
                elif isinstance(raw_reset_output, np.ndarray): # If it only returned state
                    parent_obs = raw_reset_output
                    parent_info = {} # Create empty info
                    self.logger.debug(f"[PID:{pid}] reset: super().reset() returned only obs (np.ndarray). Obs type: {type(parent_obs)}")
                else: # Unexpected return type
                    self.logger.error(f"[PID:{pid}] reset: super().reset() returned unexpected type: {type(raw_reset_output)}. Attempting to use as obs.")
                    parent_obs = raw_reset_output # Hope for the best
                    parent_info = {}

            except TypeError as e:
                self.logger.warning(f"[PID:{pid}] reset: TypeError calling super().reset(seed, options): {e}. Trying super().reset().")
                # Fallback if parent reset has different signature (e.g. older gym or FinRL version)
                parent_obs = super().reset() # This sets self.state, self.day, self.data
                parent_info = {} # Default info
                if isinstance(parent_obs, tuple) and len(parent_obs) == 2: # If this fallback also returns two things
                    parent_obs, parent_info = parent_obs

            # After super().reset(), self.state, self.day, and self.data are set by the parent.
            # self.day should be 0. self.data should be self.df.loc[0,:]
            # self.state should be the initial state from parent.
            # _get_state will use these to build the enhanced state.

        else:
            # Should not happen if inheriting from gym.Env correctly
            self.logger.error(f"[PID:{pid}] reset: super().reset() method not found or not callable.")
            # In this case, we might need to manually initialize parent state components if possible
            # or raise an error. For now, create a dummy observation.
            parent_obs = np.zeros(self.original_state_space, dtype=np.float32)
            parent_info = {'error': 'super().reset() not available'}
        
        # Ensure parent_obs (which is self.state from parent) is correctly formatted if needed
        if not isinstance(parent_obs, np.ndarray) and parent_obs is not None:
             self.logger.warning(f"[PID:{pid}] reset: parent_obs from super().reset() is not np.ndarray (type: {type(parent_obs)}). Converting.")
             parent_obs = np.array(parent_obs, dtype=np.float32)
        elif parent_obs is None: # If super().reset() somehow failed to return an observation
            self.logger.error(f"[PID:{pid}] reset: parent_obs is None after super().reset(). Using zeros for base_state calculation.")
            # self.state might also be None, _get_state will handle this by creating zeros.

        enhanced_state = self._get_state() # This now uses the self.state set by parent's reset
        
        if not isinstance(parent_info, dict):
            parent_info = {}

        self.logger.info(f"[PID:{pid}] reset: Completed. Enhanced state shape: {enhanced_state.shape}. Day is now {self.day}. self.data is for day {self.data.date.unique()[0] if hasattr(self.data, 'date') else 'N/A'}")
        return enhanced_state, parent_info

    def step(self, actions: np.ndarray) -> tuple[np.ndarray, float, bool, bool, dict]:
        """Perform a step in the environment with robust data validation and error handling.

        Args:
            actions (np.ndarray): The actions to take.

        Returns:
            tuple[np.ndarray, float, bool, bool, dict]: The new state, reward, terminated, truncated, and info.
        """
        pid = os.getpid() 
        self.logger.debug(f"[PID:{pid}] step: Called. Day: {self.day}, Actions: {actions}")

        # Pre-step data validation - inspired by alpaca_trading_agent patterns
        if not self._validate_environment_state():
            self.logger.error(f"[PID:{pid}] step: Environment state validation failed before super().step()")
            return self._create_fallback_response("Environment state validation failed")

        super_step_return = None
        # Initialize defaults for safety, matching Gymnasium 5-tuple
        next_state_base, reward, terminated, truncated, info = np.zeros(self.original_state_space, dtype=np.float32), 0.0, True, False, {}

        try:
            # Enhanced data preprocessing before calling super().step()
            if hasattr(self, 'df') and self.df is not None:
                self._preprocess_current_data()
            
            super_step_return = super().step(actions)
            
            if super_step_return is None:
                self.logger.critical(f"[PID:{pid}] CRITICAL: super().step(actions) returned None! Day: {self.day}, Actions: {actions}")
                return self._create_fallback_response("super().step() returned None", actions)
            else:
                # FinRL's StockTradingEnv.step returns 5 values: state, reward, terminated, truncated, info
                if len(super_step_return) == 5:
                    next_state_base, reward, terminated, truncated, info = super_step_return
                    self.logger.debug(f"[PID:{pid}] step: Successfully unpacked 5 items from super().step().")
                elif len(super_step_return) == 4: # Compatibility for older gym-like (obs, rew, done, info)
                    self.logger.warning(f"[PID:{pid}] step: super().step() returned 4 items. Assuming (obs, rew, done, info) and setting truncated=False.")
                    next_state_base_temp, reward_temp, done_temp, info_temp = super_step_return
                    next_state_base = next_state_base_temp
                    reward = reward_temp
                    terminated = done_temp
                    truncated = False # Assume not truncated if only 'done' is provided
                    info = info_temp
                else:
                    self.logger.error(f"[PID:{pid}] step: super().step() returned an unexpected number of items: {len(super_step_return)}. Output: {super_step_return}")
                    return self._create_fallback_response(f"super().step() returned {len(super_step_return)} items", actions)

        except TypeError as e: # Handles cases where unpacking fails due to wrong number of items
            self.logger.error(f"[PID:{pid}] TypeError during unpacking super().step() return: {e}. Return: {super_step_return}, Day: {self.day}, Actions: {actions}")
            return self._create_fallback_response(f"TypeError unpacking super().step(): {e}", actions, super_step_return)
        except Exception as e:
            self.logger.error(f"[PID:{pid}] Exception during super().step(actions) or its unpacking: {e}. Day: {self.day}, Actions: {actions}", exc_info=True)
            return self._create_fallback_response(f"Exception in super().step(): {e}", actions, super_step_return)

        self.logger.debug(f"[PID:{pid}] step: After super().step() call/fallback - Day: {self.day}, Terminated: {terminated}, Truncated: {truncated}, Reward: {reward}")
        self.logger.debug(f"[PID:{pid}] step: next_state_base from super type: {type(next_state_base)}, shape: {next_state_base.shape if hasattr(next_state_base, 'shape') else 'N/A'}")
        
        # Enhanced state validation and sanitization - inspired by alpaca patterns
        next_state_base = self._validate_and_sanitize_state(next_state_base, pid)
        if next_state_base is None:
            return self._create_fallback_response("State validation failed", actions)

        # Post-step data validation
        if not terminated and not self._validate_post_step_data():
            self.logger.warning(f"[PID:{pid}] step: Post-step data validation failed, but continuing")
            # Don't terminate here, just log the warning
        # _get_state() relies on self.day and self.data being current for the state it's calculating.
        # In FinRL, if not terminated, self.day is incremented, and self.data and self.state are updated *before* returning.
        # So, self.state (which is next_state_base here) and self.data should correspond to the *new* day.

        enhanced_state = self._get_state() # This will use the self.state set by super().step()
                                          # and the self.data for the current self.day

        self.logger.debug(f"[PID:{pid}] step: enhanced_state shape: {enhanced_state.shape if hasattr(enhanced_state, 'shape') else 'N/A'}, dtype: {enhanced_state.dtype if hasattr(enhanced_state, 'dtype') else 'N/A'}")

        # Check for NaN or Inf values in enhanced_state, as Box.contains() can be sensitive
        if np.any(np.isnan(enhanced_state)) or np.any(np.isinf(enhanced_state)):
            self.logger.warning(f"[PID:{pid}] step: enhanced_state contains NaN or Inf values before observation_space check. Shape: {enhanced_state.shape}, Dtype: {enhanced_state.dtype}. Replacing them.")
            # Option 1: Replace NaNs/Infs (e.g., with 0 or a large finite number)
            enhanced_state = np.nan_to_num(enhanced_state, nan=0.0, posinf=1e9, neginf=-1e9) 
            self.logger.info(f"[PID:{pid}] step: Replaced NaN/Inf in enhanced_state. New min: {np.min(enhanced_state)}, new max: {np.max(enhanced_state)}")
            # Option 2: Log and allow termination or specific handling if this is unexpected
            # For now, just logging, the critical error below will still trigger if contains() fails.

        if not self.observation_space.contains(enhanced_state):
            self.logger.critical(
                f"[PID:{pid}] step: CRITICAL - Final enhanced_state shape {enhanced_state.shape} or dtype {enhanced_state.dtype} "
                f"is not contained in observation_space {self.observation_space}. "
                f"Expected shape: {self.observation_space.shape}, Expected dtype: {self.observation_space.dtype}. "
                f"Original state space: {self.original_state_space}, Asymmetric features: {self.asymmetric_features_per_stock * self.stock_dim}, Enhanced: {self.enhanced_state_space}"
            )
            # Handle mismatch: pad or truncate, or terminate
            # For now, we'll return it and let a wrapper or agent handle it, but this is a critical warning.
            # If it crashes, this is the place to debug state construction.
            # One possible fix if shapes are off by a small, consistent amount:
            if enhanced_state.shape[0] != self.enhanced_state_space:
                 current_base_state_len = self.original_state_space # from self.state
                 asym_feats_len = (self.asymmetric_features_per_stock * self.stock_dim)
                 self.logger.error(f"Component lengths: current_base_state_len={current_base_state_len}, asym_feats_len={asym_feats_len}. Sum={current_base_state_len+asym_feats_len}")
                 # If it's a simple padding/truncation issue:
                 # temp_state = np.zeros(self.enhanced_state_space, dtype=np.float32)
                 # common_len = min(len(enhanced_state), self.enhanced_state_space)
                 # temp_state[:common_len] = enhanced_state[:common_len]
                 # enhanced_state = temp_state

        if not isinstance(info, dict): info = {} # Ensure info is a dict

        # Apply asymmetric reward shaping if desired (original code had _calculate_reward calling super and then shaping)
        # The reward from super().step() is already the "base" reward.
        shaped_reward = self._apply_asymmetric_reward_shaping(float(reward))
        
        self.logger.debug(f"[PID:{pid}] step: FINAL RETURN - Enhanced State Shape: {enhanced_state.shape}, Shaped Reward: {shaped_reward}, Terminated: {terminated}, Truncated: {truncated}, Info Keys: {list(info.keys())}")
        return enhanced_state, shaped_reward, bool(terminated), bool(truncated), info
    
    def _get_current_market_data(self) -> Optional[pd.DataFrame]:
        """Get current market data slice for the current self.day.
        
        Returns:
            Current market data DataFrame slice or None
        """
        pid = os.getpid()
        if not hasattr(self, 'day'):
            self.logger.warning(f"[PID:{pid}] _get_current_market_data: 'day' attribute not found.")
            return None
        if not hasattr(self, 'df') or self.df is None: # Changed from self.data to self.df
            self.logger.warning(f"[PID:{pid}] _get_current_market_data: 'df' (full data) attribute not found or is None.")
            return None

        # self.data is already df.loc[self.day,:] as per FinRL's step/reset logic.
        # So, self.data IS the current market data for all symbols on that day.
        if hasattr(self, 'data') and self.data is not None:
            # Ensure VIX_Regime is properly converted to numeric if present
            data = self.data.copy()
            if 'VIX_Regime' in data.columns:
                # Convert VIX_Regime strings to numeric values
                regime_mapping = {
                    'low_volatility': 0.0, 
                    'moderate_volatility': 1.0, 
                    'high_volatility': 2.0, 
                    'extreme_volatility': 3.0,
                    'medium': 1.0,  # fallback default
                    'Normal': 1.0   # handle test data
                }
                # Convert to numeric, handling both string and already numeric values
                if data['VIX_Regime'].dtype == 'object':
                    data['VIX_Regime'] = data['VIX_Regime'].map(regime_mapping).fillna(1.0).astype(np.float32)
                    self.logger.debug(f"[PID:{pid}] _get_current_market_data: Converted VIX_Regime from string to numeric")
            # self.logger.debug(f"[PID:{pid}] _get_current_market_data: Returning processed data for day {self.day}. Shape: {data.shape}")
            return data 
        else: # Fallback if self.data is somehow not set
            self.logger.warning(f"[PID:{pid}] _get_current_market_data: self.data is None for day {self.day}. Trying to slice self.df.")
            try:
                # This part of the original logic was a bit confusing.
                # FinRL's StockTradingEnv sets self.data = self.df.loc[self.day, :]
                # So, self.data IS the data for the current day.
                # The old logic with `self.data.index.unique()[self.day]` seemed to imply self.data was the *entire* dataframe.
                # If self.data is already the slice for the current day, just return it.
                # If it's not, then we need to slice self.df. Assuming self.data is correctly set by parent.
                if self.day < len(self.df.index.unique()): # Check if self.day is a valid index for the entire df's timeline
                    # This original logic might be flawed if df index is not simple range.
                    # current_date_identifier = self.df.index.unique()[self.day] # This assumes df.index gives daily unique identifiers
                    # return self.df[self.df.index == current_date_identifier]
                    # Safer: rely on self.data being set correctly by parent env.
                    # If self.data is not set, then something is wrong with parent's state update.
                    self.logger.error(f"[PID:{pid}] _get_current_market_data: self.data is None, and fallback to slicing self.df by day index is risky. Returning None.")
                    return None # Should not happen if parent works.

            except IndexError:
                self.logger.warning(f"[PID:{pid}] _get_current_market_data: Day {self.day} is out of bounds for self.df.index.unique().")
                return None
            except Exception as e:
                self.logger.error(f"[PID:{pid}] _get_current_market_data: Error accessing data for day {self.day}: {e}")
                return None
        return None # Should be covered by self.data return or previous error returns

    def _validate_environment_state(self) -> bool:
        """Validate the current environment state before step execution.
        
        Returns:
            bool: True if environment state is valid, False otherwise
        """
        pid = os.getpid()
        
        # Check basic attributes
        if not hasattr(self, 'day'):
            self.logger.error(f"[PID:{pid}] _validate_environment_state: Missing 'day' attribute")
            return False
            
        if not hasattr(self, 'df') or self.df is None:
            self.logger.error(f"[PID:{pid}] _validate_environment_state: Missing or None 'df' attribute")
            return False
            
        # Check if day is within valid range
        try:
            unique_days = len(self.df.index.unique())
            if self.day < 0 or self.day >= unique_days:
                self.logger.error(f"[PID:{pid}] _validate_environment_state: Day {self.day} out of range [0, {unique_days-1}]")
                return False
        except Exception as e:
            self.logger.error(f"[PID:{pid}] _validate_environment_state: Error checking day range: {e}")
            return False
            
        # Check data integrity
        if hasattr(self, 'data') and self.data is not None:
            # Check for required columns
            required_cols = ['tic', 'close']
            missing_cols = [col for col in required_cols if col not in self.data.columns]
            if missing_cols:
                self.logger.error(f"[PID:{pid}] _validate_environment_state: Missing required columns: {missing_cols}")
                return False
                
            # Check for NaN values in critical columns
            critical_cols = ['close', 'open', 'high', 'low', 'volume']
            for col in critical_cols:
                if col in self.data.columns and self.data[col].isna().any():
                    self.logger.warning(f"[PID:{pid}] _validate_environment_state: NaN values found in {col} column")
                    # Don't fail validation, but log warning
                    
        return True
    
    def _preprocess_current_data(self) -> None:
        """Preprocess current data to handle missing values and ensure data quality.
        Inspired by alpaca_trading_agent preprocessing patterns.
        """
        pid = os.getpid()
        
        if not hasattr(self, 'data') or self.data is None:
            self.logger.warning(f"[PID:{pid}] _preprocess_current_data: No current data to preprocess")
            return
            
        try:
            # Forward fill missing values (alpaca pattern)
            if self.data.isna().any().any():
                self.logger.debug(f"[PID:{pid}] _preprocess_current_data: Forward filling missing values")
                self.data = self.data.ffill()
                
                # Fill remaining NaNs with 0 (alpaca pattern)
                if self.data.isna().any().any():
                    self.logger.debug(f"[PID:{pid}] _preprocess_current_data: Filling remaining NaNs with 0")
                    self.data = self.data.fillna(0)
                    
            # Handle VIX_Regime conversion (from alpaca patterns)
            if 'VIX_Regime' in self.data.columns:
                regime_mapping = {
                    'low_volatility': 0.0, 
                    'moderate_volatility': 1.0, 
                    'high_volatility': 2.0, 
                    'extreme_volatility': 3.0,
                    'medium': 1.0,  # fallback default
                    'Normal': 1.0   # handle test data
                }
                
                if self.data['VIX_Regime'].dtype == 'object':
                    self.data['VIX_Regime'] = self.data['VIX_Regime'].map(regime_mapping).fillna(1.0).astype(np.float32)
                    self.logger.debug(f"[PID:{pid}] _preprocess_current_data: Converted VIX_Regime to numeric")
                    
            # Sanitize infinite values
            numeric_cols = self.data.select_dtypes(include=[np.number]).columns
            for col in numeric_cols:
                if np.isinf(self.data[col]).any():
                    self.logger.warning(f"[PID:{pid}] _preprocess_current_data: Infinite values found in {col}, replacing")
                    self.data[col] = np.where(np.isinf(self.data[col]), 
                                            np.where(self.data[col] > 0, 1e9, -1e9), 
                                            self.data[col])
                    
        except Exception as e:
            self.logger.error(f"[PID:{pid}] _preprocess_current_data: Error during preprocessing: {e}", exc_info=True)
    
    def _validate_and_sanitize_state(self, state: Any, pid: int) -> Optional[np.ndarray]:
        """Validate and sanitize state data.
        
        Args:
            state: State data to validate
            pid: Process ID for logging
            
        Returns:
            Validated and sanitized state array or None if validation fails
        """
        try:
            # Ensure state is a numpy array of the correct dtype
            if not isinstance(state, np.ndarray):
                self.logger.warning(f"[PID:{pid}] _validate_and_sanitize_state: State is not ndarray (type: {type(state)}). Converting.")
                state = np.array(state, dtype=np.float32)
                
            # Sanitize NaN and infinite values (alpaca pattern)
            if np.any(np.isnan(state)) or np.any(np.isinf(state)):
                self.logger.warning(f"[PID:{pid}] _validate_and_sanitize_state: State contains NaN/Inf values. Sanitizing.")
                state = np.nan_to_num(state, nan=0.0, posinf=1e9, neginf=-1e9)
                self.logger.info(f"[PID:{pid}] _validate_and_sanitize_state: Sanitized state. New min: {np.min(state)}, new max: {np.max(state)}")
                
            # Ensure correct shape
            if state.shape[0] != self.original_state_space:
                self.logger.error(f"[PID:{pid}] _validate_and_sanitize_state: State shape mismatch. Expected: {self.original_state_space}, Got: {state.shape[0]}")
                # Try to fix by padding or truncating
                if state.shape[0] < self.original_state_space:
                    # Pad with zeros
                    padded_state = np.zeros(self.original_state_space, dtype=np.float32)
                    padded_state[:state.shape[0]] = state
                    state = padded_state
                    self.logger.warning(f"[PID:{pid}] _validate_and_sanitize_state: Padded state to correct size")
                else:
                    # Truncate
                    state = state[:self.original_state_space]
                    self.logger.warning(f"[PID:{pid}] _validate_and_sanitize_state: Truncated state to correct size")
                    
            return state.astype(np.float32)
            
        except Exception as e:
            self.logger.error(f"[PID:{pid}] _validate_and_sanitize_state: Error during state validation: {e}", exc_info=True)
            return None
    
    def _validate_post_step_data(self) -> bool:
        """Validate data consistency after step execution.
        
        Returns:
            bool: True if post-step data is valid, False otherwise
        """
        pid = os.getpid()
        
        try:
            # Check if data and day are consistent
            if hasattr(self, 'data') and self.data is not None and hasattr(self, 'day'):
                # Verify data corresponds to current day
                if 'date' in self.data.columns:
                    unique_dates = self.df['date'].unique() if hasattr(self, 'df') and self.df is not None else []
                    if len(unique_dates) > self.day:
                        expected_date = unique_dates[self.day]
                        actual_dates = self.data['date'].unique()
                        if len(actual_dates) > 0 and actual_dates[0] != expected_date:
                            self.logger.warning(f"[PID:{pid}] _validate_post_step_data: Date mismatch. Expected: {expected_date}, Got: {actual_dates[0]}")
                            return False
                            
            return True
            
        except Exception as e:
            self.logger.error(f"[PID:{pid}] _validate_post_step_data: Error during post-step validation: {e}")
            return False
    
    def _create_fallback_response(self, error_msg: str, actions: Optional[np.ndarray] = None, 
                                super_return: Any = None) -> tuple[np.ndarray, float, bool, bool, dict]:
        """Create a fallback response when step execution fails.
        
        Args:
            error_msg: Error message describing the failure
            actions: Actions that were attempted (optional)
            super_return: Return value from super().step() (optional)
            
        Returns:
            Fallback response tuple
        """
        pid = os.getpid()
        
        # Create fallback state
        fallback_state = np.zeros(self.original_state_space, dtype=np.float32)
        
        # Create enhanced fallback state
        try:
            # Store original state for restoration
            original_state = getattr(self, 'state', None)
            
            # Temporarily set state for _get_state() call
            self.state = fallback_state
            enhanced_state = self._get_state()
            
            # Restore original state
            if original_state is not None:
                self.state = original_state
                
        except Exception as e:
            self.logger.error(f"[PID:{pid}] _create_fallback_response: Error creating enhanced fallback state: {e}")
            # Create basic enhanced state
            enhanced_state = np.zeros(self.enhanced_state_space, dtype=np.float32)
        
        # Create info dictionary with error details
        info = {
            'error': error_msg,
            'day_at_call': getattr(self, 'day', -1),
            'fallback_response': True
        }
        
        if actions is not None:
            info['actions'] = actions.tolist() if hasattr(actions, 'tolist') else str(actions)
            
        if super_return is not None:
            info['super_return_type'] = str(type(super_return))
            
        self.logger.error(f"[PID:{pid}] _create_fallback_response: {error_msg}")
        
        # Return fallback response with heavy penalty
        return enhanced_state, -1000.0, True, False, info

    def _extract_symbol_price_series(self, symbol: str, lookback: int = 50) -> Optional[pd.Series]:
        """Extract historical price series for a symbol up to the current day.
        
        Args:
            symbol: Stock symbol
            lookback: Number of days to look back from current day.
            
        Returns:
            Price series (Pandas Series) or None if insufficient data.
        """
        pid = os.getpid()
        if not hasattr(self, 'df') or self.df is None:
            self.logger.warning(f"[PID:{pid}] _extract_symbol_price_series: self.df is None. Cannot extract series for {symbol}.")
            return None
        if not hasattr(self, 'day'):
            self.logger.warning(f"[PID:{pid}] _extract_symbol_price_series: self.day attribute not set. Cannot determine current point for {symbol}.")
            return None

        try:
            # Filter the entire DataFrame for the specific symbol
            symbol_all_data = self.df[self.df.tic == symbol]

            if symbol_all_data.empty:
                self.logger.warning(f"[PID:{pid}] _extract_symbol_price_series: No data found in self.df for symbol {symbol}.")
                return None

            # `self.day` is the index for the current day in the unique dates of the overall dataset.
            # We need to map `self.day` to an actual index in `symbol_all_data`.
            # Assuming `self.df` is sorted by date and then by tic.
            # And that `self.data = self.df.loc[self.day, :]` means `self.day` is a primary index level (e.g. an integer from 0 to N-1 days)

            # A robust way: get unique dates from the full df, find current date, then filter symbol_all_data.
            unique_dates = self.df.date.unique() # Assumes 'date' column exists and is sorted
            if self.day >= len(unique_dates):
                self.logger.warning(f"[PID:{pid}] _extract_symbol_price_series: self.day ({self.day}) is out of range for unique dates (len: {len(unique_dates)}).")
                return None
            current_date = unique_dates[self.day]
            
            # Get data for the symbol up to and including the current_date
            symbol_historical_data = symbol_all_data[symbol_all_data.date <= current_date].copy()

            if symbol_historical_data.empty:
                self.logger.warning(f"[PID:{pid}] _extract_symbol_price_series: No historical data for {symbol} up to date {current_date} (day index {self.day}).")
                return None
            
            # Now apply lookback to this filtered data
            end_point_exclusive = len(symbol_historical_data) # One past the last element
            start_point_inclusive = max(0, end_point_exclusive - lookback)
            
            price_series = symbol_historical_data.iloc[start_point_inclusive:end_point_exclusive]['close']
            
            # self.logger.debug(f"[PID:{pid}] _extract_symbol_price_series for {symbol} on day {self.day} (date {current_date}): Found {len(price_series)} points with lookback {lookback}.")

            return price_series if not price_series.empty else None
            
        except KeyError as e: # e.g. 'close' column not found
            self.logger.error(f"[PID:{pid}] _extract_symbol_price_series: KeyError for {symbol} on day {self.day}: {e}. Check DataFrame structure.")
            return None
        except Exception as e:
            self.logger.error(f"[PID:{pid}] _extract_symbol_price_series: Failed to extract price series for {symbol} on day {self.day}: {e}", exc_info=True)
            return None
    
    # _calculate_reward is not typically overridden if the parent class already calculates it based on portfolio changes.
    # The reward shaping is applied in the step method after getting the reward from super().step().
    # However, if FinRL's _calculate_reward itself needs to be called explicitly (it's usually internal to step),
    # then this structure might be needed. FinRL's StockTradingEnv calculates reward internally within its step().
    # For clarity, we will assume the reward from super().step() is the one to be shaped.
    # The original code had a _calculate_reward method that called super()._calculate_reward(actions)
    # and then _apply_asymmetric_reward_shaping. This might be if _calculate_reward is an explicit
    # part of the FinRL step lifecycle that can be hooked.
    # If super().step() returns the final reward, then shaping should happen on that.
    # Given the current step method, we are taking reward from super().step() and then shaping it.
    # So, an explicit _calculate_reward here might be redundant unless super()._calculate_reward needs to be called.
    # Let's keep the shaping logic separate as _apply_asymmetric_reward_shaping and call it in step.

    def _apply_asymmetric_reward_shaping(self, base_reward: float) -> float:
        """Apply asymmetric reward shaping based on market trend.
        
        Args:
            base_reward: Base reward from environment (e.g., portfolio change)
            
        Returns:
            Asymmetric-shaped reward
        """
        pid = os.getpid()
        try:
            market_trend = self._detect_market_trend()
            shaped_reward = base_reward # Default to base reward

            if market_trend == 'down':
                if base_reward < 0:
                    shaped_reward = base_reward * 1.5  # Increase penalty
                else:
                    shaped_reward = base_reward * 0.8  # Reduce gains slightly
            elif market_trend == 'up':
                if base_reward > 0:
                    shaped_reward = base_reward * 1.3  # Increase reward
                else:
                    shaped_reward = base_reward * 0.9  # Reduce penalty slightly
            
            # self.logger.debug(f"[PID:{pid}] _apply_asymmetric_reward_shaping: Trend={market_trend}, BaseRew={base_reward:.4f}, ShapedRew={shaped_reward:.4f}")
            return shaped_reward
            
        except Exception as e:
            self.logger.warning(f"[PID:{pid}] Failed to apply asymmetric reward shaping: {e}. Returning base_reward.")
            return base_reward
    
    def _detect_market_trend(self) -> str:
        """Detect current market trend based on recent portfolio performance.
        
        Returns:
            Market trend: 'up', 'down', or 'neutral'
        """
        pid = os.getpid()
        try:
            if hasattr(self, 'asset_memory') and len(self.asset_memory) > 5: # Need at least 5 records for 4 differences
                # Asset memory stores portfolio value at each step
                recent_values = np.array(self.asset_memory[-5:], dtype=np.float32) # Last 5 portfolio values
                # Calculate percent returns: (p_t / p_{t-1}) - 1
                # To avoid division by zero if asset_memory can be zero.
                # A simple diff might be fine if values are reasonably large.
                # Let's use simple differences first, as in original example.
                recent_diffs = np.diff(recent_values) # Differences between consecutive values
                
                if len(recent_diffs) == 0: # Should not happen if len(asset_memory) > 1
                    return 'neutral'

                avg_change = np.mean(recent_diffs)
                
                # Thresholds depend on the scale of portfolio_value and typical changes
                # The original thresholds (0.001) might be too small if asset_memory stores large dollar values.
                # Let's assume asset_memory changes are scaled appropriately or thresholds are relative.
                # For simplicity, using the original logic's threshold idea:
                # Consider a threshold relative to initial amount or current value if asset_memory is raw value
                # If asset_memory stores returns, then 0.001 (0.1%) is fine.
                # Assuming avg_change is a change in portfolio value:
                # A more robust way would be to look at average *percentage* return.
                # For now, let's use a simplified threshold relative to initial capital for interpreting avg_change.
                # This is a placeholder, proper trend detection is complex.
                threshold = self.initial_amount * 0.0005 # 0.05% of initial capital as a daily change threshold

                if avg_change > threshold: # Consistently positive change
                    # self.logger.debug(f"[PID:{pid}] _detect_market_trend: UP (avg_change: {avg_change:.2f} > threshold: {threshold:.2f})")
                    return 'up'
                elif avg_change < -threshold: # Consistently negative change
                    # self.logger.debug(f"[PID:{pid}] _detect_market_trend: DOWN (avg_change: {avg_change:.2f} < -threshold: {-threshold:.2f})")
                    return 'down'
                else:
                    # self.logger.debug(f"[PID:{pid}] _detect_market_trend: NEUTRAL (avg_change: {avg_change:.2f} within +/-threshold: {threshold:.2f})")
                    return 'neutral'
            
            # self.logger.debug(f"[PID:{pid}] _detect_market_trend: NEUTRAL (insufficient asset_memory: {len(self.asset_memory) if hasattr(self, 'asset_memory') else 'N/A'})")
            return 'neutral'
            
        except Exception as e:
            self.logger.warning(f"[PID:{pid}] _detect_market_trend: Error detecting trend: {e}. Defaulting to 'neutral'.")
            return 'neutral'