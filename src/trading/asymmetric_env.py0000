"""Asymmetric Trading Environment

This module implements an enhanced trading environment that integrates
asymmetric return strategy with FinRL's StockTradingEnv.

Features:
- Asymmetric signal integration in state space
- Asymmetric reward shaping
- Enhanced state representation with strategy signals
- Market regime awareness
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
import warnings
import os
from utils.logging import logger # Corrected logger import

try:
    from finrl.meta.env_stock_trading.env_stocktrading import StockTradingEnv
    import gymnasium as gym # Use gymnasium for consistency
    from gymnasium import spaces
except ImportError:
    # Fallback for development/testing
    import gymnasium as gym # Use gymnasium for consistency
    from gymnasium import spaces
    
    class StockTradingEnv_NotInUse(gym.Env):
        """Fallback StockTradingEnv for when FinRL is not available"""
        
        def __init__(self, df, stock_dim, hmax, initial_amount, num_stock_shares,
                     buy_cost_pct, sell_cost_pct, state_space, action_space, 
                     tech_indicator_list, **kwargs):
            super().__init__()
            self.df = df
            self.stock_dim = stock_dim
            self.hmax = hmax
            self.initial_amount = initial_amount
            self.num_stock_shares = num_stock_shares
            self.buy_cost_pct = buy_cost_pct
            self.sell_cost_pct = sell_cost_pct
            self.state_space = state_space
            self.action_space_dim = action_space
            self.tech_indicator_list = tech_indicator_list
            
            # Initialize gym spaces
            self.action_space = spaces.Box(low=-1, high=1, shape=(action_space,), dtype=np.float32)
            self.observation_space = spaces.Box(low=-np.inf, high=np.inf, shape=(state_space,), dtype=np.float32)
            
            # Initialize state variables
            self.day = 0
            self.data = df.loc[self.day, :]
            self.terminal = False
            self.portfolio_value = initial_amount
            self.asset_memory = [initial_amount]
            self.portfolio_return_memory = [0]
            self.actions_memory = []
            self.date_memory = [self.data.date.unique()[0]]
            
        def reset(self):
            """Reset environment to initial state"""
            self.day = 0
            self.data = self.df.loc[self.day, :]
            self.terminal = False
            self.portfolio_value = self.initial_amount
            self.asset_memory = [self.initial_amount]
            self.portfolio_return_memory = [0]
            self.actions_memory = []
            self.date_memory = [self.data.date.unique()[0]]
            return self._get_state()
            
        def step(self, actions):
            """Execute one step in the environment"""
            self.terminal = self.day >= len(self.df.index.unique()) - 1
            
            if self.terminal:
                # Return 5-tuple: observation, reward, terminated, truncated, info
                return self._get_state(), 0, True, False, {}
            
            # Simple step implementation
            self.day += 1
            self.data = self.df.loc[self.day, :]
            
            # Calculate reward (simplified)
            reward = np.random.normal(0, 0.01)  # Placeholder reward
            
            # Update memory
            self.actions_memory.append(actions)
            self.portfolio_value += reward * self.initial_amount
            self.asset_memory.append(self.portfolio_value)
            self.portfolio_return_memory.append(reward)
            self.date_memory.append(self.data.date.unique()[0])
            
            # Return 5-tuple: observation, reward, terminated, truncated, info
            return self._get_state(), reward, self.terminal, False, {}
            
        def _get_state(self):
            """Get current state as NumPy array"""
            # Create a simple state representation
            state = np.zeros(self.state_space, dtype=np.float32)
            
            # Fill with some basic values
            state[0] = self.portfolio_value / self.initial_amount  # Portfolio ratio
            
            # Fill remaining with random values (placeholder)
            if len(state) > 1:
                state[1:] = np.random.normal(0, 0.1, len(state) - 1)
            
            return state.astype(np.float32)

from strategies.asymmetric_strategy import AsymmetricStrategy, AsymmetricConfig
from config import settings
from utils import get_logger # Keep this for the main logger
from loguru import logger as loguru_logger # Import loguru directly for adding sinks


class AsymmetricTradingEnv(StockTradingEnv):
    """Enhanced trading environment with asymmetric strategy integration."""
    
    def __init__(
        self,
        df: pd.DataFrame,
        stock_dim: int,
        hmax: int,
        initial_amount: float,
        num_stock_shares: List[int],
        buy_cost_pct: float,
        sell_cost_pct: float,
        state_space: int,
        action_space: int,
        tech_indicator_list: List[str],
        reward_scaling: float = 1e-4,
        asymmetric_config: Optional[AsymmetricConfig] = None,
        enhanced_state_input: bool = False,
        **kwargs
    ):
        """Initialize asymmetric trading environment.
        
        Args:
            df: Market data DataFrame
            stock_dim: Number of stocks
            hmax: Maximum shares per stock
            initial_amount: Initial capital
            num_stock_shares: Initial stock holdings
            buy_cost_pct: Buy transaction cost
            sell_cost_pct: Sell transaction cost
            state_space: State space dimension
            action_space: Action space dimension
            tech_indicator_list: Technical indicators
            reward_scaling: Reward scaling factor
            asymmetric_config: Asymmetric strategy configuration
            **kwargs: Additional arguments, may include log_file_path and log_level
        """
        # Initialize logger for this instance
        # Use get_logger for general class logging, but configure specific sink for workers
        self.logger = get_logger(__name__) 

        # Worker-specific logging setup using loguru
        log_file_path = kwargs.get('log_file_path')
        log_level = kwargs.get('log_level', 'INFO') # Default to INFO if not provided

        if log_file_path:
            try:
                # Ensure the logger for this instance (worker) writes to the specified file
                # Use a format that includes PID to distinguish worker logs
                loguru_logger.add(
                    log_file_path,
                    level=log_level.upper(),
                    format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} [PID:{process}] - {message}",
                    enqueue=True,  # Essential for multiprocessing
                    rotation="10 MB", # Optional: manage log file size
                    retention="10 days", # Optional: manage log retention
                    catch=True # Catch errors within loguru
                )
                self.logger.info(f"Worker-specific logger configured for {__name__} at {log_file_path} with level {log_level}")
            except Exception as e:
                self.logger.error(f"Failed to configure worker-specific logger: {e}")

        # Initialize asymmetric strategy
        if asymmetric_config is None:
            asymmetric_config = AsymmetricConfig(
                symbols=[df['tic'].unique()[i] for i in range(stock_dim)]
            )
        
        self.asymmetric_strategy = AsymmetricStrategy(asymmetric_config)
        self.asymmetric_features_per_stock = 5  # asymmetry_score, rsi, bb_position, momentum, volatility
        asymmetric_features_size = self.asymmetric_features_per_stock * stock_dim

        # Calculate the true original_state_space based on FinRL's structure
        # FinRL's state space: 1 (balance) + stock_dim (shares) + stock_dim (prices) + stock_dim * len(tech_indicator_list)
        # This simplifies to: 1 + 2 * stock_dim + len(tech_indicator_list) * stock_dim
        true_original_state_space = 1 + (2 * stock_dim) + (len(tech_indicator_list) * stock_dim)
        
        if enhanced_state_input:
            # This case assumes state_space argument is already the enhanced dimension
            self.enhanced_state_space = state_space
            self.original_state_space = state_space - asymmetric_features_size
            # Ensure parent_state_space is the true original, not derived from a potentially incorrect enhanced input
            parent_state_space = true_original_state_space 
        else:
            # state_space argument is a placeholder or base, calculate true dimensions
            self.original_state_space = true_original_state_space
            self.enhanced_state_space = self.original_state_space + asymmetric_features_size
            parent_state_space = self.original_state_space
        
        # Initialize parent class with the correctly calculated parent_state_space
        # Convert scalar buy/sell cost percentages to lists if they are not already
        if isinstance(buy_cost_pct, float):
            buy_cost_pct_list = [buy_cost_pct] * stock_dim
        else:
            buy_cost_pct_list = buy_cost_pct

        if isinstance(sell_cost_pct, float):
            sell_cost_pct_list = [sell_cost_pct] * stock_dim
        else:
            sell_cost_pct_list = sell_cost_pct

        super().__init__(
            df=df,
            stock_dim=stock_dim,
            hmax=hmax,
            initial_amount=initial_amount,
            num_stock_shares=num_stock_shares,
            buy_cost_pct=buy_cost_pct_list,  # Pass the list
            sell_cost_pct=sell_cost_pct_list, # Pass the list
            state_space=parent_state_space, # Use the calculated original state space for parent
            action_space=action_space,
            tech_indicator_list=tech_indicator_list,
            reward_scaling=reward_scaling
            # Removed unexpected kwargs: initial_buy, daily_information_cols, print_verbosity, random_exploration, make_plots, cache_indicator_data
        )
        
        # Override observation_space to include asymmetric features
        # gymnasium.spaces is imported at the top of the file now
        self.observation_space = spaces.Box(
            low=-np.inf, 
            high=np.inf, 
            shape=(self.enhanced_state_space,), 
            dtype=np.float32
        )
        
        self.logger = get_logger(__name__)
        self.asymmetric_cache = {}  # Cache for asymmetric calculations
        
        self.logger.info(
            f"AsymmetricTradingEnv initialized: stock_dim={stock_dim}, "
            f"enhanced_state_space={self.enhanced_state_space}, "
            f"asymmetric_features_per_stock={self.asymmetric_features_per_stock}"
        )
    
    def _get_state(self) -> np.ndarray:
        """Get enhanced state with asymmetric features.
        
        Returns:
            Enhanced state array including asymmetric signals
        """
        import os # For PID
        pid = os.getpid()

        self.logger.debug(f"[PID:{pid}] _get_state: original_state_space={self.original_state_space}, enhanced_state_space={self.enhanced_state_space}")

        # Get base state from parent class state attribute
        parent_state_raw = None
        if hasattr(self, 'state') and self.state is not None:
            parent_state_raw = self.state
            self.logger.debug(f"[PID:{pid}] _get_state: Parent self.state type: {type(parent_state_raw)}, length/shape: {len(parent_state_raw) if isinstance(parent_state_raw, list) else parent_state_raw.shape if hasattr(parent_state_raw, 'shape') else 'N/A'}")
            base_state = np.array(self.state, dtype=np.float32) # Ensure float32 early
        else:
            # Fallback: update state first, then get it
            self.logger.warning(f"[PID:{pid}] _get_state: Parent self.state is None or not found. Attempting self._update_state().")
            self._update_state() # This method should set self.state in the parent
            if hasattr(self, 'state') and self.state is not None:
                parent_state_raw = self.state
                self.logger.debug(f"[PID:{pid}] _get_state: Parent self.state after _update_state() - type: {type(parent_state_raw)}, length/shape: {len(parent_state_raw) if isinstance(parent_state_raw, list) else parent_state_raw.shape if hasattr(parent_state_raw, 'shape') else 'N/A'}")
                base_state = np.array(self.state, dtype=np.float32)
            else:
                self.logger.error(f"[PID:{pid}] _get_state: Parent self.state still None after _update_state(). Falling back to zeros for base_state.")
                base_state = np.zeros(self.original_state_space, dtype=np.float32)
        
        self.logger.debug(f"[PID:{pid}] _get_state: base_state shape: {base_state.shape}, dtype: {base_state.dtype}")

        # Calculate asymmetric features
        asymmetric_features = self._calculate_asymmetric_features() # Already returns np.float32
        self.logger.debug(f"[PID:{pid}] _get_state: asymmetric_features shape: {asymmetric_features.shape}, dtype: {asymmetric_features.dtype}")

        # Pre-concatenation check
        expected_len_base = self.original_state_space
        expected_len_asym = self.asymmetric_features_per_stock * self.stock_dim
        if base_state.shape[0] != expected_len_base:
            self.logger.critical(f"[PID:{pid}] _get_state: CRITICAL MISMATCH! base_state length {base_state.shape[0]} != expected original_state_space {expected_len_base}. Parent state: {parent_state_raw}")
        if asymmetric_features.shape[0] != expected_len_asym:
             self.logger.critical(f"[PID:{pid}] _get_state: CRITICAL MISMATCH! asymmetric_features length {asymmetric_features.shape[0]} != expected {expected_len_asym}.")
        
        if base_state.shape[0] + asymmetric_features.shape[0] != self.enhanced_state_space:
            self.logger.critical(
                f"[PID:{pid}] _get_state: CRITICAL DIMENSION MISMATCH BEFORE CONCAT! "
                f"base_state ({base_state.shape[0]}) + asymmetric_features ({asymmetric_features.shape[0]}) = {base_state.shape[0] + asymmetric_features.shape[0]} "
                f"!= enhanced_state_space ({self.enhanced_state_space})"
            )
            # Attempt to pad/truncate base_state if it's the culprit, to prevent crash, but log error
            if base_state.shape[0] != self.original_state_space:
                self.logger.error(f"[PID:{pid}] _get_state: Adjusting base_state from {base_state.shape[0]} to {self.original_state_space} due to mismatch.")
                new_base_state = np.zeros(self.original_state_space, dtype=np.float32)
                common_len = min(base_state.shape[0], self.original_state_space)
                new_base_state[:common_len] = base_state[:common_len]
                base_state = new_base_state

        # Combine base state with asymmetric features
        enhanced_state = np.concatenate([base_state, asymmetric_features]).astype(np.float32) # Ensure final dtype
        
        self.logger.debug(f"[PID:{pid}] _get_state: enhanced_state shape: {enhanced_state.shape}, dtype: {enhanced_state.dtype}")

        if enhanced_state.shape[0] != self.enhanced_state_space:
            self.logger.critical(
                f"[PID:{pid}] _get_state: FINAL CRITICAL SHAPE MISMATCH! enhanced_state.shape[0] ({enhanced_state.shape[0]}) "
                f"!= self.enhanced_state_space ({self.enhanced_state_space})."
            )
        
        return enhanced_state
    
    def _calculate_asymmetric_features(self) -> np.ndarray:
        """Calculate asymmetric strategy features for current state.
        
        Returns:
            Array of asymmetric features for all stocks
        """
        features = []
        current_data = self._get_current_market_data()
        
        if current_data is None:
            # Return zeros if no data available
            return np.zeros(self.stock_dim * self.asymmetric_features_per_stock)
        
        for i, symbol in enumerate(self.data.tic.unique()):
            try:
                # Extract price series for this symbol
                symbol_data = self._extract_symbol_price_series(symbol)
                
                if symbol_data is None or len(symbol_data) < 20:
                    # Use default values if insufficient data
                    stock_features = [0.0, 50.0, 0.5, 0.0, 0.01]  # Default values
                else:
                    # Calculate asymmetric features
                    asymmetry_score = self.asymmetric_strategy._calculate_asymmetry_score(symbol_data)
                    indicators = self.asymmetric_strategy._calculate_indicators(symbol_data)
                    
                    # Extract key features
                    rsi = indicators.get('rsi', 50.0)
                    bb_position = indicators.get('bb_position', 0.5)
                    momentum = indicators.get('momentum', 0.0)
                    volatility = indicators.get('volatility', 0.01)
                    
                    stock_features = [
                        asymmetry_score,
                        rsi / 100.0,  # Normalize RSI to [0, 1]
                        bb_position,
                        np.tanh(momentum),  # Normalize momentum
                        min(volatility * 10, 1.0)  # Scale and cap volatility
                    ]
                
                features.extend(stock_features)
                
            except Exception as e:
                self.logger.warning(f"Failed to calculate asymmetric features for {symbol}: {e}")
                # Use default values on error
                features.extend([0.0, 0.5, 0.5, 0.0, 0.01])
        
        return np.array(features, dtype=np.float32)

    def reset(self, *, seed: Optional[int] = None, options: Optional[dict] = None) -> Tuple[np.ndarray, dict]:
        """Reset the environment and return the enhanced state."""
        pid = os.getpid()
        self.logger.info(f"[PID:{pid}] reset: Called. Current day: {self.day}, data_source type: {type(self.data_source)}")
        if self.df is not None:
            self.logger.debug(f"[PID:{pid}] reset: self.df shape: {self.df.shape}, dtypes:\n{self.df.dtypes}")
            self.logger.debug(f"[PID:{pid}] reset: self.df head:\n{self.df.head().to_string()}")
            self.logger.debug(f"[PID:{pid}] reset: self.df tail:\n{self.df.tail().to_string()}")
        else:
            self.logger.warning(f"[PID:{pid}] reset: self.df is None.")

        if hasattr(self, 'data') and self.data is not None and not self.data.equals(self.df):
            self.logger.debug(f"[PID:{pid}] reset: self.data shape: {self.data.shape}, dtypes:\n{self.data.dtypes}")
            self.logger.debug(f"[PID:{pid}] reset: self.data head:\n{self.data.head().to_string()}")
        elif hasattr(self, 'data') and self.data is None:
            self.logger.warning(f"[PID:{pid}] reset: self.data is None.")

        # Call parent's reset logic. 
        # FinRL's StockTradingEnv.reset() sets self.state and returns it.
        # The gymnasium standard is to return (obs, info)
        # We need to ensure compatibility with what super().reset() returns and expects.
        # If super().reset() follows gymnasium standard: obs, info = super().reset(seed=seed, options=options)
        # If it's older gym: obs = super().reset(seed=seed) # (options might not be supported)
        # For now, let's assume it might just return obs, and we'll construct info if needed.
        
        # Standard way to call parent reset in gymnasium
        # super().reset() will set the parent's internal state (e.g., self.state)
        if hasattr(super(), 'reset') and callable(getattr(super(), 'reset')):
            # Accommodate possibility that parent reset might not take all args or return (obs, info)
            try:
                # Attempt standard gymnasium call if possible
                _, parent_info = super().reset(seed=seed, options=options) 
            except TypeError:
                # Fallback if parent reset has different signature (e.g. older gym)
                super().reset() # seed might need to be handled via self.seed(seed) if applicable
                parent_info = {} # Default info
        else:
            # Should not happen if inheriting from gym.Env
            parent_info = {}

        enhanced_state = self._get_state()
        # Ensure info is a dictionary
        if not isinstance(parent_info, dict):
            parent_info = {}
        return enhanced_state, parent_info

    def step(self, actions: np.ndarray) -> tuple[np.ndarray, float, bool, bool, dict]:
        """Perform a step in the environment.

        Args:
            actions (np.ndarray): The actions to take.

        Returns:
            tuple[np.ndarray, float, bool, bool, dict]: The new state, reward, done, truncated, and info.
        """
        # Correctly unpack five values from the superclass's step method
        # Standard Gym interface returns: observation, reward, terminated, truncated, info
        # FinRL's StockTradingEnv likely adheres to this.
        pid = os.getpid() # Get current process ID
        logger.debug(f"[PID:{pid}] step: About to call super().step() with actions: {actions}. Day: {self.day} (before super().step() and self.day increment)")
        # logger.debug(f"[PID:{pid}] step: self.data.head():\n{self.data.head()} (before super().step())")
        # logger.debug(f"[PID:{pid}] step: self.df relevant slice for day {self.day}:\n{self.df.iloc[max(0, self.day-2):min(len(self.df), self.day+3)]}")

        super_step_return = None
        next_state_base, reward, terminated, truncated, info = None, None, None, None, None # Ensure they are defined

        try:
            super_step_return = super().step(actions)
            if super_step_return is None:
                logger.critical(f"[PID:{pid}] CRITICAL: super().step(actions) returned None!")
                # Fallback strategy: Use placeholder values and mark as terminated
                next_state_base = self.observation_space.sample() # Placeholder, might not be ideal for 'base'
                reward = -1000  # Penalize heavily
                terminated = True  # End episode
                truncated = False # Explicitly set truncated
                info = {'error': 'super().step() returned None', 'actions': actions, 'day_at_call': self.day}
                logger.critical(f"[PID:{pid}] Assigned placeholder values after super().step() returned None: next_state_base type: {type(next_state_base)}, reward: {reward}, terminated: {terminated}, truncated: {truncated}")
            else:
                # Attempt to unpack if super_step_return is not None
                next_state_base, reward, terminated, truncated, info = super_step_return
                logger.debug(f"[PID:{pid}] step: Successfully unpacked super().step() return.")

        except TypeError as e:
            logger.error(f"[PID:{pid}] TypeError during unpacking super().step() return: {e}. super_step_return was: {super_step_return}, actions: {actions}, day_at_call: {self.day}")
            # Fallback strategy for TypeError during unpacking
            next_state_base = self.observation_space.sample() # Placeholder
            reward = -1000  # Penalize heavily
            terminated = True  # End episode
            truncated = False # Explicitly set truncated
            info = {'error': f'TypeError unpacking super().step(): {e}', 'super_return_type': str(type(super_step_return)), 'actions': actions, 'day_at_call': self.day}
            logger.critical(f"[PID:{pid}] Assigned placeholder values after TypeError unpacking super().step().")
            # It's often better to re-raise if the agent can't handle this, but for diagnosis, we continue with defaults.
            # raise # Uncomment to make the original error visible and halt
        except Exception as e:
            logger.error(f"[PID:{pid}] Exception during super().step(actions) or its unpacking: {e}. super_step_return was: {super_step_return}, actions: {actions}, day_at_call: {self.day}")
            # Generic fallback strategy
            next_state_base = self.observation_space.sample() # Placeholder
            reward = -1000  # Penalize heavily
            terminated = True  # End episode
            truncated = False # Explicitly set truncated
            info = {'error': f'Exception in super().step(): {e}', 'super_return_type': str(type(super_step_return)), 'actions': actions, 'day_at_call': self.day}
            logger.critical(f"[PID:{pid}] Assigned placeholder values after generic Exception in super().step().")
            # raise # Uncomment to make the original error visible and halt

        logger.debug(f"[PID:{pid}] step: After super().step() call/fallback - next_state_base type: {type(next_state_base)}, reward: {reward}, terminated: {terminated}, truncated: {truncated}, info: {info}")

        # self.day and self.data are updated AFTER super().step() in the original logic.
        # This is important because super().step() might use the 'current' self.day to determine 'next_state_base'.
        # If super().step() failed and we used fallback values, self.day might not have been incremented.
        # We need to ensure self.day is incremented if the episode is not terminated by the fallback.
        if not (terminated or truncated): # Only increment day if episode is not over
            self.day += 1
        self.data = self.df.loc[self.day, :] # This might fail if self.day goes out of bounds


        # Get the enhanced state using the new base state
        # Note: _get_state now uses self.data which is updated for the current day
        enhanced_state = self._get_state() 

        # Log the shapes for debugging, especially in worker processes
        pid = os.getpid()
        logger.debug(f"[PID:{pid}] step: enhanced_state shape: {enhanced_state.shape}, dtype: {enhanced_state.dtype}")
        logger.debug(f"[PID:{pid}] step: next_state_base from super().step() shape: {next_state_base.shape if isinstance(next_state_base, np.ndarray) else type(next_state_base)}, type: {type(next_state_base)}")
        logger.debug(f"[PID:{pid}] step: reward: {reward}, terminated: {terminated}, truncated: {truncated}")

        # Ensure the state returned by _get_state is used if it's the intended observation
        # The original `next_state_base` might be from the *previous* day if super().step() calculates it before self.day is incremented.
        # If _get_state() is meant to be the *actual* next state observation, use it.
        # However, the standard is that super().step() returns the *next* state directly.
        # For now, let's assume enhanced_state is the correct one to return to the agent.

        # Check if the state shape matches the observation space
        if not self.observation_space.contains(enhanced_state):
            logger.critical(
                f"[PID:{pid}] step: State shape mismatch! "
                f"Enhanced state shape: {enhanced_state.shape}, dtype: {enhanced_state.dtype}. "
                f"Observation space shape: {self.observation_space.shape}. "
                f"Enhanced state space (expected): {self.enhanced_state_space}."
            )
            # Potentially raise an error or handle this case, e.g., by clipping or padding
            # For now, just log critically.

        # The 'done' signal should be a combination of terminated and truncated
        # The 'done' signal is typically handled by the agent based on terminated and truncated.
        # ElegantRL expects 5 values: obs, reward, terminated, truncated, info
        logger.debug(f"[PID:{os.getpid()}] FINAL RETURN: enhanced_state type: {type(enhanced_state)}, reward type: {type(reward)}, terminated type: {type(terminated)}, truncated type: {type(truncated)}, info type: {type(info)}, enhanced_state is None: {enhanced_state is None}, reward is None: {reward is None}, terminated is None: {terminated is None}, truncated is None: {truncated is None}, info is None: {info is None}")
        return enhanced_state, reward, terminated, truncated, info
    
    def _get_current_market_data(self) -> Optional[pd.DataFrame]:
        """Get current market data slice.
        
        Returns:
            Current market data or None
        """
        try:
            if hasattr(self, 'day') and self.day < len(self.data.index.unique()):
                current_date = self.data.index.unique()[self.day]
                return self.data[self.data.index == current_date]
            return None
        except Exception:
            return None
    
    def _extract_symbol_price_series(self, symbol: str, lookback: int = 50) -> Optional[pd.Series]:
        """Extract price series for a symbol with lookback.
        
        Args:
            symbol: Stock symbol
            lookback: Number of days to look back
            
        Returns:
            Price series or None
        """
        try:
            # Get symbol data up to current day
            symbol_data = self.data[self.data.tic == symbol].copy()
            
            if len(symbol_data) == 0:
                return None
            
            # Get current position in data
            current_day = getattr(self, 'day', 0)
            end_idx = min(current_day + 1, len(symbol_data))
            start_idx = max(0, end_idx - lookback)
            
            # Extract price series
            price_series = symbol_data.iloc[start_idx:end_idx]['close']
            
            return price_series if len(price_series) > 0 else None
            
        except Exception as e:
            self.logger.warning(f"Failed to extract price series for {symbol}: {e}")
            return None
    
    def _calculate_reward(self, actions: np.ndarray) -> float:
        """Calculate reward with asymmetric shaping.
        
        Args:
            actions: Trading actions
            
        Returns:
            Asymmetric-shaped reward
        """
        # Get base reward from parent class
        base_reward = super()._calculate_reward(actions)
        
        # Apply asymmetric reward shaping
        asymmetric_reward = self._apply_asymmetric_reward_shaping(base_reward)
        
        return asymmetric_reward
    
    def _apply_asymmetric_reward_shaping(self, base_reward: float) -> float:
        """Apply asymmetric reward shaping.
        
        Args:
            base_reward: Base reward from environment
            
        Returns:
            Asymmetric-shaped reward
        """
        try:
            # Detect market regime (simplified)
            market_trend = self._detect_market_trend()
            
            if market_trend == 'down':
                # During downturns, penalize losses more heavily
                if base_reward < 0:
                    shaped_reward = base_reward * 1.5  # Increase penalty
                else:
                    shaped_reward = base_reward * 0.8  # Reduce gains slightly
            elif market_trend == 'up':
                # During upturns, reward gains more
                if base_reward > 0:
                    shaped_reward = base_reward * 1.3  # Increase reward
                else:
                    shaped_reward = base_reward * 0.9  # Reduce penalty slightly
            else:
                # Neutral market
                shaped_reward = base_reward
            
            return shaped_reward
            
        except Exception as e:
            self.logger.warning(f"Failed to apply asymmetric reward shaping: {e}")
            return base_reward
    
    def _detect_market_trend(self) -> str:
        """Detect current market trend.
        
        Returns:
            Market trend: 'up', 'down', or 'neutral'
        """
        try:
            # Simple trend detection based on recent portfolio performance
            if hasattr(self, 'asset_memory') and len(self.asset_memory) > 5:
                recent_returns = np.diff(self.asset_memory[-5:])
                avg_return = np.mean(recent_returns)
                
                if avg_return > 0.001:
                    return 'up'
                elif avg_return < -0.001:
                    return 'down'
                else:
                    return 'neutral'
            
            return 'neutral'
            
        except Exception:
            return 'neutral'
    
    def reset(self) -> Tuple[np.ndarray, Dict]:
        """Reset environment and clear asymmetric cache.
        
        Returns:
            Tuple of (initial_state, info_dict) for ElegantRL compatibility
        """
        # Clear asymmetric cache
        self.asymmetric_cache.clear()
        
        # Reset parent environment
        reset_result = super().reset()
        
        # Handle different return formats from FinRL
        if isinstance(reset_result, tuple):
            # FinRL sometimes returns (state, info)
            initial_state = reset_result[0]
            info_dict = reset_result[1] if len(reset_result) > 1 else {}
        else:
            # Direct state return
            initial_state = reset_result
            info_dict = {}
        
        # Ensure state is always a NumPy array
        if isinstance(initial_state, list):
            initial_state = np.array(initial_state, dtype=np.float32)
        elif isinstance(initial_state, np.ndarray):
            initial_state = initial_state.astype(np.float32)
        else:
            # Handle scalar or other types
            initial_state = np.array([float(initial_state)], dtype=np.float32)
        
        return initial_state, info_dict
    
    def step(self, actions: np.ndarray) -> Tuple[np.ndarray, float, bool, Dict]:
        """Execute one step with asymmetric features.
        
        Args:
            actions: Trading actions
            
        Returns:
            Tuple of (state, reward, done, info) with state as NumPy array
        """
        self.actions_memory.append(actions)
        # self.day and self.data are expected to be updated by super().step()

        # logger.debug(f"PID: {os.getpid()} AsymmetricEnv.step() BEFORE super().step(): day={getattr(self, 'day', 'N/A')}, actions={actions}")

        next_state_base, reward, terminated, truncated, info = None, 0.0, False, False, {}
        enhanced_state = np.zeros(self.observation_space.shape, dtype=np.float32)
        done = True # Default to done=True if super().step() fails catastrophically

        try:
            raw_step_output = super().step(actions)

            if raw_step_output is None:
                logger.error(f"PID: {os.getpid()} AsymmetricEnv.step() super().step(actions) returned None. Actions: {actions}, Day: {getattr(self, 'day', 'N/A')}")
                info.update({'status': 'error_super_step_returned_none', 'actions': actions, 'day': getattr(self, 'day', 'N/A')})
                # Return a default state to prevent crashing, episode will terminate
                return enhanced_state, 0.0, True, info
            
            # Attempt to unpack; could be 4 or 5 items depending on Gym/Gymnasium version of parent
            if len(raw_step_output) == 5:
                next_state_base, reward, terminated, truncated, info_super = raw_step_output
                info.update(info_super) # Merge info dicts
            elif len(raw_step_output) == 4:
                # FinRL's StockTradingEnv might return 4 items (state, reward, done, info)
                next_state_base, reward, done_super, info_super = raw_step_output
                terminated = done_super # If parent returns 'done', it implies terminated or truncated
                truncated = False # Assume not truncated if only 'done' is provided by parent
                info.update(info_super)
            else:
                logger.error(f"PID: {os.getpid()} AsymmetricEnv.step() super().step(actions) returned unexpected number of items: {len(raw_step_output)}. Output: {raw_step_output}")
                info.update({'status': 'error_super_step_unexpected_items', 'output_len': len(raw_step_output), 'raw_output': raw_step_output})
                return enhanced_state, 0.0, True, info

            logger.debug(f"PID: {os.getpid()} AsymmetricEnv.step() AFTER super().step(): day={getattr(self, 'day', 'N/A')}, next_state_base type: {type(next_state_base)}, reward={reward}, terminated={terminated}, truncated={truncated}, info={info}")

            if isinstance(next_state_base, list):
                next_state_base = np.array(next_state_base, dtype=np.float32)
            elif not isinstance(next_state_base, np.ndarray):
                logger.warning(f"PID: {os.getpid()} AsymmetricEnv.step() next_state_base (type: {type(next_state_base)}) is not ndarray or list. Attempting conversion.")
                next_state_base = np.array(next_state_base, dtype=np.float32)
            
            # Ensure next_state_base has a shape, even if it's a scalar converted to array
            if next_state_base.ndim == 0: # Handles scalar that became 0-dim array
                next_state_base = next_state_base.reshape(1) 
            logger.debug(f"PID: {os.getpid()} AsymmetricEnv.step() next_state_base shape: {next_state_base.shape}")

            done = terminated or truncated # Gymnasium standard
            info['terminated'] = terminated
            info['truncated'] = truncated

            enhanced_state = self._get_state(next_state_base)
            if not isinstance(enhanced_state, np.ndarray):
                enhanced_state = np.array(enhanced_state, dtype=np.float32)
            else:
                enhanced_state = enhanced_state.astype(np.float32)

        except Exception as e:
            logger.exception(f"PID: {os.getpid()} AsymmetricEnv.step() CRITICAL ERROR during super().step() or processing: {e}. Actions: {actions}. Returning default safe state.")
            # Ensure info dict is initialized even in catastrophic failure before this point
            if not isinstance(info, dict): info = {}
            info.update({'status': 'critical_error_in_step', 'exception_type': type(e).__name__, 'exception_msg': str(e), 'actions': actions})
            # Return a default state, 0 reward, done=True to terminate episode
            # enhanced_state is already initialized to zeros
            return enhanced_state, 0.0, True, info

        # Final check to ensure a 4-tuple is returned
        # logger.debug(f"PID: {os.getpid()} AsymmetricEnv.step() returning: enhanced_state.shape={enhanced_state.shape}, reward={reward}, done={done}")
        return enhanced_state, float(reward), bool(done), dict(info)
    
    def _get_current_market_data(self) -> Optional[pd.DataFrame]:
        """Get current market data slice.
        
        Returns:
            Current market data or None
        """
        try:
            if hasattr(self, 'day') and self.day < len(self.data.index.unique()):
                current_date = self.data.index.unique()[self.day]
                return self.data[self.data.index == current_date]
            return None
        except Exception:
            return None
    
    def _extract_symbol_price_series(self, symbol: str, lookback: int = 50) -> Optional[pd.Series]:
        """Extract price series for a symbol with lookback.
        
        Args:
            symbol: Stock symbol
            lookback: Number of days to look back
            
        Returns:
            Price series or None
        """
        try:
            # Get symbol data up to current day
            symbol_data = self.data[self.data.tic == symbol].copy()
            
            if len(symbol_data) == 0:
                return None
            
            # Get current position in data
            current_day = getattr(self, 'day', 0)
            end_idx = min(current_day + 1, len(symbol_data))
            start_idx = max(0, end_idx - lookback)
            
            # Extract price series
            price_series = symbol_data.iloc[start_idx:end_idx]['close']
            
            return price_series if len(price_series) > 0 else None
            
        except Exception as e:
            self.logger.warning(f"Failed to extract price series for {symbol}: {e}")
            return None
    
    def _calculate_reward(self, actions: np.ndarray) -> float:
        """Calculate reward with asymmetric shaping.
        
        Args:
            actions: Trading actions
            
        Returns:
            Asymmetric-shaped reward
        """
        # Get base reward from parent class
        base_reward = super()._calculate_reward(actions)
        
        # Apply asymmetric reward shaping
        asymmetric_reward = self._apply_asymmetric_reward_shaping(base_reward)
        
        return asymmetric_reward
    
    def _apply_asymmetric_reward_shaping(self, base_reward: float) -> float:
        """Apply asymmetric reward shaping.
        
        Args:
            base_reward: Base reward from environment
            
        Returns:
            Asymmetric-shaped reward
        """
        try:
            # Detect market regime (simplified)
            market_trend = self._detect_market_trend()
            
            if market_trend == 'down':
                # During downturns, penalize losses more heavily
                if base_reward < 0:
                    shaped_reward = base_reward * 1.5  # Increase penalty
                else:
                    shaped_reward = base_reward * 0.8  # Reduce gains slightly
            elif market_trend == 'up':
                # During upturns, reward gains more
                if base_reward > 0:
                    shaped_reward = base_reward * 1.3  # Increase reward
                else:
                    shaped_reward = base_reward * 0.9  # Reduce penalty slightly
            else:
                # Neutral market
                shaped_reward = base_reward
            
            return shaped_reward
            
        except Exception as e:
            self.logger.warning(f"Failed to apply asymmetric reward shaping: {e}")
            return base_reward
    
    def _detect_market_trend(self) -> str:
        """Detect current market trend.
        
        Returns:
            Market trend: 'up', 'down', or 'neutral'
        """
        try:
            # Simple trend detection based on recent portfolio performance
            if hasattr(self, 'asset_memory') and len(self.asset_memory) > 5:
                recent_returns = np.diff(self.asset_memory[-5:])
                avg_return = np.mean(recent_returns)
                
                if avg_return > 0.001:
                    return 'up'
                elif avg_return < -0.001:
                    return 'down'
                else:
                    return 'neutral'
            
            return 'neutral'
            
        except Exception:
            return 'neutral'
    
    def reset(self) -> Tuple[np.ndarray, Dict]:
        """Reset environment and clear asymmetric cache.
        
        Returns:
            Tuple of (initial_state, info_dict) for ElegantRL compatibility
        """
        # Clear asymmetric cache
        self.asymmetric_cache.clear()
        
        # Reset parent environment
        reset_result = super().reset()
        
        # Handle different return formats from FinRL
        if isinstance(reset_result, tuple):
            # FinRL sometimes returns (state, info)
            initial_state = reset_result[0]
            info_dict = reset_result[1] if len(reset_result) > 1 else {}
        else:
            # Direct state return
            initial_state = reset_result
            info_dict = {}
        
        # Ensure state is always a NumPy array
        if isinstance(initial_state, list):
            initial_state = np.array(initial_state, dtype=np.float32)
        elif isinstance(initial_state, np.ndarray):
            initial_state = initial_state.astype(np.float32)
        else:
            # Handle scalar or other types
            initial_state = np.array([float(initial_state)], dtype=np.float32)
        
        return initial_state, info_dict
    
    def step(self, actions: np.ndarray) -> tuple[np.ndarray, float, bool, bool, dict]:
        """Perform a step in the environment.

        Args:
            actions (np.ndarray): The actions to take.

        Returns:
            tuple[np.ndarray, float, bool, bool, dict]: The new state, reward, done, truncated, and info.
        """
        # Correctly unpack five values from the superclass's step method
        # Standard Gym interface returns: observation, reward, terminated, truncated, info
        # FinRL's StockTradingEnv likely adheres to this.
        pid = os.getpid() # Get current process ID
        logger.debug(f"[PID:{pid}] step: About to call super().step() with actions: {actions}. Day: {self.day} (before super().step() and self.day increment)")
        # logger.debug(f"[PID:{pid}] step: self.data.head():\n{self.data.head()} (before super().step())")
        # logger.debug(f"[PID:{pid}] step: self.df relevant slice for day {self.day}:\n{self.df.iloc[max(0, self.day-2):min(len(self.df), self.day+3)]}")

        super_step_return = None
        next_state_base, reward, terminated, truncated, info = None, None, None, None, None # Ensure they are defined

        try:
            super_step_return = super().step(actions)
            if super_step_return is None:
                logger.critical(f"[PID:{pid}] CRITICAL: super().step(actions) returned None!")
                # Fallback strategy: Use placeholder values and mark as terminated
                next_state_base = self.observation_space.sample() # Placeholder, might not be ideal for 'base'
                reward = -1000  # Penalize heavily
                terminated = True  # End episode
                truncated = False # Explicitly set truncated
                info = {'error': 'super().step() returned None', 'actions': actions, 'day_at_call': self.day}
                logger.critical(f"[PID:{pid}] Assigned placeholder values after super().step() returned None: next_state_base type: {type(next_state_base)}, reward: {reward}, terminated: {terminated}, truncated: {truncated}")
            else:
                # Attempt to unpack if super_step_return is not None
                next_state_base, reward, terminated, truncated, info = super_step_return
                logger.debug(f"[PID:{pid}] step: Successfully unpacked super().step() return.")

        except TypeError as e:
            logger.error(f"[PID:{pid}] TypeError during unpacking super().step() return: {e}. super_step_return was: {super_step_return}, actions: {actions}, day_at_call: {self.day}")
            # Fallback strategy for TypeError during unpacking
            next_state_base = self.observation_space.sample() # Placeholder
            reward = -1000  # Penalize heavily
            terminated = True  # End episode
            truncated = False # Explicitly set truncated
            info = {'error': f'TypeError unpacking super().step(): {e}', 'super_return_type': str(type(super_step_return)), 'actions': actions, 'day_at_call': self.day}
            logger.critical(f"[PID:{pid}] Assigned placeholder values after TypeError unpacking super().step().")
            # It's often better to re-raise if the agent can't handle this, but for diagnosis, we continue with defaults.
            # raise # Uncomment to make the original error visible and halt
        except Exception as e:
            logger.error(f"[PID:{pid}] Exception during super().step(actions) or its unpacking: {e}. super_step_return was: {super_step_return}, actions: {actions}, day_at_call: {self.day}")
            # Generic fallback strategy
            next_state_base = self.observation_space.sample() # Placeholder
            reward = -1000  # Penalize heavily
            terminated = True  # End episode
            truncated = False # Explicitly set truncated
            info = {'error': f'Exception in super().step(): {e}', 'super_return_type': str(type(super_step_return)), 'actions': actions, 'day_at_call': self.day}
            logger.critical(f"[PID:{pid}] Assigned placeholder values after generic Exception in super().step().")
            # raise # Uncomment to make the original error visible and halt

        logger.debug(f"[PID:{pid}] step: After super().step() call/fallback - next_state_base type: {type(next_state_base)}, reward: {reward}, terminated: {terminated}, truncated: {truncated}, info: {info}")

        # self.day and self.data are updated AFTER super().step() in the original logic.
        # This is important because super().step() might use the 'current' self.day to determine 'next_state_base'.
        # If super().step() failed and we used fallback values, self.day might not have been incremented.
        # We need to ensure self.day is incremented if the episode is not terminated by the fallback.
        if not (terminated or truncated): # Only increment day if episode is not over
            self.day += 1
        self.data = self.df.loc[self.day, :] # This might fail if self.day goes out of bounds


        # Get the enhanced state using the new base state
        # Note: _get_state now uses self.data which is updated for the current day
        enhanced_state = self._get_state() 

        # Log the shapes for debugging, especially in worker processes
        pid = os.getpid()
        logger.debug(f"[PID:{pid}] step: enhanced_state shape: {enhanced_state.shape}, dtype: {enhanced_state.dtype}")
        logger.debug(f"[PID:{pid}] step: next_state_base from super().step() shape: {next_state_base.shape if isinstance(next_state_base, np.ndarray) else type(next_state_base)}, type: {type(next_state_base)}")
        logger.debug(f"[PID:{pid}] step: reward: {reward}, terminated: {terminated}, truncated: {truncated}")

        # Ensure the state returned by _get_state is used if it's the intended observation
        # The original `next_state_base` might be from the *previous* day if super().step() calculates it before self.day is incremented.
        # If _get_state() is meant to be the *actual* next state observation, use it.
        # However, the standard is that super().step() returns the *next* state directly.
        # For now, let's assume enhanced_state is the correct one to return to the agent.

        # Check if the state shape matches the observation space
        if not self.observation_space.contains(enhanced_state):
            logger.critical(
                f"[PID:{pid}] step: State shape mismatch! "
                f"Enhanced state shape: {enhanced_state.shape}, dtype: {enhanced_state.dtype}. "
                f"Observation space shape: {self.observation_space.shape}. "
                f"Enhanced state space (expected): {self.enhanced_state_space}."
            )
            # Potentially raise an error or handle this case, e.g., by clipping or padding
            # For now, just log critically.

        # The 'done' signal should be a combination of terminated and truncated
        # The 'done' signal is typically handled by the agent based on terminated and truncated.
        # ElegantRL expects 5 values: obs, reward, terminated, truncated, info
        logger.debug(f"[PID:{os.getpid()}] FINAL RETURN: enhanced_state type: {type(enhanced_state)}, reward type: {type(reward)}, terminated type: {type(terminated)}, truncated type: {type(truncated)}, info type: {type(info)}, enhanced_state is None: {enhanced_state is None}, reward is None: {reward is None}, terminated is None: {terminated is None}, truncated is None: {truncated is None}, info is None: {info is None}")
        return enhanced_state, reward, terminated, truncated, info
    
    def _get_current_market_data(self) -> Optional[pd.DataFrame]:
        """Get current market data slice.
        
        Returns:
            Current market data or None
        """
        try:
            if hasattr(self, 'day') and self.day < len(self.data.index.unique()):
                current_date = self.data.index.unique()[self.day]
                return self.data[self.data.index == current_date]
            return None
        except Exception:
            return None
    
    def _extract_symbol_price_series(self, symbol: str, lookback: int = 50) -> Optional[pd.Series]:
        """Extract price series for a symbol with lookback.
        
        Args:
            symbol: Stock symbol
            lookback: Number of days to look back
            
        Returns:
            Price series or None
        """
        try:
            # Get symbol data up to current day
            symbol_data = self.data[self.data.tic == symbol].copy()
            
            if len(symbol_data) == 0:
                return None
            
            # Get current position in data
            current_day = getattr(self, 'day', 0)
            end_idx = min(current_day + 1, len(symbol_data))
            start_idx = max(0, end_idx - lookback)
            
            # Extract price series
            price_series = symbol_data.iloc[start_idx:end_idx]['close']
            
            return price_series if len(price_series) > 0 else None
            
        except Exception as e:
            self.logger.warning(f"Failed to extract price series for {symbol}: {e}")
            return None
    
    def _calculate_reward(self, actions: np.ndarray) -> float:
        """Calculate reward with asymmetric shaping.
        
        Args:
            actions: Trading actions
            
        Returns:
            Asymmetric-shaped reward
        """
        # Get base reward from parent class
        base_reward = super()._calculate_reward(actions)
        
        # Apply asymmetric reward shaping
        asymmetric_reward = self._apply_asymmetric_reward_shaping(base_reward)
        
        return asymmetric_reward
    
    def _apply_asymmetric_reward_shaping(self, base_reward: float) -> float:
        """Apply asymmetric reward shaping.
        
        Args:
            base_reward: Base reward from environment
            
        Returns:
            Asymmetric-shaped reward
        """
        try:
            # Detect market regime (simplified)
            market_trend = self._detect_market_trend()
            
            if market_trend == 'down':
                # During downturns, penalize losses more heavily
                if base_reward < 0:
                    shaped_reward = base_reward * 1.5  # Increase penalty
                else:
                    shaped_reward = base_reward * 0.8  # Reduce gains slightly
            elif market_trend == 'up':
                # During upturns, reward gains more
                if base_reward > 0:
                    shaped_reward = base_reward * 1.3  # Increase reward
                else:
                    shaped_reward = base_reward * 0.9  # Reduce penalty slightly
            else:
                # Neutral market
                shaped_reward = base_reward
            
            return shaped_reward
            
        except Exception as e:
            self.logger.warning(f"Failed to apply asymmetric reward shaping: {e}")
            return base_reward
    
    def _detect_market_trend(self) -> str:
        """Detect current market trend.
        
        Returns:
            Market trend: 'up', 'down', or 'neutral'
        """
        try:
            # Simple trend detection based on recent portfolio performance
            if hasattr(self, 'asset_memory') and len(self.asset_memory) > 5:
                recent_returns = np.diff(self.asset_memory[-5:])
                avg_return = np.mean(recent_returns)
                
                if avg_return > 0.001:
                    return 'up'
                elif avg_return < -0.001:
                    return 'down'
                else:
                    return 'neutral'
            
            return 'neutral'
            
        except Exception:
            return 'neutral'
    
    def reset(self) -> Tuple[np.ndarray, Dict]:
        """Reset environment and clear asymmetric cache.
        
        Returns:
            Tuple of (initial_state, info_dict) for ElegantRL compatibility
        """
        # Clear asymmetric cache
        self.asymmetric_cache.clear()
        
        # Reset parent environment
        reset_result = super().reset()
        
        # Handle different return formats from FinRL
        if isinstance(reset_result, tuple):
            # FinRL sometimes returns (state, info)
            initial_state = reset_result[0]
            info_dict = reset_result[1] if len(reset_result) > 1 else {}
        else:
            # Direct state return
            initial_state = reset_result
            info_dict = {}
        
        # Ensure state is always a NumPy array
        if isinstance(initial_state, list):
            initial_state = np.array(initial_state, dtype=np.float32)
        elif isinstance(initial_state, np.ndarray):
            initial_state = initial_state.astype(np.float32)
        else:
            # Handle scalar or other types
            initial_state = np.array([float(initial_state)], dtype=np.float32)
        
        return initial_state, info_dict
    
    def step(self, actions: np.ndarray) -> Tuple[np.ndarray, float, bool, Dict]:
        """Execute one step with asymmetric features.
        
        Args:
            actions: Trading actions
            
        Returns:
            Tuple of (state, reward, done, info) with state as NumPy array
        """
        self.actions_memory.append(actions)
        # self.day and self.data are expected to be updated by super().step()

        # logger.debug(f"PID: {os.getpid()} AsymmetricEnv.step() BEFORE super().step(): day={getattr(self, 'day', 'N/A')}, actions={actions}")

        next_state_base, reward, terminated, truncated, info = None, 0.0, False, False, {}
        enhanced_state = np.zeros(self.observation_space.shape, dtype=np.float32)
        done = True # Default to done=True if super().step() fails catastrophically

        try:
            raw_step_output = super().step(actions)

            if raw_step_output is None:
                logger.error(f"PID: {os.getpid()} AsymmetricEnv.step() super().step(actions) returned None. Actions: {actions}, Day: {getattr(self, 'day', 'N/A')}")
                info.update({'status': 'error_super_step_returned_none', 'actions': actions, 'day': getattr(self, 'day', 'N/A')})
                # Return a default state to prevent crashing, episode will terminate
                return enhanced_state, 0.0, True, info
            
            # Attempt to unpack; could be 4 or 5 items depending on Gym/Gymnasium version of parent
            if len(raw_step_output) == 5:
                next_state_base, reward, terminated, truncated, info_super = raw_step_output
                info.update(info_super) # Merge info dicts
            elif len(raw_step_output) == 4:
                # FinRL's StockTradingEnv might return 4 items (state, reward, done, info)
                next_state_base, reward, done_super, info_super = raw_step_output
                terminated = done_super # If parent returns 'done', it implies terminated or truncated
                truncated = False # Assume not truncated if only 'done' is provided by parent
                info.update(info_super)
            else:
                logger.error(f"PID: {os.getpid()} AsymmetricEnv.step() super().step(actions) returned unexpected number of items: {len(raw_step_output)}. Output: {raw_step_output}")
                info.update({'status': 'error_super_step_unexpected_items', 'output_len': len(raw_step_output), 'raw_output': raw_step_output})
                return enhanced_state, 0.0, True, info

            logger.debug(f"PID: {os.getpid()} AsymmetricEnv.step() AFTER super().step(): day={getattr(self, 'day', 'N/A')}, next_state_base type: {type(next_state_base)}, reward={reward}, terminated={terminated}, truncated={truncated}, info={info}")

            if isinstance(next_state_base, list):
                next_state_base = np.array(next_state_base, dtype=np.float32)
            elif not isinstance(next_state_base, np.ndarray):
                logger.warning(f"PID: {os.getpid()} AsymmetricEnv.step() next_state_base (type: {type(next_state_base)}) is not ndarray or list. Attempting conversion.")
                next_state_base = np.array(next_state_base, dtype=np.float32)
            
            # Ensure next_state_base has a shape, even if it's a scalar converted to array
            if next_state_base.ndim == 0: # Handles scalar that became 0-dim array
                next_state_base = next_state_base.reshape(1) 
            logger.debug(f"PID: {os.getpid()} AsymmetricEnv.step() next_state_base shape: {next_state_base.shape}")

            done = terminated or truncated # Gymnasium standard
            info['terminated'] = terminated
            info['truncated'] = truncated

            enhanced_state = self._get_state(next_state_base)
            if not isinstance(enhanced_state, np.ndarray):
                enhanced_state = np.array(enhanced_state, dtype=np.float32)
            else:
                enhanced_state = enhanced_state.astype(np.float32)

        except Exception as e:
            logger.exception(f"PID: {os.getpid()} AsymmetricEnv.step() CRITICAL ERROR during super().step() or processing: {e}. Actions: {actions}. Returning default safe state.")
            # Ensure info dict is initialized even in catastrophic failure before this point
            if not isinstance(info, dict): info = {}
            info.update({'status': 'critical_error_in_step', 'exception_type': type(e).__name__, 'exception_msg': str(e), 'actions': actions})
            # Return a default state, 0 reward, done=True to terminate episode
            # enhanced_state is already initialized to zeros
            return enhanced_state, 0.0, True, info

        # Final check to ensure a 4-tuple is returned
        # logger.debug(f"PID: {os.getpid()} AsymmetricEnv.step() returning: enhanced_state.shape={enhanced_state.shape}, reward={reward}, done={done}")
        return enhanced_state, float(reward), bool(done), dict(info)
    
    def _get_current_market_data(self) -> Optional[pd.DataFrame]:
        """Get current market data slice.
        
        Returns:
            Current market data or None
        """
        try:
            if hasattr(self, 'day') and self.day < len(self.data.index.unique()):
                current_date = self.data.index.unique()[self.day]
                return self.data[self.data.index == current_date]
            return None
        except Exception:
            return None
    
    def _extract_symbol_price_series(self, symbol: str, lookback: int = 50) -> Optional[pd.Series]:
        """Extract price series for a symbol with lookback.
        
        Args:
            symbol: Stock symbol
            lookback: Number of days to look back
            
        Returns:
            Price series or None
        """
        try:
            # Get symbol data up to current day
            symbol_data = self.data[self.data.tic == symbol].copy()
            
            if len(symbol_data) == 0:
                return None
            
            # Get current position in data
            current_day = getattr(self, 'day', 0)
            end_idx = min(current_day + 1, len(symbol_data))
            start_idx = max(0, end_idx - lookback)
            
            # Extract price series
            price_series = symbol_data.iloc[start_idx:end_idx]['close']
            
            return price_series if len(price_series) > 0 else None
            
        except Exception as e:
            self.logger.warning(f"Failed to extract price series for {symbol}: {e}")
            return None
    
    def _calculate_reward(self, actions: np.ndarray) -> float:
        """Calculate reward with asymmetric shaping.
        
        Args:
            actions: Trading actions
            
        Returns:
            Asymmetric-shaped reward
        """
        # Get base reward from parent class
        base_reward = super()._calculate_reward(actions)
        
        # Apply asymmetric reward shaping
        asymmetric_reward = self._apply_asymmetric_reward_shaping(base_reward)
        
        return asymmetric_reward
    
    def _apply_asymmetric_reward_shaping(self, base_reward: float) -> float:
        """Apply asymmetric reward shaping.
        
        Args:
            base_reward: Base reward from environment
            
        Returns:
            Asymmetric-shaped reward
        """
        try:
            # Detect market regime (simplified)
            market_trend = self._detect_market_trend()
            
            if market_trend == 'down':
                # During downturns, penalize losses more heavily
                if base_reward < 0:
                    shaped_reward = base_reward * 1.5  # Increase penalty
                else:
                    shaped_reward = base_reward * 0.8  # Reduce gains slightly
            elif market_trend == 'up':
                # During upturns, reward gains more
                if base_reward > 0:
                    shaped_reward = base_reward * 1.3  # Increase reward
                else:
                    shaped_reward = base_reward * 0.9  # Reduce penalty slightly
            else:
                # Neutral market
                shaped_reward = base_reward
            
            return shaped_reward
            
        except Exception as e:
            self.logger.warning(f"Failed to apply asymmetric reward shaping: {e}")
            return base_reward
    
    def _detect_market_trend(self) -> str:
        """Detect current market trend.
        
        Returns:
            Market trend: 'up', 'down', or 'neutral'
        """
        try:
            # Simple trend detection based on recent portfolio performance
            if hasattr(self, 'asset_memory') and len(self.asset_memory) > 5:
                recent_returns = np.diff(self.asset_memory[-5:])
                avg_return = np.mean(recent_returns)
                
                if avg_return > 0.001:
                    return 'up'
                elif avg_return < -0.001:
                    return 'down'
                else:
                    return 'neutral'
            
            return 'neutral'
            
        except Exception:
            return 'neutral'
    
    def reset(self) -> Tuple[np.ndarray, Dict]:
        """Reset environment and clear asymmetric cache.
        
        Returns:
            Tuple of (initial_state, info_dict) for ElegantRL compatibility
        """
        # Clear asymmetric cache
        self.asymmetric_cache.clear()
        
        # Reset parent environment
        reset_result = super().reset()
        
        # Handle different return formats from FinRL
        if isinstance(reset_result, tuple):
            # FinRL sometimes returns (state, info)
            initial_state = reset_result[0]
            info_dict = reset_result[1] if len(reset_result) > 1 else {}
        else:
            # Direct state return
            initial_state = reset_result
            info_dict = {}
        
        # Ensure state is always a NumPy array
        if isinstance(initial_state, list):
            initial_state = np.array(initial_state, dtype=np.float32)
        elif isinstance(initial_state, np.ndarray):
            initial_state = initial_state.astype(np.float32)
        else:
            # Handle scalar or other types
            initial_state = np.array([float(initial_state)], dtype=np.float32)
        
        return initial_state, info_dict
    
    def step(self, actions: np.ndarray) -> tuple[np.ndarray, float, bool, bool, dict]:
        """Perform a step in the environment.

        Args:
            actions (np.ndarray): The actions to take.

        Returns:
            tuple[np.ndarray, float, bool, bool, dict]: The new state, reward, done, truncated, and info.
        """
        # Correctly unpack five values from the superclass's step method
        # Standard Gym interface returns: observation, reward, terminated, truncated, info
        # FinRL's StockTradingEnv likely adheres to this.
        pid = os.getpid() # Get current process ID
        logger.debug(f"[PID:{pid}] step: About to call super().step() with actions: {actions}. Day: {self.day} (before super().step() and self.day increment)")
        # logger.debug(f"[PID:{pid}] step: self.data.head():\n{self.data.head()} (before super().step())")
        # logger.debug(f"[PID:{pid}] step: self.df relevant slice for day {self.day}:\n{self.df.iloc[max(0, self.day-2):min(len(self.df), self.day+3)]}")

        super_step_return = None
        next_state_base, reward, terminated, truncated, info = None, None, None, None, None # Ensure they are defined

        try:
            super_step_return = super().step(actions)
            if super_step_return is None:
                logger.critical(f"[PID:{pid}] CRITICAL: super().step(actions) returned None!")
                # Fallback strategy: Use placeholder values and mark as terminated
                next_state_base = self.observation_space.sample() # Placeholder, might not be ideal for 'base'
                reward = -1000  # Penalize heavily
                terminated = True  # End episode
                truncated = False # Explicitly set truncated
                info = {'error': 'super().step() returned None', 'actions': actions, 'day_at_call': self.day}
                logger.critical(f"[PID:{pid}] Assigned placeholder values after super().step() returned None: next_state_base type: {type(next_state_base)}, reward: {reward}, terminated: {terminated}, truncated: {truncated}")
            else:
                # Attempt to unpack if super_step_return is not None
                next_state_base, reward, terminated, truncated, info = super_step_return
                logger.debug(f"[PID:{pid}] step: Successfully unpacked super().step() return.")

        except TypeError as e:
            logger.error(f"[PID:{pid}] TypeError during unpacking super().step() return: {e}. super_step_return was: {super_step_return}, actions: {actions}, day_at_call: {self.day}")
            # Fallback strategy for TypeError during unpacking
            next_state_base = self.observation_space.sample() # Placeholder
            reward = -1000  # Penalize heavily
            terminated = True  # End episode
            truncated = False # Explicitly set truncated
            info = {'error': f'TypeError unpacking super().step(): {e}', 'super_return_type': str(type(super_step_return)), 'actions': actions, 'day_at_call': self.day}
            logger.critical(f"[PID:{pid}] Assigned placeholder values after TypeError unpacking super().step().")
            # It's often better to re-raise if the agent can't handle this, but for diagnosis, we continue with defaults.
            # raise # Uncomment to make the original error visible and halt
        except Exception as e:
            logger.error(f"[PID:{pid}] Exception during super().step(actions) or its unpacking: {e}. super_step_return was: {super_step_return}, actions: {actions}, day_at_call: {self.day}")
            # Generic fallback strategy
            next_state_base = self.observation_space.sample() # Placeholder
            reward = -1000  # Penalize heavily
            terminated = True  # End episode
            truncated = False # Explicitly set truncated
            info = {'error': f'Exception in super().step(): {e}', 'super_return_type': str(type(super_step_return)), 'actions': actions, 'day_at_call': self.day}
            logger.critical(f"[PID:{pid}] Assigned placeholder values after generic Exception in super().step().")
            # raise # Uncomment to make the original error visible and halt

        logger.debug(f"[PID:{pid}] step: After super().step() call/fallback - next_state_base type: {type(next_state_base)}, reward: {reward}, terminated: {terminated}, truncated: {truncated}, info: {info}")

        # self.day and self.data are updated AFTER super().step() in the original logic.
        # This is important because super().step() might use the 'current' self.day to determine 'next_state_base'.
        # If super().step() failed and we used fallback values, self.day might not have been incremented.
        # We need to ensure self.day is incremented if the episode is not terminated by the fallback.
        if not (terminated or truncated): # Only increment day if episode is not over
            self.day += 1
        self.data = self.df.loc[self.day, :] # This might fail if self.day goes out of bounds


        # Get the enhanced state using the new base state
        # Note: _get_state now uses self.data which is updated for the current day
        enhanced_state = self._get_state() 

        # Log the shapes for debugging, especially in worker processes
        pid = os.getpid()
        logger.debug(f"[PID:{pid}] step: enhanced_state shape: {enhanced_state.shape}, dtype: {enhanced_state.dtype}")
        logger.debug(f"[PID:{pid}] step: next_state_base from super().step() shape: {next_state_base.shape if isinstance(next_state_base, np.ndarray) else type(next_state_base)}, type: {type(next_state_base)}")
        logger.debug(f"[PID:{pid}] step: reward: {reward}, terminated: {terminated}, truncated: {truncated}")

        # Ensure the state returned by _get_state is used if it's the intended observation
        # The original `next_state_base` might be from the *previous* day if super().step() calculates it before self.day is incremented.
        # If _get_state() is meant to be the *actual* next state observation, use it.
        # However, the standard is that super().step() returns the *next* state directly.
        # For now, let's assume enhanced_state is the correct one to return to the agent.

        # Check if the state shape matches the observation space
        if not self.observation_space.contains(enhanced_state):
            logger.critical(
                f"[PID:{pid}] step: State shape mismatch! "
                f"Enhanced state shape: {enhanced_state.shape}, dtype: {enhanced_state.dtype}. "
                f"Observation space shape: {self.observation_space.shape}. "
                f"Enhanced state space (expected): {self.enhanced_state_space}."
            )
            # Potentially raise an error or handle this case, e.g., by clipping or padding
            # For now, just log critically.

        # The 'done' signal should be a combination of terminated and truncated
        # The 'done' signal is typically handled by the agent based on terminated and truncated.
        # ElegantRL expects 5 values: obs, reward, terminated, truncated, info
        logger.debug(f"[PID:{os.getpid()}] FINAL RETURN: enhanced_state type: {type(enhanced_state)}, reward type: {type(reward)}, terminated type: {type(terminated)}, truncated type: {type(truncated)}, info type: {type(info)}, enhanced_state is None: {enhanced_state is None}, reward is None: {reward is None}, terminated is None: {terminated is None}, truncated is None: {truncated is None}, info is None: {info is None}")
        return enhanced_state, reward, terminated, truncated, info
    
    def _get_current_market_data(self) -> Optional[pd.DataFrame]:
        """Get current market data slice.
        
        Returns:
            Current market data or None
        """
        try:
            if hasattr(self, 'day') and self.day < len(self.data.index.unique()):
                current_date = self.data.index.unique()[self.day]
                return self.data[self.data.index == current_date]
            return None
        except Exception:
            return None
    
    def _extract_symbol_price_series(self, symbol: str, lookback: int = 50) -> Optional[pd.Series]:
        """Extract price series for a symbol with lookback.
        
        Args:
            symbol: Stock symbol
            lookback: Number of days to look back
            
        Returns:
            Price series or None
        """
        try:
            # Get symbol data up to current day
            symbol_data = self.data[self.data.tic == symbol].copy()
            
            if len(symbol_data) == 0:
                return None
            
            # Get current position in data
            current_day = getattr(self, 'day', 0)
            end_idx = min(current_day + 1, len(symbol_data))
            start_idx = max(0, end_idx - lookback)
            
            # Extract price series
            price_series = symbol_data.iloc[start_idx:end_idx]['close']
            
            return price_series if len(price_series) > 0 else None
            
        except Exception as e:
            self.logger.warning(f"Failed to extract price series for {symbol}: {e}")
            return None
    
    def _calculate_reward(self, actions: np.ndarray) -> float:
        """Calculate reward with asymmetric shaping.
        
        Args:
            actions: Trading actions
            
        Returns:
            Asymmetric-shaped reward
        """
        # Get base reward from parent class
        base_reward = super()._calculate_reward(actions)
        
        # Apply asymmetric reward shaping
        asymmetric_reward = self._apply_asymmetric_reward_shaping(base_reward)
        
        return asymmetric_reward
    
    def _apply_asymmetric_reward_shaping(self, base_reward: float) -> float:
        """Apply asymmetric reward shaping.
        
        Args:
            base_reward: Base reward from environment
            
        Returns:
            Asymmetric-shaped reward
        """
        try:
            # Detect market regime (simplified)
            market_trend = self._detect_market_trend()
            
            if market_trend == 'down':
                # During downturns, penalize losses more heavily
                if base_reward < 0:
                    shaped_reward = base_reward * 1.5  # Increase penalty
                else:
                    shaped_reward = base_reward * 0.8  # Reduce gains slightly
            elif market_trend == 'up':
                # During upturns, reward gains more
                if base_reward > 0:
                    shaped_reward = base_reward * 1.3  # Increase reward
                else:
                    shaped_reward = base_reward * 0.9  # Reduce penalty slightly
            else:
                # Neutral market
                shaped_reward = base_reward
            
            return shaped_reward
            
        except Exception as e:
            self.logger.warning(f"Failed to apply asymmetric reward shaping: {e}")
            return base_reward
    
    def _detect_market_trend(self) -> str:
        """Detect current market trend.
        
        Returns:
            Market trend: 'up', 'down', or 'neutral'
        """
        try:
            # Simple trend detection based on recent portfolio performance
            if hasattr(self, 'asset_memory') and len(self.asset_memory) > 5:
                recent_returns = np.diff(self.asset_memory[-5:])
                avg_return = np.mean(recent_returns)
                
                if avg_return > 0.001:
                    return 'up'
                elif avg_return < -0.001:
                    return 'down'
                else:
                    return 'neutral'
            
            return 'neutral'
            
        except Exception:
            return 'neutral'
    
    def reset(self) -> Tuple[np.ndarray, Dict]:
        """Reset environment and clear asymmetric cache.
        
        Returns:
            Tuple of (initial_state, info_dict) for ElegantRL compatibility
        """
        # Clear asymmetric cache
        self.asymmetric_cache.clear()
        
        # Reset parent environment
        reset_result = super().reset()
        
        # Handle different return formats from FinRL
        if isinstance(reset_result, tuple):
            # FinRL sometimes returns (state, info)
            initial_state = reset_result[0]
            info_dict = reset_result[1] if len(reset_result) > 1 else {}
        else:
            # Direct state return
            initial_state = reset_result
            info_dict = {}
        
        # Ensure state is always a NumPy array
        if isinstance(initial_state, list):
            initial_state = np.array(initial_state, dtype=np.float32)
        elif isinstance(initial_state, np.ndarray):
            initial_state = initial_state.astype(np.float32)
        else:
            # Handle scalar or other types
            initial_state = np.array([float(initial_state)], dtype=np.float32)
        
        return initial_state, info_dict
    
    def step(self, actions: np.ndarray) -> tuple[np.ndarray, float, bool, bool, dict]:
        """Perform a step in the environment.

        Args:
            actions (np.ndarray): The actions to take.

        Returns:
            tuple[np.ndarray, float, bool, bool, dict]: The new state, reward, done, truncated, and info.
        """
        # Correctly unpack five values from the superclass's step method
        # Standard Gym interface returns: observation, reward, terminated, truncated, info
        # FinRL's StockTradingEnv likely adheres to this.
        pid = os.getpid() # Get current process ID
        logger.debug(f"[PID:{pid}] step: About to call super().step() with actions: {actions}. Day: {self.day} (before super().step() and self.day increment)")
        # logger.debug(f"[PID:{pid}] step: self.data.head():\n{self.data.head()} (before super().step())")
        # logger.debug(f"[PID:{pid}] step: self.df relevant slice for day {self.day}:\n{self.df.iloc[max(0, self.day-2):min(len(self.df), self.day+3)]}")

        super_step_return = None
        next_state_base, reward, terminated, truncated, info = None, None, None, None, None # Ensure they are defined

        try:
            super_step_return = super().step(actions)
            if super_step_return is None:
                logger.critical(f"[PID:{pid}] CRITICAL: super().step(actions) returned None!")
                # Fallback strategy: Use placeholder values and mark as terminated
                next_state_base = self.observation_space.sample() # Placeholder, might not be ideal for 'base'
                reward = -1000  # Penalize heavily
                terminated = True  # End episode
                truncated = False # Explicitly set truncated
                info = {'error': 'super().step() returned None', 'actions': actions, 'day_at_call': self.day}
                logger.critical(f"[PID:{pid}] Assigned placeholder values after super().step() returned None: next_state_base type: {type(next_state_base)}, reward: {reward}, terminated: {terminated}, truncated: {truncated}")
            else:
                # Attempt to unpack if super_step_return is not None
                next_state_base, reward, terminated, truncated, info = super_step_return
                logger.debug(f"[PID:{pid}] step: Successfully unpacked super().step() return.")

        except TypeError as e:
            logger.error(f"[PID:{pid}] TypeError during unpacking super().step() return: {e}. super_step_return was: {super_step_return}, actions: {actions}, day_at_call: {self.day}")
            # Fallback strategy for TypeError during unpacking
            next_state_base = self.observation_space.sample() # Placeholder
            reward = -1000  # Penalize heavily
            terminated = True  # End episode
            truncated = False # Explicitly set truncated
            info = {'error': f'TypeError unpacking super().step(): {e}', 'super_return_type': str(type(super_step_return)), 'actions': actions, 'day_at_call': self.day}
            logger.critical(f"[PID:{pid}] Assigned placeholder values after TypeError unpacking super().step().")
            # It's often better to re-raise if the agent can't handle this, but for diagnosis, we continue with defaults.
            # raise # Uncomment to make the original error visible and halt
        except Exception as e:
            logger.error(f"[PID:{pid}] Exception during super().step(actions) or its unpacking: {e}. super_step_return was: {super_step_return}, actions: {actions}, day_at_call: {self.day}")
            # Generic fallback strategy
            next_state_base = self.observation_space.sample() # Placeholder
            reward = -1000  # Penalize heavily
            terminated = True  # End episode
            truncated = False # Explicitly set truncated
            info = {'error': f'Exception in super().step(): {e}', 'super_return_type': str(type(super_step_return)), 'actions': actions, 'day_at_call': self.day}
            logger.critical(f"[PID:{pid}] Assigned placeholder values after generic Exception in super().step().")
            # raise # Uncomment to make the original error visible and halt

        logger.debug(f"[PID:{pid}] step: After super().step() call/fallback - next_state_base type: {type(next_state_base)}, reward: {reward}, terminated: {terminated}, truncated: {truncated}, info: {info}")

        # self.day and self.data are updated AFTER super().step() in the original logic.
        # This is important because super().step() might use the 'current' self.day to determine 'next_state_base'.
        # If super().step() failed and we used fallback values, self.day might not have been incremented.
        # We need to ensure self.day is incremented if the episode is not terminated by the fallback.
        if not (terminated or truncated): # Only increment day if episode is not over
            self.day += 1
        self.data = self.df.loc[self.day, :] # This might fail if self.day goes out of bounds


        # Get the enhanced state using the new base state
        # Note: _get_state now uses self.data which is updated for the current day
        enhanced_state = self._get_state() 

        # Log the shapes for debugging, especially in worker processes
        pid = os.getpid()
        logger.debug(f"[PID:{pid}] step: enhanced_state shape: {enhanced_state.shape}, dtype: {enhanced_state.dtype}")
        logger.debug(f"[PID:{pid}] step: next_state_base from super().step() shape: {next_state_base.shape if isinstance(next_state_base, np.ndarray) else type(next_state_base)}, type: {type(next_state_base)}")
        logger.debug(f"[PID:{pid}] step: reward: {reward}, terminated: {terminated}, truncated: {truncated}")

        # Ensure the state returned by _get_state is used if it's the intended observation
        # The original `next_state_base` might be from the *previous* day if super().step() calculates it before self.day is incremented.
        # If _get_state() is meant to be the *actual* next state observation, use it.
        # However, the standard is that super().step() returns the *next* state directly.
        # For now, let's assume enhanced_state is the correct one to return to the agent.

        # Check if the state shape matches the observation space
        if not self.observation_space.contains(enhanced_state):
            logger.critical(
                f"[PID:{pid}] step: State shape mismatch! "
                f"Enhanced state shape: {enhanced_state.shape}, dtype: {enhanced_state.dtype}. "
                f"Observation space shape: {self.observation_space.shape}. "
                f"Enhanced state space (expected): {self.enhanced_state_space}."
            )
            # Potentially raise an error or handle this case, e.g., by clipping or padding
            # For now, just log critically.

        # The 'done' signal should be a combination of terminated and truncated
        # The 'done' signal is typically handled by the agent based on terminated and truncated.
        # ElegantRL expects 5 values: obs, reward, terminated, truncated, info
        logger.debug(f"[PID:{os.getpid()}] FINAL RETURN: enhanced_state type: {type(enhanced_state)}, reward type: {type(reward)}, terminated type: {type(terminated)}, truncated type: {type(truncated)}, info type: {type(info)}, enhanced_state is None: {enhanced_state is None}, reward is None: {reward is None}, terminated is None: {terminated is None}, truncated is None: {truncated is None}, info is None: {info is None}")
        return enhanced_state, reward, terminated, truncated, info
    
    def _get_current_market_data(self) -> Optional[pd.DataFrame]:
        """Get current market data slice.
        
        Returns:
            Current market data or None
        """
        try:
            if hasattr(self, 'day') and self.day < len(self.data.index.unique()):
                current_date = self.data.index.unique()[self.day]
                return self.data[self.data.index == current_date]
            return None
        except Exception:
            return None
    
    def _extract_symbol_price_series(self, symbol: str, lookback: int = 50) -> Optional[pd.Series]:
        """Extract price series for a symbol with lookback.
        
        Args:
            symbol: Stock symbol
            lookback: Number of days to look back
            
        Returns:
            Price series or None
        """
        try:
            # Get symbol data up to current day
            symbol_data = self.data[self.data.tic == symbol].copy()
            
            if len(symbol_data) == 0:
                return None
            
            # Get current position in data
            current_day = getattr(self, 'day', 0)
            end_idx = min(current_day + 1, len(symbol_data))
            start_idx = max(0, end_idx - lookback)
            
            # Extract price series
            price_series = symbol_data.iloc[start_idx:end_idx]['close']
            
            return price_series if len(price_series) > 0 else None
            
        except Exception as e:
            self.logger.warning(f"Failed to extract price series for {symbol}: {e}")
            return None
    
    def _calculate_reward(self, actions: np.ndarray) -> float:
        """Calculate reward with asymmetric shaping.
        
        Args:
            actions: Trading actions
            
        Returns:
            Asymmetric-shaped reward
        """
        # Get base reward from parent class
        base_reward = super()._calculate_reward(actions)
        
        # Apply asymmetric reward shaping
        asymmetric_reward = self._apply_asymmetric_reward_shaping(base_reward)
        
        return asymmetric_reward
    
    def _apply_asymmetric_reward_shaping(self, base_reward: float) -> float:
        """Apply asymmetric reward shaping.
        
        Args:
            base_reward: Base reward from environment
            
        Returns:
            Asymmetric-shaped reward
        """
        try:
            # Detect market regime (simplified)
            market_trend = self._detect_market_trend()
            
            if market_trend == 'down':
                # During downturns, penalize losses more heavily
                if base_reward < 0:
                    shaped_reward = base_reward * 1.5  # Increase penalty
                else:
                    shaped_reward = base_reward * 0.8  # Reduce gains slightly
            elif market_trend == 'up':
                # During upturns, reward gains more
                if base_reward > 0:
                    shaped_reward = base_reward * 1.3  # Increase reward
                else:
                    shaped_reward = base_reward * 0.9  # Reduce penalty slightly
            else:
                # Neutral market
                shaped_reward = base_reward
            
            return shaped_reward
            
        except Exception as e:
            self.logger.warning(f"Failed to apply asymmetric reward shaping: {e}")
            return base_reward
    
    def _detect_market_trend(self) -> str:
        """Detect current market trend.
        
        Returns:
            Market trend: 'up', 'down', or 'neutral'
        """
        try:
            # Simple trend detection based on recent portfolio performance
            if hasattr(self, 'asset_memory') and len(self.asset_memory) > 5:
                recent_returns = np.diff(self.asset_memory[-5:])
                avg_return = np.mean(recent_returns)
                
                if avg_return > 0.001:
                    return 'up'
                elif avg_return < -0.001:
                    return 'down'
                else:
                    return 'neutral'
            
            return 'neutral'
            
        except Exception:
            return 'neutral'
    
    def reset(self) -> Tuple[np.ndarray, Dict]:
        """Reset environment and clear asymmetric cache.
        
        Returns:
            Tuple of (initial_state, info_dict) for ElegantRL compatibility
        """
        # Clear asymmetric cache
        self.asymmetric_cache.clear()
        
        # Reset parent environment
        reset_result = super().reset()
        
        # Handle different return formats from FinRL
        if isinstance(reset_result, tuple):
            # FinRL sometimes returns (state, info)
            initial_state = reset_result[0]
            info_dict = reset_result[1] if len(reset_result) > 1 else {}
        else:
            # Direct state return
            initial_state = reset_result
            info_dict = {}
        
        # Ensure state is always a NumPy array
        if isinstance(initial_state, list):
            initial_state = np.array(initial_state, dtype=np.float32)
        elif isinstance(initial_state, np.ndarray):
            initial_state = initial_state.astype(np.float32)
        else:
            # Handle scalar or other types
            initial_state = np.array([float(initial_state)], dtype=np.float32)
        
        return initial_state, info_dict
    
    def step(self, actions: np.ndarray) -> tuple[np.ndarray, float, bool, bool, dict]:
        """Perform a step in the environment.

        Args:
            actions (np.ndarray): The actions to take.

        Returns:
            tuple[np.ndarray, float, bool, bool, dict]: The new state, reward, done, truncated, and info.
        """
        # Correctly unpack five values from the superclass's step method
        # Standard Gym interface returns: observation, reward, terminated, truncated, info
        # FinRL's StockTradingEnv likely adheres to this.
        pid = os.getpid() # Get current process ID
        logger.debug(f"[PID:{pid}] step: About to call super().step() with actions: {actions}. Day: {self.day} (before super().step() and self.day increment)")
        # logger.debug(f"[PID:{pid}] step: self.data.head():\n{self.data.head()} (before super().step())")
        # logger.debug(f"[PID:{pid}] step: self.df relevant slice for day {self.day}:\n{self.df.iloc[max(0, self.day-2):min(len(self.df), self.day+3)]}")

        super_step_return = None
        next_state_base, reward, terminated, truncated, info = None, None, None, None, None # Ensure they are defined

        try:
            super_step_return = super().step(actions)
            if super_step_return is None:
                logger.critical(f"[PID:{pid}] CRITICAL: super().step(actions) returned None!")
                # Fallback strategy: Use placeholder values and mark as terminated
                next_state_base = self.observation_space.sample() # Placeholder, might not be ideal for 'base'
                reward = -1000  # Penalize heavily
                terminated = True  # End episode
                truncated = False # Explicitly set truncated
                info = {'error': 'super().step() returned None', 'actions': actions, 'day_at_call': self.day}
                logger.critical(f"[PID:{pid}] Assigned placeholder values after super().step() returned None: next_state_base type: {type(next_state_base)}, reward: {reward}, terminated: {terminated}, truncated: {truncated}")
            else:
                # Attempt to unpack if super_step_return is not None
                next_state_base, reward, terminated, truncated, info = super_step_return
                logger.debug(f"[PID:{pid}] step: Successfully unpacked super().step() return.")

        except TypeError as e:
            logger.error(f"[PID:{pid}] TypeError during unpacking super().step() return: {e}. super_step_return was: {super_step_return}, actions: {actions}, day_at_call: {self.day}")
            # Fallback strategy for TypeError during unpacking
            next_state_base = self.observation_space.sample() # Placeholder
            reward = -1000  # Penalize heavily
            terminated = True  # End episode
            truncated = False # Explicitly set truncated
            info = {'error': f'TypeError unpacking super().step(): {e}', 'super_return_type': str(type(super_step_return)), 'actions': actions, 'day_at_call': self.day}
            logger.critical(f"[PID:{pid}] Assigned placeholder values after TypeError unpacking super().step().")
            # It's often better to re-raise if the agent can't handle this, but for diagnosis, we continue with defaults.
            # raise # Uncomment to make the original error visible and halt
        except Exception as e:
            logger.error(f"[PID:{pid}] Exception during super().step(actions) or its unpacking: {e}. super_step_return was: {super_step_return}, actions: {actions}, day_at_call: {self.day}")
            # Generic fallback strategy
            next_state_base = self.observation_space.sample() # Placeholder
            reward = -1000  # Penalize heavily
            terminated = True  # End episode
            truncated = False # Explicitly set truncated
            info = {'error': f'Exception in super().step(): {e}', 'super_return_type': str(type(super_step_return)), 'actions': actions, 'day_at_call': self.day}
            logger.critical(f"[PID:{pid}] Assigned placeholder values after generic Exception in super().step().")
            # raise # Uncomment to make the original error visible and halt

        logger.debug(f"[PID:{pid}] step: After super().step() call/fallback - next_state_base type: {type(next_state_base)}, reward: {reward}, terminated: {terminated}, truncated: {truncated}, info: {info}")

        # self.day and self.data are updated AFTER super().step() in the original logic.
        # This is important because super().step() might use the 'current' self.day to determine 'next_state_base'.
        # If super().step() failed and we used fallback values, self.day might not have been incremented.
        # We need to ensure self.day is incremented if the episode is not terminated by the fallback.
        if not (terminated or truncated): # Only increment day if episode is not over
            self.day += 1
        self.data = self.df.loc[self.day, :] # This might fail if self.day goes out of bounds


        # Get the enhanced state using the new base state
        # Note: _get_state now uses self.data which is updated for the current day
        enhanced_state = self._get_state() 

        # Log the shapes for debugging, especially in worker processes
        pid = os.getpid()
        logger.debug(f"[PID:{pid}] step: enhanced_state shape: {enhanced_state.shape}, dtype: {enhanced_state.dtype}")
        logger.debug(f"[PID:{pid}] step: next_state_base from super().step() shape: {next_state_base.shape if isinstance(next_state_base, np.ndarray) else type(next_state_base)}, type: {type(next_state_base)}")
        logger.debug(f"[PID:{pid}] step: reward: {reward}, terminated: {terminated}, truncated: {truncated}")

        # Ensure the state returned by _get_state is used if it's the intended observation
        # The original `next_state_base` might be from the *previous* day if super().step() calculates it before self.day is incremented.
        # If _get_state() is meant to be the *actual* next state observation, use it.
        # However, the standard is that super().step() returns the *next* state directly.
        # For now, let's assume enhanced_state is