"""FinRL Trading Agent Source Package

This package contains the core implementation of the FinRL-based stock trading
agent with ElegantRL SAC algorithm and asymmetric return profile strategy.
"""

__version__ = "1.0.0"
__author__ = "FinRL Trading Agent"
__description__ = "Sophisticated stock trading agent using reinforcement learning"

# Package metadata
__all__ = [
    '__version__',
    '__author__',
    '__description__'
]