"""Portfolio optimization strategy for FinRL trading.

This module implements:
- Modern Portfolio Theory (MPT) optimization
- Risk parity and equal risk contribution
- Black-Litterman model implementation
- Dynamic asset allocation
- Rebalancing strategies
- Factor-based portfolio construction
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass
from datetime import datetime, timedelta
import warnings
from scipy import optimize
from scipy.linalg import inv, pinv
import cvxpy as cp

from .base_strategy import BaseStrategy, StrategyConfig, StrategySignal, Position
from config import settings
from utils import get_logger, log_performance, log_error


@dataclass
class PortfolioConfig(StrategyConfig):
    """Configuration for portfolio optimization strategy."""
    
    # Optimization method
    optimization_method: str = "mean_variance"  # mean_variance, risk_parity, black_litterman
    objective: str = "max_sharpe"  # max_sharpe, min_variance, max_return
    
    # Risk parameters
    target_volatility: float = 0.15  # Target portfolio volatility
    risk_aversion: float = 3.0  # Risk aversion parameter
    max_weight: float = 0.2  # Maximum weight per asset
    min_weight: float = 0.0  # Minimum weight per asset
    
    # Rebalancing parameters
    rebalance_frequency: str = "weekly"  # daily, weekly, monthly, quarterly
    rebalance_threshold: float = 0.05  # Rebalance if weight deviates by 5%
    transaction_cost: float = 0.001  # Transaction cost (0.1%)
    
    # Estimation parameters
    lookback_period: int = 252  # Days for parameter estimation
    min_history_days: int = 60  # Minimum days required
    shrinkage_factor: float = 0.1  # Covariance shrinkage
    
    # Black-Litterman parameters
    tau: float = 0.025  # Scaling factor for uncertainty
    confidence_level: float = 0.5  # Confidence in views (0-1)
    market_cap_weights: bool = True  # Use market cap for equilibrium
    
    # Risk parity parameters
    risk_budget_tolerance: float = 1e-6  # Convergence tolerance
    max_iterations: int = 1000  # Maximum optimization iterations
    
    # Factor model parameters
    use_factor_model: bool = False  # Enable factor-based optimization
    factor_exposure_limit: float = 0.3  # Maximum factor exposure
    
    def __post_init__(self):
        super().__post_init__()
        self.name = "PortfolioOptimizationStrategy"
        self.description = "Modern portfolio optimization and asset allocation"


class PortfolioOptimizer:
    """Portfolio optimization engine."""
    
    def __init__(self, config: PortfolioConfig):
        self.config = config
        self.logger = get_logger(self.__class__.__name__)
    
    def optimize_portfolio(
        self,
        expected_returns: pd.Series,
        covariance_matrix: pd.DataFrame,
        current_weights: Optional[pd.Series] = None,
        views: Optional[Dict[str, float]] = None
    ) -> pd.Series:
        """Optimize portfolio weights.
        
        Args:
            expected_returns: Expected returns for each asset
            covariance_matrix: Covariance matrix
            current_weights: Current portfolio weights
            views: Investor views for Black-Litterman
            
        Returns:
            Optimal portfolio weights
        """
        if self.config.optimization_method == "mean_variance":
            return self._mean_variance_optimization(expected_returns, covariance_matrix)
        elif self.config.optimization_method == "risk_parity":
            return self._risk_parity_optimization(covariance_matrix)
        elif self.config.optimization_method == "black_litterman":
            return self._black_litterman_optimization(
                expected_returns, covariance_matrix, views
            )
        else:
            raise ValueError(f"Unknown optimization method: {self.config.optimization_method}")
    
    def _mean_variance_optimization(
        self,
        expected_returns: pd.Series,
        covariance_matrix: pd.DataFrame
    ) -> pd.Series:
        """Mean-variance optimization using CVXPY.
        
        Args:
            expected_returns: Expected returns
            covariance_matrix: Covariance matrix
            
        Returns:
            Optimal weights
        """
        n_assets = len(expected_returns)
        weights = cp.Variable(n_assets)
        
        # Objective function
        if self.config.objective == "max_sharpe":
            # Maximize Sharpe ratio (approximate by maximizing return - risk penalty)
            risk_penalty = self.config.risk_aversion * cp.quad_form(weights, covariance_matrix.values)
            objective = cp.Maximize(expected_returns.values @ weights - risk_penalty)
        elif self.config.objective == "min_variance":
            objective = cp.Minimize(cp.quad_form(weights, covariance_matrix.values))
        elif self.config.objective == "max_return":
            objective = cp.Maximize(expected_returns.values @ weights)
        else:
            raise ValueError(f"Unknown objective: {self.config.objective}")
        
        # Constraints
        constraints = [
            cp.sum(weights) == 1,  # Fully invested
            weights >= self.config.min_weight,  # Minimum weight
            weights <= self.config.max_weight   # Maximum weight
        ]
        
        # Add volatility constraint if specified
        if self.config.objective == "max_return":
            portfolio_variance = cp.quad_form(weights, covariance_matrix.values)
            constraints.append(portfolio_variance <= self.config.target_volatility ** 2)
        
        # Solve optimization problem
        problem = cp.Problem(objective, constraints)
        
        try:
            problem.solve(solver=cp.ECOS)
            
            if problem.status == cp.OPTIMAL:
                optimal_weights = pd.Series(
                    weights.value,
                    index=expected_returns.index
                )
                return optimal_weights
            else:
                self.logger.warning(f"Optimization failed with status: {problem.status}")
                # Return equal weights as fallback
                return pd.Series(
                    1.0 / n_assets,
                    index=expected_returns.index
                )
        
        except Exception as e:
            self.logger.error(f"Optimization error: {e}")
            # Return equal weights as fallback
            return pd.Series(
                1.0 / n_assets,
                index=expected_returns.index
            )
    
    def _risk_parity_optimization(self, covariance_matrix: pd.DataFrame) -> pd.Series:
        """Risk parity optimization.
        
        Args:
            covariance_matrix: Covariance matrix
            
        Returns:
            Risk parity weights
        """
        n_assets = len(covariance_matrix)
        
        def risk_budget_objective(weights):
            """Objective function for risk parity."""
            weights = np.array(weights)
            portfolio_vol = np.sqrt(weights.T @ covariance_matrix.values @ weights)
            
            # Marginal risk contributions
            marginal_contrib = covariance_matrix.values @ weights / portfolio_vol
            
            # Risk contributions
            risk_contrib = weights * marginal_contrib
            
            # Target equal risk contribution
            target_contrib = portfolio_vol / n_assets
            
            # Sum of squared deviations from target
            return np.sum((risk_contrib - target_contrib) ** 2)
        
        # Constraints
        constraints = [
            {'type': 'eq', 'fun': lambda w: np.sum(w) - 1},  # Fully invested
        ]
        
        # Bounds
        bounds = [(self.config.min_weight, self.config.max_weight) for _ in range(n_assets)]
        
        # Initial guess (equal weights)
        x0 = np.array([1.0 / n_assets] * n_assets)
        
        try:
            result = optimize.minimize(
                risk_budget_objective,
                x0,
                method='SLSQP',
                bounds=bounds,
                constraints=constraints,
                options={'maxiter': self.config.max_iterations}
            )
            
            if result.success:
                return pd.Series(result.x, index=covariance_matrix.index)
            else:
                self.logger.warning("Risk parity optimization failed")
                return pd.Series(x0, index=covariance_matrix.index)
        
        except Exception as e:
            self.logger.error(f"Risk parity optimization error: {e}")
            return pd.Series(
                1.0 / n_assets,
                index=covariance_matrix.index
            )
    
    def _black_litterman_optimization(
        self,
        expected_returns: pd.Series,
        covariance_matrix: pd.DataFrame,
        views: Optional[Dict[str, float]] = None
    ) -> pd.Series:
        """Black-Litterman optimization.
        
        Args:
            expected_returns: Market equilibrium returns
            covariance_matrix: Covariance matrix
            views: Investor views {asset: expected_return}
            
        Returns:
            Black-Litterman optimal weights
        """
        try:
            # Market capitalization weights (equilibrium)
            if self.config.market_cap_weights:
                # Use equal weights as proxy for market cap weights
                w_market = pd.Series(
                    1.0 / len(expected_returns),
                    index=expected_returns.index
                )
            else:
                w_market = pd.Series(
                    1.0 / len(expected_returns),
                    index=expected_returns.index
                )
            
            # Implied equilibrium returns
            pi = self.config.risk_aversion * covariance_matrix @ w_market
            
            # If no views provided, use equilibrium
            if not views:
                return self._mean_variance_optimization(pi, covariance_matrix)
            
            # Construct views matrices
            assets = expected_returns.index.tolist()
            view_assets = [asset for asset in views.keys() if asset in assets]
            
            if not view_assets:
                return self._mean_variance_optimization(pi, covariance_matrix)
            
            # P matrix (picking matrix)
            P = np.zeros((len(view_assets), len(assets)))
            for i, asset in enumerate(view_assets):
                j = assets.index(asset)
                P[i, j] = 1.0
            
            # Q vector (view returns)
            Q = np.array([views[asset] for asset in view_assets])
            
            # Omega matrix (view uncertainty)
            omega_diag = []
            for asset in view_assets:
                asset_var = covariance_matrix.loc[asset, asset]
                view_uncertainty = self.config.tau * asset_var / self.config.confidence_level
                omega_diag.append(view_uncertainty)
            
            Omega = np.diag(omega_diag)
            
            # Black-Litterman formula
            tau_Sigma = self.config.tau * covariance_matrix.values
            
            # New expected returns
            M1 = inv(tau_Sigma) @ pi.values
            M2 = P.T @ inv(Omega) @ Q
            M3 = inv(inv(tau_Sigma) + P.T @ inv(Omega) @ P)
            
            mu_bl = M3 @ (M1 + M2)
            
            # New covariance matrix
            Sigma_bl = M3
            
            # Convert back to pandas
            mu_bl_series = pd.Series(mu_bl, index=expected_returns.index)
            Sigma_bl_df = pd.DataFrame(
                Sigma_bl,
                index=expected_returns.index,
                columns=expected_returns.index
            )
            
            # Optimize with Black-Litterman inputs
            return self._mean_variance_optimization(mu_bl_series, Sigma_bl_df)
        
        except Exception as e:
            self.logger.error(f"Black-Litterman optimization error: {e}")
            return self._mean_variance_optimization(expected_returns, covariance_matrix)


class PortfolioOptimizationStrategy(BaseStrategy):
    """Portfolio optimization strategy implementation."""
    
    def __init__(self, config: PortfolioConfig):
        """Initialize portfolio optimization strategy.
        
        Args:
            config: Portfolio optimization configuration
        """
        super().__init__(config)
        self.config: PortfolioConfig = config
        self.optimizer = PortfolioOptimizer(config)
        
        # Portfolio tracking
        self.target_weights = pd.Series(dtype=float)
        self.current_weights = pd.Series(dtype=float)
        self.last_rebalance_date = None
        self.rebalance_history = []
        
        # Market data for optimization
        self.price_data = pd.DataFrame()
        self.returns_data = pd.DataFrame()
        
        # Views for Black-Litterman
        self.investor_views = {}
        
        self.logger.info("Portfolio optimization strategy initialized")
    
    def update_market_data(self, price_data: pd.DataFrame) -> None:
        """Update market data for optimization.
        
        Args:
            price_data: Price data DataFrame
        """
        self.price_data = price_data
        self.returns_data = price_data.pct_change().dropna()
        
        # Update current weights
        self._update_current_weights()
    
    def set_investor_views(self, views: Dict[str, float]) -> None:
        """Set investor views for Black-Litterman optimization.
        
        Args:
            views: Dictionary of {asset: expected_return}
        """
        self.investor_views = views
        self.logger.info(f"Updated investor views: {views}")
    
    def generate_signals(
        self,
        data: pd.DataFrame,
        timestamp: datetime
    ) -> List[StrategySignal]:
        """Generate portfolio optimization signals.
        
        Args:
            data: Market data DataFrame
            timestamp: Current timestamp
            
        Returns:
            List of rebalancing signals
        """
        signals = []
        
        # Check if rebalancing is needed
        if self._should_rebalance(timestamp):
            # Calculate optimal portfolio weights
            optimal_weights = self._calculate_optimal_weights()
            
            if optimal_weights is not None and not optimal_weights.empty:
                # Generate rebalancing signals
                rebalance_signals = self._generate_rebalancing_signals(
                    optimal_weights, timestamp
                )
                signals.extend(rebalance_signals)
                
                # Update target weights and rebalance date
                self.target_weights = optimal_weights
                self.last_rebalance_date = timestamp
                
                # Log rebalancing
                self.rebalance_history.append({
                    'timestamp': timestamp,
                    'target_weights': optimal_weights.to_dict(),
                    'current_weights': self.current_weights.to_dict()
                })
        
        # Store signals for history
        self.signals_history.extend(signals)
        
        return signals
    
    def _should_rebalance(self, timestamp: datetime) -> bool:
        """Check if portfolio should be rebalanced.
        
        Args:
            timestamp: Current timestamp
            
        Returns:
            True if rebalancing is needed
        """
        # First rebalance
        if self.last_rebalance_date is None:
            return True
        
        # Check frequency-based rebalancing
        days_since_rebalance = (timestamp - self.last_rebalance_date).days
        
        if self.config.rebalance_frequency == "daily":
            frequency_trigger = days_since_rebalance >= 1
        elif self.config.rebalance_frequency == "weekly":
            frequency_trigger = days_since_rebalance >= 7
        elif self.config.rebalance_frequency == "monthly":
            frequency_trigger = days_since_rebalance >= 30
        elif self.config.rebalance_frequency == "quarterly":
            frequency_trigger = days_since_rebalance >= 90
        else:
            frequency_trigger = False
        
        # Check threshold-based rebalancing
        threshold_trigger = False
        if not self.target_weights.empty and not self.current_weights.empty:
            weight_deviations = abs(self.current_weights - self.target_weights)
            max_deviation = weight_deviations.max()
            threshold_trigger = max_deviation > self.config.rebalance_threshold
        
        return frequency_trigger or threshold_trigger
    
    def _calculate_optimal_weights(self) -> Optional[pd.Series]:
        """Calculate optimal portfolio weights.
        
        Returns:
            Optimal weights or None if calculation fails
        """
        try:
            # Check if we have enough data
            if len(self.returns_data) < self.config.min_history_days:
                self.logger.warning("Insufficient data for optimization")
                return None
            
            # Use recent data for estimation
            recent_returns = self.returns_data.tail(self.config.lookback_period)
            
            # Calculate expected returns (simple mean)
            expected_returns = recent_returns.mean() * 252  # Annualized
            
            # Calculate covariance matrix with shrinkage
            sample_cov = recent_returns.cov() * 252  # Annualized
            
            # Ledoit-Wolf shrinkage
            shrinkage_target = np.trace(sample_cov) / len(sample_cov) * np.eye(len(sample_cov))
            shrunk_cov = (
                (1 - self.config.shrinkage_factor) * sample_cov +
                self.config.shrinkage_factor * shrinkage_target
            )
            
            shrunk_cov = pd.DataFrame(
                shrunk_cov,
                index=sample_cov.index,
                columns=sample_cov.columns
            )
            
            # Optimize portfolio
            optimal_weights = self.optimizer.optimize_portfolio(
                expected_returns=expected_returns,
                covariance_matrix=shrunk_cov,
                current_weights=self.current_weights,
                views=self.investor_views
            )
            
            return optimal_weights
        
        except Exception as e:
            self.logger.error(f"Failed to calculate optimal weights: {e}")
            return None
    
    def _generate_rebalancing_signals(
        self,
        target_weights: pd.Series,
        timestamp: datetime
    ) -> List[StrategySignal]:
        """Generate signals to rebalance to target weights.
        
        Args:
            target_weights: Target portfolio weights
            timestamp: Current timestamp
            
        Returns:
            List of rebalancing signals
        """
        signals = []
        
        # Calculate weight differences
        current_weights = self.current_weights.reindex(target_weights.index, fill_value=0)
        weight_diff = target_weights - current_weights
        
        # Generate signals for each asset
        for symbol, weight_change in weight_diff.items():
            if abs(weight_change) < 0.001:  # Skip tiny changes
                continue
            
            # Determine signal type and strength
            if weight_change > 0:
                signal_type = "buy"
                strength = min(weight_change / self.config.max_weight, 1.0)
            else:
                signal_type = "sell"
                strength = min(abs(weight_change) / self.config.max_weight, 1.0)
            
            # Calculate confidence based on optimization method
            confidence = self._calculate_signal_confidence(symbol, target_weights)
            
            signal = StrategySignal(
                symbol=symbol,
                signal_type=signal_type,
                strength=strength,
                confidence=confidence,
                timestamp=timestamp,
                metadata={
                    'reason': 'portfolio_rebalancing',
                    'target_weight': target_weights[symbol],
                    'current_weight': current_weights[symbol],
                    'weight_change': weight_change,
                    'optimization_method': self.config.optimization_method
                }
            )
            
            signals.append(signal)
        
        return signals
    
    def _calculate_signal_confidence(self, symbol: str, target_weights: pd.Series) -> float:
        """Calculate confidence for rebalancing signal.
        
        Args:
            symbol: Asset symbol
            target_weights: Target portfolio weights
            
        Returns:
            Signal confidence (0-1)
        """
        base_confidence = 0.8
        
        # Adjust confidence based on target weight
        target_weight = target_weights[symbol]
        
        # Higher confidence for more significant allocations
        weight_factor = min(target_weight / self.config.max_weight, 1.0)
        
        # Adjust for optimization method
        if self.config.optimization_method == "risk_parity":
            method_factor = 0.9  # High confidence in risk parity
        elif self.config.optimization_method == "black_litterman":
            method_factor = 0.85  # Good confidence with views
        else:
            method_factor = 0.8  # Standard mean-variance
        
        confidence = base_confidence * weight_factor * method_factor
        return min(confidence, 1.0)
    
    def _update_current_weights(self) -> None:
        """Update current portfolio weights based on positions."""
        if self.portfolio_value <= 0:
            self.current_weights = pd.Series(dtype=float)
            return
        
        weights = {}
        for symbol, position in self.positions.items():
            if position.is_open:
                market_value = position.get_market_value()
                weight = market_value / self.portfolio_value
                weights[symbol] = weight
        
        self.current_weights = pd.Series(weights)
    
    def calculate_position_size(
        self,
        signal: StrategySignal,
        current_price: float,
        available_cash: float
    ) -> float:
        """Calculate position size based on target weights.
        
        Args:
            signal: Trading signal
            current_price: Current price
            available_cash: Available cash
            
        Returns:
            Position size (number of shares)
        """
        symbol = signal.symbol
        
        # Get target weight from signal metadata
        target_weight = signal.metadata.get('target_weight', 0)
        
        if target_weight <= 0:
            return 0
        
        # Calculate target position value
        target_value = target_weight * self.portfolio_value
        
        # Get current position value
        current_position = self.positions.get(symbol)
        current_value = current_position.get_market_value() if current_position and current_position.is_open else 0
        
        # Calculate required change in position value
        value_change = target_value - current_value
        
        if signal.signal_type == "buy" and value_change > 0:
            # Calculate shares to buy
            shares_to_buy = value_change / current_price
            
            # Account for transaction costs
            cost_adjusted_shares = shares_to_buy / (1 + self.config.transaction_cost)
            
            # Check available cash
            max_affordable = available_cash / current_price * 0.95
            
            return min(cost_adjusted_shares, max_affordable)
        
        elif signal.signal_type == "sell" and value_change < 0:
            # Calculate shares to sell
            shares_to_sell = abs(value_change) / current_price
            
            # Check available shares
            if current_position and current_position.is_open:
                max_sellable = current_position.quantity
                return min(shares_to_sell, max_sellable)
        
        return 0
    
    def get_portfolio_analytics(self) -> Dict[str, Any]:
        """Get comprehensive portfolio analytics.
        
        Returns:
            Portfolio analytics dictionary
        """
        analytics = {
            'current_weights': self.current_weights.to_dict(),
            'target_weights': self.target_weights.to_dict(),
            'weight_deviations': (self.current_weights - self.target_weights).to_dict() if not self.target_weights.empty else {},
            'last_rebalance_date': self.last_rebalance_date,
            'rebalance_count': len(self.rebalance_history),
            'optimization_method': self.config.optimization_method
        }
        
        # Portfolio risk metrics
        if not self.returns_data.empty and len(self.current_weights) > 0:
            try:
                # Portfolio returns
                portfolio_returns = (self.returns_data * self.current_weights).sum(axis=1)
                
                if len(portfolio_returns) > 0:
                    analytics.update({
                        'portfolio_volatility': portfolio_returns.std() * np.sqrt(252),
                        'portfolio_return': portfolio_returns.mean() * 252,
                        'sharpe_ratio': portfolio_returns.mean() / portfolio_returns.std() * np.sqrt(252) if portfolio_returns.std() > 0 else 0,
                        'max_drawdown': self._calculate_max_drawdown(portfolio_returns)
                    })
            
            except Exception as e:
                self.logger.warning(f"Failed to calculate portfolio metrics: {e}")
        
        # Diversification metrics
        if len(self.current_weights) > 0:
            analytics.update({
                'effective_number_of_assets': 1 / (self.current_weights ** 2).sum(),
                'concentration_ratio': self.current_weights.nlargest(3).sum(),
                'max_weight': self.current_weights.max(),
                'min_weight': self.current_weights.min()
            })
        
        return analytics
    
    def _calculate_max_drawdown(self, returns: pd.Series) -> float:
        """Calculate maximum drawdown from returns series.
        
        Args:
            returns: Returns series
            
        Returns:
            Maximum drawdown
        """
        cumulative = (1 + returns).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        return abs(drawdown.min())
    
    def get_rebalancing_history(self) -> List[Dict[str, Any]]:
        """Get rebalancing history.
        
        Returns:
            List of rebalancing events
        """
        return self.rebalance_history.copy()