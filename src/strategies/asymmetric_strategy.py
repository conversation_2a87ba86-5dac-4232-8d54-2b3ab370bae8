"""Asymmetric return strategy for FinRL trading.

This module implements:
- Asymmetric return profile targeting
- Upside/downside ratio optimization
- Dynamic position sizing based on asymmetry
- Risk-adjusted signal generation
- Momentum and mean reversion detection
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime
import warnings

from .base_strategy import BaseStrategy, StrategyConfig, StrategySignal
from config import settings
from utils import get_logger, log_performance


@dataclass
class AsymmetricConfig(StrategyConfig):
    """Configuration for asymmetric return strategy."""
    
    # Asymmetric parameters
    target_upside_downside_ratio: float = 2.0  # Target upside/downside ratio
    momentum_threshold: float = 0.02  # Momentum detection threshold
    mean_reversion_threshold: float = 0.05  # Mean reversion threshold
    volatility_lookback: int = 20  # Volatility calculation window
    
    # Signal generation
    rsi_period: int = 14  # RSI period
    rsi_oversold: float = 30  # RSI oversold level
    rsi_overbought: float = 70  # RSI overbought level
    
    # Moving averages
    fast_ma_period: int = 10  # Fast moving average
    slow_ma_period: int = 20  # Slow moving average
    
    # Bollinger Bands
    bb_period: int = 20  # Bollinger Bands period
    bb_std: float = 2.0  # Bollinger Bands standard deviation
    
    # Position sizing
    base_position_size: float = 0.05  # Base position size
    max_asymmetric_multiplier: float = 2.0  # Maximum asymmetric multiplier
    min_asymmetric_multiplier: float = 0.5  # Minimum asymmetric multiplier
    
    # Risk management
    asymmetric_stop_loss: float = 0.03  # Asymmetric stop loss
    asymmetric_take_profit: float = 0.06  # Asymmetric take profit
    
    def __post_init__(self):
        super().__post_init__()
        self.name = "AsymmetricStrategy"
        self.description = "Strategy targeting asymmetric return profiles"


class AsymmetricStrategy(BaseStrategy):
    """Asymmetric return strategy implementation."""
    
    def __init__(self, config: AsymmetricConfig):
        """Initialize asymmetric strategy.
        
        Args:
            config: Asymmetric strategy configuration
        """
        super().__init__(config)
        self.config: AsymmetricConfig = config
        
        # Strategy state
        self.price_history = {}
        self.volatility_history = {}
        self.asymmetry_scores = {}
        
        self.logger.info(f"Asymmetric strategy initialized with target ratio: {config.target_upside_downside_ratio}")
    
    def generate_signals(
        self,
        data: pd.DataFrame,
        timestamp: datetime
    ) -> List[StrategySignal]:
        """Generate asymmetric trading signals.
        
        Args:
            data: Market data DataFrame
            timestamp: Current timestamp
            
        Returns:
            List of trading signals
        """
        signals = []
        
        for symbol in self.config.symbols:
            if symbol not in data.columns:
                continue
            
            try:
                # Get symbol data
                symbol_data = self._get_symbol_data(data, symbol)
                if symbol_data is None or len(symbol_data) < self.config.slow_ma_period:
                    continue
                
                # Calculate technical indicators
                indicators = self._calculate_indicators(symbol_data)
                
                # Calculate asymmetry metrics
                asymmetry_score = self._calculate_asymmetry_score(symbol_data)
                self.asymmetry_scores[symbol] = asymmetry_score
                
                # Generate signal
                signal = self._generate_symbol_signal(
                    symbol, symbol_data, indicators, asymmetry_score, timestamp
                )
                
                if signal:
                    signals.append(signal)
                    
            except Exception as e:
                self.logger.warning(f"Failed to generate signal for {symbol}: {e}")
                continue
        
        # Store signals for history
        self.signals_history.extend(signals)
        
        return signals
    
    def _get_symbol_data(self, data: pd.DataFrame, symbol: str) -> Optional[pd.Series]:
        """Extract symbol data from DataFrame.
        
        Args:
            data: Market data DataFrame
            symbol: Symbol to extract
            
        Returns:
            Symbol price series or None
        """
        # Try different column naming conventions
        possible_columns = [
            symbol,
            f"{symbol}_close",
            f"{symbol}_Close",
            f"close_{symbol}",
            f"Close_{symbol}"
        ]
        
        for col in possible_columns:
            if col in data.columns:
                return data[col].dropna()
        
        return None
    
    def _calculate_indicators(self, price_data: pd.Series) -> Dict[str, Any]:
        """Calculate technical indicators.
        
        Args:
            price_data: Price series
            
        Returns:
            Dictionary of indicators
        """
        indicators = {}
        
        try:
            # Moving averages
            indicators['fast_ma'] = price_data.rolling(self.config.fast_ma_period).mean().iloc[-1]
            indicators['slow_ma'] = price_data.rolling(self.config.slow_ma_period).mean().iloc[-1]
            
            # RSI
            indicators['rsi'] = self._calculate_rsi(price_data, self.config.rsi_period)
            
            # Bollinger Bands
            bb_data = self._calculate_bollinger_bands(price_data)
            indicators.update(bb_data)
            
            # Volatility
            returns = price_data.pct_change().dropna()
            indicators['volatility'] = returns.rolling(self.config.volatility_lookback).std().iloc[-1]
            
            # Price momentum
            indicators['momentum'] = (price_data.iloc[-1] / price_data.iloc[-self.config.fast_ma_period] - 1)
            
            # Current price
            indicators['current_price'] = price_data.iloc[-1]
            
        except Exception as e:
            self.logger.warning(f"Failed to calculate indicators: {e}")
            return {}
        
        return indicators
    
    def _calculate_rsi(self, price_data: pd.Series, period: int) -> float:
        """Calculate RSI indicator.
        
        Args:
            price_data: Price series
            period: RSI period
            
        Returns:
            RSI value
        """
        delta = price_data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi.iloc[-1] if not pd.isna(rsi.iloc[-1]) else 50.0
    
    def _calculate_bollinger_bands(self, price_data: pd.Series) -> Dict[str, float]:
        """Calculate Bollinger Bands.
        
        Args:
            price_data: Price series
            
        Returns:
            Bollinger Bands data
        """
        sma = price_data.rolling(self.config.bb_period).mean()
        std = price_data.rolling(self.config.bb_period).std()
        
        upper_band = sma + (std * self.config.bb_std)
        lower_band = sma - (std * self.config.bb_std)
        
        current_price = price_data.iloc[-1]
        
        return {
            'bb_upper': upper_band.iloc[-1],
            'bb_lower': lower_band.iloc[-1],
            'bb_middle': sma.iloc[-1],
            'bb_position': (current_price - lower_band.iloc[-1]) / (upper_band.iloc[-1] - lower_band.iloc[-1])
        }
    
    def _calculate_asymmetry_score(self, price_data: pd.Series) -> float:
        """Calculate asymmetry score for the symbol.
        
        Args:
            price_data: Price series
            
        Returns:
            Asymmetry score
        """
        try:
            # Calculate returns
            returns = price_data.pct_change().dropna()
            
            if len(returns) < self.config.volatility_lookback:
                return 0.0
            
            # Recent returns for asymmetry calculation
            recent_returns = returns.tail(self.config.volatility_lookback)
            
            # Separate positive and negative returns
            positive_returns = recent_returns[recent_returns > 0]
            negative_returns = recent_returns[recent_returns < 0]
            
            if len(positive_returns) == 0 or len(negative_returns) == 0:
                return 0.0
            
            # Calculate upside and downside metrics
            upside_mean = positive_returns.mean()
            downside_mean = abs(negative_returns.mean())
            
            upside_volatility = positive_returns.std()
            downside_volatility = negative_returns.std()
            
            # Asymmetry ratio (upside potential vs downside risk)
            if downside_mean > 0:
                return_asymmetry = upside_mean / downside_mean
            else:
                return_asymmetry = 1.0
            
            if downside_volatility > 0:
                volatility_asymmetry = upside_volatility / downside_volatility
            else:
                volatility_asymmetry = 1.0
            
            # Combined asymmetry score
            asymmetry_score = (return_asymmetry + volatility_asymmetry) / 2
            
            # Normalize to [-1, 1] range
            asymmetry_score = np.tanh(asymmetry_score - 1)
            
            return asymmetry_score
            
        except Exception as e:
            self.logger.warning(f"Failed to calculate asymmetry score: {e}")
            return 0.0
    
    def _generate_symbol_signal(
        self,
        symbol: str,
        price_data: pd.Series,
        indicators: Dict[str, Any],
        asymmetry_score: float,
        timestamp: datetime
    ) -> Optional[StrategySignal]:
        """Generate signal for a specific symbol.
        
        Args:
            symbol: Trading symbol
            price_data: Price data
            indicators: Technical indicators
            asymmetry_score: Asymmetry score
            timestamp: Current timestamp
            
        Returns:
            Trading signal or None
        """
        if not indicators:
            return None
        
        # Initialize signal components
        signal_strength = 0.0
        signal_type = "hold"
        confidence = 0.0
        
        # Technical analysis signals
        tech_signals = self._get_technical_signals(indicators)
        
        # Asymmetry-based adjustments
        asymmetry_adjustment = self._get_asymmetry_adjustment(asymmetry_score)
        
        # Combine signals
        combined_strength = tech_signals['strength'] * asymmetry_adjustment
        
        # Determine signal type and strength
        if combined_strength > self.config.signal_threshold:
            signal_type = "buy"
            signal_strength = min(combined_strength, 1.0)
        elif combined_strength < -self.config.signal_threshold:
            signal_type = "sell"
            signal_strength = min(abs(combined_strength), 1.0)
        else:
            signal_type = "hold"
            signal_strength = abs(combined_strength)
        
        # Calculate confidence based on multiple factors
        confidence = self._calculate_signal_confidence(
            tech_signals, asymmetry_score, indicators
        )
        
        # Create signal metadata
        metadata = {
            'asymmetry_score': asymmetry_score,
            'technical_strength': tech_signals['strength'],
            'asymmetry_adjustment': asymmetry_adjustment,
            'indicators': indicators,
            'upside_downside_ratio': self._estimate_upside_downside_ratio(indicators)
        }
        
        return StrategySignal(
            symbol=symbol,
            signal_type=signal_type,
            strength=signal_strength,
            confidence=confidence,
            timestamp=timestamp,
            metadata=metadata
        )
    
    def _get_technical_signals(self, indicators: Dict[str, Any]) -> Dict[str, float]:
        """Get technical analysis signals.
        
        Args:
            indicators: Technical indicators
            
        Returns:
            Technical signals dictionary
        """
        signals = []
        
        # Moving average crossover
        if indicators['fast_ma'] > indicators['slow_ma']:
            ma_signal = (indicators['fast_ma'] / indicators['slow_ma'] - 1) * 10
        else:
            ma_signal = (indicators['fast_ma'] / indicators['slow_ma'] - 1) * 10
        signals.append(np.clip(ma_signal, -1, 1))
        
        # RSI signals
        rsi = indicators['rsi']
        if rsi < self.config.rsi_oversold:
            rsi_signal = (self.config.rsi_oversold - rsi) / self.config.rsi_oversold
        elif rsi > self.config.rsi_overbought:
            rsi_signal = -(rsi - self.config.rsi_overbought) / (100 - self.config.rsi_overbought)
        else:
            rsi_signal = 0
        signals.append(np.clip(rsi_signal, -1, 1))
        
        # Bollinger Bands signals
        bb_position = indicators['bb_position']
        if bb_position < 0.2:  # Near lower band
            bb_signal = 0.5
        elif bb_position > 0.8:  # Near upper band
            bb_signal = -0.5
        else:
            bb_signal = 0
        signals.append(bb_signal)
        
        # Momentum signals
        momentum = indicators['momentum']
        if abs(momentum) > self.config.momentum_threshold:
            momentum_signal = np.sign(momentum) * min(abs(momentum) / self.config.momentum_threshold, 1)
        else:
            momentum_signal = 0
        signals.append(momentum_signal)
        
        # Combine signals with weights
        weights = [0.3, 0.25, 0.25, 0.2]  # MA, RSI, BB, Momentum
        combined_strength = sum(s * w for s, w in zip(signals, weights))
        
        return {
            'strength': np.clip(combined_strength, -1, 1),
            'components': {
                'ma_signal': signals[0],
                'rsi_signal': signals[1],
                'bb_signal': signals[2],
                'momentum_signal': signals[3]
            }
        }
    
    def _get_asymmetry_adjustment(self, asymmetry_score: float) -> float:
        """Get asymmetry-based signal adjustment.
        
        Args:
            asymmetry_score: Asymmetry score
            
        Returns:
            Asymmetry adjustment factor
        """
        # Favor signals for assets with better asymmetric profiles
        if asymmetry_score > 0:
            # Positive asymmetry (good upside/downside ratio)
            adjustment = 1 + (asymmetry_score * 0.5)
        else:
            # Negative asymmetry (poor upside/downside ratio)
            adjustment = 1 + (asymmetry_score * 0.3)
        
        return np.clip(adjustment, self.config.min_asymmetric_multiplier, self.config.max_asymmetric_multiplier)
    
    def _calculate_signal_confidence(
        self,
        tech_signals: Dict[str, Any],
        asymmetry_score: float,
        indicators: Dict[str, Any]
    ) -> float:
        """Calculate signal confidence.
        
        Args:
            tech_signals: Technical signals
            asymmetry_score: Asymmetry score
            indicators: Technical indicators
            
        Returns:
            Signal confidence [0, 1]
        """
        confidence_factors = []
        
        # Technical signal consistency
        components = tech_signals['components']
        signal_consistency = 1 - np.std(list(components.values()))
        confidence_factors.append(max(signal_consistency, 0))
        
        # Asymmetry score confidence
        asymmetry_confidence = (asymmetry_score + 1) / 2  # Normalize to [0, 1]
        confidence_factors.append(asymmetry_confidence)
        
        # Volatility confidence (lower volatility = higher confidence)
        volatility = indicators.get('volatility', 0.02)
        volatility_confidence = max(0, 1 - (volatility * 20))  # Assume 5% vol = 0 confidence
        confidence_factors.append(volatility_confidence)
        
        # Volume confirmation (if available)
        # This would require volume data in indicators
        
        return np.mean(confidence_factors)
    
    def _estimate_upside_downside_ratio(self, indicators: Dict[str, Any]) -> float:
        """Estimate potential upside/downside ratio.
        
        Args:
            indicators: Technical indicators
            
        Returns:
            Estimated upside/downside ratio
        """
        current_price = indicators['current_price']
        bb_upper = indicators['bb_upper']
        bb_lower = indicators['bb_lower']
        
        # Estimate upside potential to upper Bollinger Band
        upside_potential = (bb_upper - current_price) / current_price
        
        # Estimate downside risk to lower Bollinger Band
        downside_risk = (current_price - bb_lower) / current_price
        
        if downside_risk > 0:
            return upside_potential / downside_risk
        else:
            return 1.0
    
    def calculate_position_size(
        self,
        signal: StrategySignal,
        current_price: float,
        available_cash: float
    ) -> float:
        """Calculate position size based on asymmetric strategy.
        
        Args:
            signal: Trading signal
            current_price: Current price
            available_cash: Available cash
            
        Returns:
            Position size (number of shares)
        """
        # Base position size
        base_size = self.config.base_position_size * self.portfolio_value / current_price
        
        # Asymmetry-based adjustment
        asymmetry_score = signal.metadata.get('asymmetry_score', 0)
        asymmetry_multiplier = self._get_asymmetry_adjustment(asymmetry_score)
        
        # Signal strength adjustment
        strength_multiplier = signal.strength * signal.confidence
        
        # Calculate final position size
        position_size = base_size * asymmetry_multiplier * strength_multiplier
        
        # Apply maximum position size constraint
        max_size = self.config.max_position_size * self.portfolio_value / current_price
        position_size = min(position_size, max_size)
        
        # Check available cash
        max_affordable = available_cash / current_price * 0.95  # Leave some buffer
        position_size = min(position_size, max_affordable)
        
        return max(0, position_size)
    
    def get_strategy_metrics(self) -> Dict[str, Any]:
        """Get asymmetric strategy specific metrics.
        
        Returns:
            Strategy metrics dictionary
        """
        base_metrics = self.get_performance_metrics()
        
        # Calculate asymmetric-specific metrics
        if self.trades:
            returns = [trade['return_pct'] for trade in self.trades]
            positive_returns = [r for r in returns if r > 0]
            negative_returns = [r for r in returns if r < 0]
            
            if positive_returns and negative_returns:
                avg_upside = np.mean(positive_returns)
                avg_downside = abs(np.mean(negative_returns))
                actual_upside_downside_ratio = avg_upside / avg_downside if avg_downside > 0 else 0
            else:
                actual_upside_downside_ratio = 0
            
            # Asymmetry achievement score
            asymmetry_achievement = (
                actual_upside_downside_ratio / self.config.target_upside_downside_ratio
                if self.config.target_upside_downside_ratio > 0 else 0
            )
        else:
            actual_upside_downside_ratio = 0
            asymmetry_achievement = 0
        
        asymmetric_metrics = {
            'target_upside_downside_ratio': self.config.target_upside_downside_ratio,
            'actual_upside_downside_ratio': actual_upside_downside_ratio,
            'asymmetry_achievement_score': asymmetry_achievement,
            'avg_asymmetry_score': np.mean(list(self.asymmetry_scores.values())) if self.asymmetry_scores else 0
        }
        
        return {**base_metrics, **asymmetric_metrics}