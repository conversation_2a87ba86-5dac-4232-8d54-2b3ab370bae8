"""VIX-based strategy for FinRL trading.

This module implements:
- VIX regime detection and classification
- Volatility-based position sizing
- Market fear/greed indicators
- Regime-specific trading rules
- Volatility breakout detection
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
import warnings

from .base_strategy import BaseStrategy, StrategyConfig, StrategySignal
from config import settings, VIX_REGIMES
from utils import get_logger, log_performance


@dataclass
class VIXConfig(StrategyConfig):
    """Configuration for VIX-based strategy."""
    
    # VIX parameters
    vix_low_threshold: float = 15.0  # Low volatility threshold
    vix_medium_threshold: float = 25.0  # Medium volatility threshold
    vix_high_threshold: float = 35.0  # High volatility threshold
    vix_extreme_threshold: float = 50.0  # Extreme volatility threshold
    
    # Regime detection
    regime_lookback: int = 10  # Days to look back for regime detection
    regime_smoothing: int = 3  # Smoothing period for regime changes
    
    # Position sizing by regime
    low_vol_position_size: float = 0.08  # Position size in low volatility
    medium_vol_position_size: float = 0.06  # Position size in medium volatility
    high_vol_position_size: float = 0.04  # Position size in high volatility
    extreme_vol_position_size: float = 0.02  # Position size in extreme volatility
    
    # Strategy parameters by regime
    low_vol_signal_threshold: float = 0.3  # Signal threshold in low vol
    medium_vol_signal_threshold: float = 0.4  # Signal threshold in medium vol
    high_vol_signal_threshold: float = 0.6  # Signal threshold in high vol
    extreme_vol_signal_threshold: float = 0.8  # Signal threshold in extreme vol
    
    # VIX-specific indicators
    vix_ma_period: int = 10  # VIX moving average period
    vix_percentile_period: int = 252  # Period for VIX percentile calculation
    
    # Volatility breakout
    vol_breakout_threshold: float = 2.0  # Standard deviations for breakout
    vol_breakout_period: int = 20  # Period for volatility breakout calculation
    
    # Fear/Greed adjustments
    fear_adjustment_factor: float = 1.5  # Multiplier during fear periods
    greed_adjustment_factor: float = 0.7  # Multiplier during greed periods
    
    def __post_init__(self):
        super().__post_init__()
        self.name = "VIXStrategy"
        self.description = "Strategy based on VIX volatility regimes"


class VIXStrategy(BaseStrategy):
    """VIX-based trading strategy implementation."""
    
    def __init__(self, config: VIXConfig):
        """Initialize VIX strategy.
        
        Args:
            config: VIX strategy configuration
        """
        super().__init__(config)
        self.config: VIXConfig = config
        
        # VIX data storage
        self.vix_data = pd.Series(dtype=float)
        self.vix_regimes = []
        self.current_regime = "medium"
        
        # Regime history
        self.regime_history = []
        self.regime_transitions = []
        
        # Volatility metrics
        self.volatility_metrics = {}
        
        self.logger.info("VIX strategy initialized with volatility regime detection")
    
    def update_vix_data(self, vix_data: pd.Series) -> None:
        """Update VIX data for strategy.
        
        Args:
            vix_data: VIX time series data
        """
        self.vix_data = vix_data
        self._detect_current_regime()
        self.logger.info(f"VIX data updated, current regime: {self.current_regime}")
    
    def _detect_current_regime(self) -> None:
        """Detect current volatility regime based on VIX."""
        if len(self.vix_data) == 0:
            self.current_regime = "medium"
            return
        
        # Get recent VIX values
        recent_vix = self.vix_data.tail(self.config.regime_lookback)
        current_vix = self.vix_data.iloc[-1]
        avg_recent_vix = recent_vix.mean()
        
        # Classify regime based on current and recent VIX
        regime_score = (current_vix * 0.7) + (avg_recent_vix * 0.3)
        
        if regime_score < self.config.vix_low_threshold:
            new_regime = "low"
        elif regime_score < self.config.vix_medium_threshold:
            new_regime = "medium"
        elif regime_score < self.config.vix_high_threshold:
            new_regime = "high"
        else:
            new_regime = "extreme"
        
        # Apply smoothing to prevent frequent regime changes
        if len(self.regime_history) >= self.config.regime_smoothing:
            recent_regimes = self.regime_history[-self.config.regime_smoothing:]
            if recent_regimes.count(new_regime) >= self.config.regime_smoothing - 1:
                if self.current_regime != new_regime:
                    self.regime_transitions.append({
                        'timestamp': datetime.now(),
                        'from_regime': self.current_regime,
                        'to_regime': new_regime,
                        'vix_level': current_vix
                    })
                    self.logger.info(f"Regime transition: {self.current_regime} -> {new_regime} (VIX: {current_vix:.2f})")
                
                self.current_regime = new_regime
        
        self.regime_history.append(new_regime)
        
        # Keep history manageable
        if len(self.regime_history) > 100:
            self.regime_history = self.regime_history[-50:]
    
    def generate_signals(
        self,
        data: pd.DataFrame,
        timestamp: datetime
    ) -> List[StrategySignal]:
        """Generate VIX-based trading signals.
        
        Args:
            data: Market data DataFrame
            timestamp: Current timestamp
            
        Returns:
            List of trading signals
        """
        signals = []
        
        # Update VIX regime if VIX data is available
        if 'VIX' in data.columns or '^VIX' in data.columns:
            vix_col = 'VIX' if 'VIX' in data.columns else '^VIX'
            self.vix_data = data[vix_col].dropna()
            self._detect_current_regime()
        
        # Calculate VIX-based metrics
        vix_metrics = self._calculate_vix_metrics()
        
        for symbol in self.config.symbols:
            if symbol not in data.columns:
                continue
            
            try:
                # Get symbol data
                symbol_data = self._get_symbol_data(data, symbol)
                if symbol_data is None or len(symbol_data) < 20:
                    continue
                
                # Calculate technical indicators
                indicators = self._calculate_indicators(symbol_data)
                
                # Generate VIX-adjusted signal
                signal = self._generate_vix_signal(
                    symbol, symbol_data, indicators, vix_metrics, timestamp
                )
                
                if signal:
                    signals.append(signal)
                    
            except Exception as e:
                self.logger.warning(f"Failed to generate VIX signal for {symbol}: {e}")
                continue
        
        # Store signals for history
        self.signals_history.extend(signals)
        
        return signals
    
    def _get_symbol_data(self, data: pd.DataFrame, symbol: str) -> Optional[pd.Series]:
        """Extract symbol data from DataFrame.
        
        Args:
            data: Market data DataFrame
            symbol: Symbol to extract
            
        Returns:
            Symbol price series or None
        """
        # Try different column naming conventions
        possible_columns = [
            symbol,
            f"{symbol}_close",
            f"{symbol}_Close",
            f"close_{symbol}",
            f"Close_{symbol}"
        ]
        
        for col in possible_columns:
            if col in data.columns:
                return data[col].dropna()
        
        return None
    
    def _calculate_vix_metrics(self) -> Dict[str, Any]:
        """Calculate VIX-based metrics.
        
        Returns:
            VIX metrics dictionary
        """
        metrics = {
            'current_regime': self.current_regime,
            'vix_level': 0,
            'vix_percentile': 50,
            'vix_ma': 0,
            'vix_trend': 0,
            'fear_greed_indicator': 0,
            'volatility_breakout': False
        }
        
        if len(self.vix_data) == 0:
            return metrics
        
        current_vix = self.vix_data.iloc[-1]
        metrics['vix_level'] = current_vix
        
        # VIX moving average
        if len(self.vix_data) >= self.config.vix_ma_period:
            vix_ma = self.vix_data.rolling(self.config.vix_ma_period).mean().iloc[-1]
            metrics['vix_ma'] = vix_ma
            metrics['vix_trend'] = (current_vix - vix_ma) / vix_ma
        
        # VIX percentile
        if len(self.vix_data) >= self.config.vix_percentile_period:
            vix_percentile = (
                (self.vix_data.tail(self.config.vix_percentile_period) <= current_vix).sum() /
                self.config.vix_percentile_period * 100
            )
            metrics['vix_percentile'] = vix_percentile
        
        # Fear/Greed indicator
        if metrics['vix_percentile'] > 80:
            metrics['fear_greed_indicator'] = 1  # Fear
        elif metrics['vix_percentile'] < 20:
            metrics['fear_greed_indicator'] = -1  # Greed
        else:
            metrics['fear_greed_indicator'] = 0  # Neutral
        
        # Volatility breakout detection
        if len(self.vix_data) >= self.config.vol_breakout_period:
            vix_rolling = self.vix_data.tail(self.config.vol_breakout_period)
            vix_mean = vix_rolling.mean()
            vix_std = vix_rolling.std()
            
            if current_vix > vix_mean + (self.config.vol_breakout_threshold * vix_std):
                metrics['volatility_breakout'] = True
        
        return metrics
    
    def _calculate_indicators(self, price_data: pd.Series) -> Dict[str, Any]:
        """Calculate technical indicators.
        
        Args:
            price_data: Price series
            
        Returns:
            Dictionary of indicators
        """
        indicators = {}
        
        try:
            # Price-based indicators
            indicators['current_price'] = price_data.iloc[-1]
            
            # Returns and volatility
            returns = price_data.pct_change().dropna()
            indicators['returns'] = returns.iloc[-1] if len(returns) > 0 else 0
            
            if len(returns) >= 20:
                indicators['realized_volatility'] = returns.rolling(20).std().iloc[-1] * np.sqrt(252)
            else:
                indicators['realized_volatility'] = 0
            
            # Moving averages
            if len(price_data) >= 10:
                indicators['sma_10'] = price_data.rolling(10).mean().iloc[-1]
            if len(price_data) >= 20:
                indicators['sma_20'] = price_data.rolling(20).mean().iloc[-1]
            
            # Price momentum
            if len(price_data) >= 5:
                indicators['momentum_5d'] = (price_data.iloc[-1] / price_data.iloc[-5] - 1)
            if len(price_data) >= 10:
                indicators['momentum_10d'] = (price_data.iloc[-1] / price_data.iloc[-10] - 1)
            
            # Volatility-adjusted momentum
            if 'realized_volatility' in indicators and indicators['realized_volatility'] > 0:
                indicators['vol_adj_momentum'] = (
                    indicators.get('momentum_5d', 0) / indicators['realized_volatility']
                )
            else:
                indicators['vol_adj_momentum'] = 0
            
        except Exception as e:
            self.logger.warning(f"Failed to calculate indicators: {e}")
            return {}
        
        return indicators
    
    def _generate_vix_signal(
        self,
        symbol: str,
        price_data: pd.Series,
        indicators: Dict[str, Any],
        vix_metrics: Dict[str, Any],
        timestamp: datetime
    ) -> Optional[StrategySignal]:
        """Generate VIX-based signal for a symbol.
        
        Args:
            symbol: Trading symbol
            price_data: Price data
            indicators: Technical indicators
            vix_metrics: VIX metrics
            timestamp: Current timestamp
            
        Returns:
            Trading signal or None
        """
        if not indicators:
            return None
        
        # Get regime-specific parameters
        regime_params = self._get_regime_parameters(vix_metrics['current_regime'])
        
        # Base signal from technical analysis
        base_signal = self._calculate_base_signal(indicators)
        
        # VIX adjustments
        vix_adjustment = self._calculate_vix_adjustment(vix_metrics, indicators)
        
        # Combine signals
        adjusted_strength = base_signal * vix_adjustment
        
        # Apply regime-specific threshold
        signal_threshold = regime_params['signal_threshold']
        
        # Determine signal type
        if adjusted_strength > signal_threshold:
            signal_type = "buy"
            signal_strength = min(adjusted_strength, 1.0)
        elif adjusted_strength < -signal_threshold:
            signal_type = "sell"
            signal_strength = min(abs(adjusted_strength), 1.0)
        else:
            signal_type = "hold"
            signal_strength = abs(adjusted_strength)
        
        # Calculate confidence
        confidence = self._calculate_vix_confidence(vix_metrics, indicators, base_signal)
        
        # Create signal metadata
        metadata = {
            'vix_regime': vix_metrics['current_regime'],
            'vix_level': vix_metrics['vix_level'],
            'vix_percentile': vix_metrics['vix_percentile'],
            'base_signal': base_signal,
            'vix_adjustment': vix_adjustment,
            'regime_threshold': signal_threshold,
            'fear_greed_indicator': vix_metrics['fear_greed_indicator'],
            'volatility_breakout': vix_metrics['volatility_breakout']
        }
        
        return StrategySignal(
            symbol=symbol,
            signal_type=signal_type,
            strength=signal_strength,
            confidence=confidence,
            timestamp=timestamp,
            metadata=metadata
        )
    
    def _get_regime_parameters(self, regime: str) -> Dict[str, Any]:
        """Get parameters for specific volatility regime.
        
        Args:
            regime: Volatility regime
            
        Returns:
            Regime parameters
        """
        regime_map = {
            'low': {
                'position_size': self.config.low_vol_position_size,
                'signal_threshold': self.config.low_vol_signal_threshold
            },
            'medium': {
                'position_size': self.config.medium_vol_position_size,
                'signal_threshold': self.config.medium_vol_signal_threshold
            },
            'high': {
                'position_size': self.config.high_vol_position_size,
                'signal_threshold': self.config.high_vol_signal_threshold
            },
            'extreme': {
                'position_size': self.config.extreme_vol_position_size,
                'signal_threshold': self.config.extreme_vol_signal_threshold
            }
        }
        
        return regime_map.get(regime, regime_map['medium'])
    
    def _calculate_base_signal(self, indicators: Dict[str, Any]) -> float:
        """Calculate base technical signal.
        
        Args:
            indicators: Technical indicators
            
        Returns:
            Base signal strength
        """
        signals = []
        
        # Moving average signal
        if 'sma_10' in indicators and 'sma_20' in indicators:
            ma_signal = (indicators['sma_10'] / indicators['sma_20'] - 1) * 10
            signals.append(np.clip(ma_signal, -1, 1))
        
        # Momentum signal
        if 'momentum_5d' in indicators:
            momentum = indicators['momentum_5d']
            momentum_signal = np.clip(momentum * 20, -1, 1)  # Scale momentum
            signals.append(momentum_signal)
        
        # Volatility-adjusted momentum
        if 'vol_adj_momentum' in indicators:
            vol_adj_signal = np.clip(indicators['vol_adj_momentum'], -1, 1)
            signals.append(vol_adj_signal)
        
        # Mean reversion signal (contrarian)
        if 'momentum_10d' in indicators:
            mean_reversion = -indicators['momentum_10d'] * 5  # Contrarian signal
            signals.append(np.clip(mean_reversion, -1, 1))
        
        if not signals:
            return 0.0
        
        # Weight signals (trend following gets more weight in low vol, mean reversion in high vol)
        if len(signals) >= 3:
            weights = [0.4, 0.3, 0.2, 0.1][:len(signals)]
        else:
            weights = [1.0 / len(signals)] * len(signals)
        
        return sum(s * w for s, w in zip(signals, weights))
    
    def _calculate_vix_adjustment(self, vix_metrics: Dict[str, Any], indicators: Dict[str, Any]) -> float:
        """Calculate VIX-based signal adjustment.
        
        Args:
            vix_metrics: VIX metrics
            indicators: Technical indicators
            
        Returns:
            VIX adjustment factor
        """
        adjustment = 1.0
        
        # Fear/Greed adjustment
        fear_greed = vix_metrics['fear_greed_indicator']
        if fear_greed == 1:  # Fear - be more aggressive on dips
            adjustment *= self.config.fear_adjustment_factor
        elif fear_greed == -1:  # Greed - be more conservative
            adjustment *= self.config.greed_adjustment_factor
        
        # Volatility breakout adjustment
        if vix_metrics['volatility_breakout']:
            # Reduce position sizing during volatility spikes
            adjustment *= 0.5
        
        # VIX trend adjustment
        vix_trend = vix_metrics.get('vix_trend', 0)
        if abs(vix_trend) > 0.1:  # Significant VIX trend
            if vix_trend > 0:  # Rising VIX (increasing fear)
                adjustment *= 0.8  # Be more conservative
            else:  # Falling VIX (decreasing fear)
                adjustment *= 1.2  # Be more aggressive
        
        # Regime-based adjustment
        regime = vix_metrics['current_regime']
        regime_adjustments = {
            'low': 1.2,      # More aggressive in low vol
            'medium': 1.0,   # Neutral in medium vol
            'high': 0.8,     # More conservative in high vol
            'extreme': 0.5   # Very conservative in extreme vol
        }
        adjustment *= regime_adjustments.get(regime, 1.0)
        
        return np.clip(adjustment, 0.1, 3.0)
    
    def _calculate_vix_confidence(self, vix_metrics: Dict[str, Any], indicators: Dict[str, Any], base_signal: float) -> float:
        """Calculate VIX-based signal confidence.
        
        Args:
            vix_metrics: VIX metrics
            indicators: Technical indicators
            base_signal: Base signal strength
            
        Returns:
            Signal confidence [0, 1]
        """
        confidence_factors = []
        
        # Base signal strength confidence
        signal_confidence = abs(base_signal)
        confidence_factors.append(signal_confidence)
        
        # VIX regime stability
        if len(self.regime_history) >= 5:
            recent_regimes = self.regime_history[-5:]
            regime_stability = recent_regimes.count(self.current_regime) / len(recent_regimes)
            confidence_factors.append(regime_stability)
        else:
            confidence_factors.append(0.5)
        
        # VIX percentile confidence
        vix_percentile = vix_metrics['vix_percentile']
        if vix_percentile < 20 or vix_percentile > 80:
            percentile_confidence = 0.8  # High confidence at extremes
        else:
            percentile_confidence = 0.4  # Lower confidence in middle
        confidence_factors.append(percentile_confidence)
        
        # Volatility consistency
        realized_vol = indicators.get('realized_volatility', 0)
        vix_level = vix_metrics['vix_level']
        if vix_level > 0:
            vol_consistency = 1 - abs(realized_vol - vix_level) / max(realized_vol, vix_level)
            confidence_factors.append(max(vol_consistency, 0))
        
        return np.mean(confidence_factors)
    
    def calculate_position_size(
        self,
        signal: StrategySignal,
        current_price: float,
        available_cash: float
    ) -> float:
        """Calculate position size based on VIX regime.
        
        Args:
            signal: Trading signal
            current_price: Current price
            available_cash: Available cash
            
        Returns:
            Position size (number of shares)
        """
        # Get regime-specific position size
        regime = signal.metadata.get('vix_regime', 'medium')
        regime_params = self._get_regime_parameters(regime)
        base_position_size = regime_params['position_size']
        
        # Calculate base position
        base_size = base_position_size * self.portfolio_value / current_price
        
        # Signal strength adjustment
        strength_multiplier = signal.strength * signal.confidence
        
        # VIX-specific adjustments
        vix_adjustment = signal.metadata.get('vix_adjustment', 1.0)
        
        # Calculate final position size
        position_size = base_size * strength_multiplier * vix_adjustment
        
        # Apply maximum position size constraint
        max_size = self.config.max_position_size * self.portfolio_value / current_price
        position_size = min(position_size, max_size)
        
        # Check available cash
        max_affordable = available_cash / current_price * 0.95
        position_size = min(position_size, max_affordable)
        
        return max(0, position_size)
    
    def get_vix_metrics(self) -> Dict[str, Any]:
        """Get VIX strategy specific metrics.
        
        Returns:
            VIX metrics dictionary
        """
        base_metrics = self.get_performance_metrics()
        
        # VIX-specific metrics
        vix_metrics = {
            'current_vix_regime': self.current_regime,
            'regime_transitions': len(self.regime_transitions),
            'vix_data_points': len(self.vix_data),
            'avg_vix_level': self.vix_data.mean() if len(self.vix_data) > 0 else 0
        }
        
        # Regime distribution
        if self.regime_history:
            regime_counts = {}
            for regime in ['low', 'medium', 'high', 'extreme']:
                regime_counts[f'{regime}_regime_pct'] = (
                    self.regime_history.count(regime) / len(self.regime_history) * 100
                )
            vix_metrics.update(regime_counts)
        
        # Performance by regime
        if self.trades:
            regime_performance = {}
            for trade in self.trades:
                # This would require storing regime info with each trade
                # For now, we'll skip this detailed analysis
                pass
        
        return {**base_metrics, **vix_metrics}