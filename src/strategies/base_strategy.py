"""Base strategy framework for FinRL trading strategies.

This module provides:
- Abstract base strategy class
- Common strategy configuration
- Signal generation framework
- Position management utilities
- Performance tracking
- Strategy validation
"""

import numpy as np
import pandas as pd
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, asdict
from datetime import datetime
import warnings

from config import settings, TECH_STOCKS
from utils import get_logger, log_performance, log_trading_action


@dataclass
class StrategyConfig:
    """Base configuration for trading strategies."""
    
    # Strategy identification
    name: str = "BaseStrategy"
    version: str = "1.0.0"
    description: str = ""
    
    # Trading parameters
    symbols: List[str] = None
    initial_capital: float = 100000.0
    max_position_size: float = 0.1  # Maximum position size as fraction of portfolio
    min_position_size: float = 0.01  # Minimum position size
    
    # Risk management
    max_drawdown: float = 0.2  # Maximum allowed drawdown
    stop_loss: Optional[float] = None  # Stop loss percentage
    take_profit: Optional[float] = None  # Take profit percentage
    max_positions: int = 10  # Maximum number of concurrent positions
    
    # Signal parameters
    lookback_window: int = 20  # Lookback window for indicators
    signal_threshold: float = 0.5  # Signal strength threshold
    rebalance_frequency: str = "daily"  # daily, weekly, monthly
    
    # Transaction costs
    commission: float = 0.001  # Commission rate
    slippage: float = 0.001  # Slippage rate
    
    # Strategy-specific parameters
    custom_params: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.symbols is None:
            self.symbols = TECH_STOCKS[:10]  # Default to top 10 tech stocks
        if self.custom_params is None:
            self.custom_params = {}


class StrategySignal:
    """Trading signal container."""
    
    def __init__(
        self,
        symbol: str,
        signal_type: str,  # 'buy', 'sell', 'hold'
        strength: float,  # Signal strength [0, 1]
        confidence: float,  # Signal confidence [0, 1]
        timestamp: Optional[datetime] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """Initialize trading signal.
        
        Args:
            symbol: Trading symbol
            signal_type: Type of signal
            strength: Signal strength
            confidence: Signal confidence
            timestamp: Signal timestamp
            metadata: Additional signal metadata
        """
        self.symbol = symbol
        self.signal_type = signal_type
        self.strength = strength
        self.confidence = confidence
        self.timestamp = timestamp or datetime.now()
        self.metadata = metadata or {}
    
    def __repr__(self) -> str:
        return (
            f"StrategySignal(symbol={self.symbol}, type={self.signal_type}, "
            f"strength={self.strength:.3f}, confidence={self.confidence:.3f})"
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert signal to dictionary."""
        return {
            'symbol': self.symbol,
            'signal_type': self.signal_type,
            'strength': self.strength,
            'confidence': self.confidence,
            'timestamp': self.timestamp.isoformat(),
            'metadata': self.metadata
        }


class Position:
    """Trading position container."""
    
    def __init__(
        self,
        symbol: str,
        quantity: float,
        entry_price: float,
        entry_time: datetime,
        position_type: str = "long"  # 'long' or 'short'
    ):
        """Initialize trading position.
        
        Args:
            symbol: Trading symbol
            quantity: Position quantity
            entry_price: Entry price
            entry_time: Entry timestamp
            position_type: Position type
        """
        self.symbol = symbol
        self.quantity = quantity
        self.entry_price = entry_price
        self.entry_time = entry_time
        self.position_type = position_type
        
        # Position tracking
        self.current_price = entry_price
        self.unrealized_pnl = 0.0
        self.realized_pnl = 0.0
        self.is_open = True
        
        # Risk management
        self.stop_loss_price = None
        self.take_profit_price = None
    
    def update_price(self, current_price: float) -> None:
        """Update current price and unrealized P&L.
        
        Args:
            current_price: Current market price
        """
        self.current_price = current_price
        
        if self.position_type == "long":
            self.unrealized_pnl = (current_price - self.entry_price) * self.quantity
        else:  # short
            self.unrealized_pnl = (self.entry_price - current_price) * self.quantity
    
    def get_market_value(self) -> float:
        """Get current market value of position."""
        return abs(self.quantity) * self.current_price
    
    def get_return(self) -> float:
        """Get position return percentage."""
        if self.position_type == "long":
            return (self.current_price - self.entry_price) / self.entry_price
        else:
            return (self.entry_price - self.current_price) / self.entry_price
    
    def close_position(self, exit_price: float, exit_time: datetime) -> float:
        """Close the position and calculate realized P&L.
        
        Args:
            exit_price: Exit price
            exit_time: Exit timestamp
            
        Returns:
            Realized P&L
        """
        if self.position_type == "long":
            self.realized_pnl = (exit_price - self.entry_price) * self.quantity
        else:
            self.realized_pnl = (self.entry_price - exit_price) * self.quantity
        
        self.is_open = False
        return self.realized_pnl
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert position to dictionary."""
        return {
            'symbol': self.symbol,
            'quantity': self.quantity,
            'entry_price': self.entry_price,
            'entry_time': self.entry_time.isoformat(),
            'position_type': self.position_type,
            'current_price': self.current_price,
            'unrealized_pnl': self.unrealized_pnl,
            'realized_pnl': self.realized_pnl,
            'is_open': self.is_open,
            'market_value': self.get_market_value(),
            'return_pct': self.get_return()
        }


class BaseStrategy(ABC):
    """Abstract base class for trading strategies."""
    
    def __init__(self, config: StrategyConfig):
        """Initialize base strategy.
        
        Args:
            config: Strategy configuration
        """
        self.config = config
        self.logger = get_logger(f"{__name__}.{config.name}")
        
        # Portfolio state
        self.cash = config.initial_capital
        self.positions: Dict[str, Position] = {}
        self.portfolio_value = config.initial_capital
        
        # Performance tracking
        self.trades = []
        self.portfolio_history = []
        self.signals_history = []
        
        # Strategy state
        self.is_initialized = False
        self.current_data = None
        
        self.logger.info(f"Strategy initialized: {config.name} v{config.version}")
    
    @abstractmethod
    def generate_signals(
        self,
        data: pd.DataFrame,
        timestamp: datetime
    ) -> List[StrategySignal]:
        """Generate trading signals based on market data.
        
        Args:
            data: Market data DataFrame
            timestamp: Current timestamp
            
        Returns:
            List of trading signals
        """
        pass
    
    @abstractmethod
    def calculate_position_size(
        self,
        signal: StrategySignal,
        current_price: float,
        available_cash: float
    ) -> float:
        """Calculate position size for a signal.
        
        Args:
            signal: Trading signal
            current_price: Current price
            available_cash: Available cash
            
        Returns:
            Position size (number of shares)
        """
        pass
    
    def initialize(self, data: pd.DataFrame) -> None:
        """Initialize strategy with historical data.
        
        Args:
            data: Historical market data
        """
        self.current_data = data
        self.is_initialized = True
        self.logger.info("Strategy initialized with historical data")
    
    def update_data(self, data: pd.DataFrame) -> None:
        """Update strategy with new market data.
        
        Args:
            data: Updated market data
        """
        self.current_data = data
    
    def process_signals(
        self,
        signals: List[StrategySignal],
        current_prices: Dict[str, float],
        timestamp: datetime
    ) -> List[Dict[str, Any]]:
        """Process trading signals and generate orders.
        
        Args:
            signals: List of trading signals
            current_prices: Current market prices
            timestamp: Current timestamp
            
        Returns:
            List of order dictionaries
        """
        orders = []
        
        for signal in signals:
            if signal.symbol not in current_prices:
                continue
            
            current_price = current_prices[signal.symbol]
            
            # Filter signals by strength threshold
            if signal.strength < self.config.signal_threshold:
                continue
            
            # Generate order based on signal
            order = self._signal_to_order(signal, current_price, timestamp)
            if order:
                orders.append(order)
                
                # Log trading action
                log_trading_action(
                    signal.signal_type,
                    signal.symbol,
                    order.get('quantity', 0),
                    current_price
                )
        
        return orders
    
    def _signal_to_order(
        self,
        signal: StrategySignal,
        current_price: float,
        timestamp: datetime
    ) -> Optional[Dict[str, Any]]:
        """Convert signal to order.
        
        Args:
            signal: Trading signal
            current_price: Current price
            timestamp: Current timestamp
            
        Returns:
            Order dictionary or None
        """
        if signal.signal_type == "buy":
            return self._create_buy_order(signal, current_price, timestamp)
        elif signal.signal_type == "sell":
            return self._create_sell_order(signal, current_price, timestamp)
        else:  # hold
            return None
    
    def _create_buy_order(
        self,
        signal: StrategySignal,
        current_price: float,
        timestamp: datetime
    ) -> Optional[Dict[str, Any]]:
        """Create buy order.
        
        Args:
            signal: Trading signal
            current_price: Current price
            timestamp: Current timestamp
            
        Returns:
            Buy order dictionary
        """
        # Check if we already have a position
        if signal.symbol in self.positions and self.positions[signal.symbol].is_open:
            return None
        
        # Check maximum positions limit
        open_positions = sum(1 for pos in self.positions.values() if pos.is_open)
        if open_positions >= self.config.max_positions:
            return None
        
        # Calculate position size
        quantity = self.calculate_position_size(signal, current_price, self.cash)
        
        if quantity <= 0:
            return None
        
        # Apply minimum position size
        min_value = self.config.min_position_size * self.portfolio_value
        if quantity * current_price < min_value:
            return None
        
        return {
            'action': 'buy',
            'symbol': signal.symbol,
            'quantity': quantity,
            'price': current_price,
            'timestamp': timestamp,
            'signal_strength': signal.strength,
            'signal_confidence': signal.confidence
        }
    
    def _create_sell_order(
        self,
        signal: StrategySignal,
        current_price: float,
        timestamp: datetime
    ) -> Optional[Dict[str, Any]]:
        """Create sell order.
        
        Args:
            signal: Trading signal
            current_price: Current price
            timestamp: Current timestamp
            
        Returns:
            Sell order dictionary
        """
        # Check if we have a position to sell
        if signal.symbol not in self.positions or not self.positions[signal.symbol].is_open:
            return None
        
        position = self.positions[signal.symbol]
        
        return {
            'action': 'sell',
            'symbol': signal.symbol,
            'quantity': position.quantity,
            'price': current_price,
            'timestamp': timestamp,
            'signal_strength': signal.strength,
            'signal_confidence': signal.confidence
        }
    
    def execute_order(self, order: Dict[str, Any]) -> bool:
        """Execute trading order.
        
        Args:
            order: Order dictionary
            
        Returns:
            True if order executed successfully
        """
        try:
            symbol = order['symbol']
            quantity = order['quantity']
            price = order['price']
            timestamp = order['timestamp']
            action = order['action']
            
            # Apply transaction costs
            commission_cost = quantity * price * self.config.commission
            slippage_cost = quantity * price * self.config.slippage
            total_cost = commission_cost + slippage_cost
            
            if action == 'buy':
                # Check if we have enough cash
                total_required = quantity * price + total_cost
                if total_required > self.cash:
                    return False
                
                # Create position
                position = Position(
                    symbol=symbol,
                    quantity=quantity,
                    entry_price=price,
                    entry_time=timestamp
                )
                
                self.positions[symbol] = position
                self.cash -= total_required
                
            elif action == 'sell':
                # Close position
                if symbol in self.positions and self.positions[symbol].is_open:
                    position = self.positions[symbol]
                    realized_pnl = position.close_position(price, timestamp)
                    
                    # Update cash
                    proceeds = quantity * price - total_cost
                    self.cash += proceeds
                    
                    # Record trade
                    trade = {
                        'symbol': symbol,
                        'entry_time': position.entry_time,
                        'exit_time': timestamp,
                        'entry_price': position.entry_price,
                        'exit_price': price,
                        'quantity': quantity,
                        'realized_pnl': realized_pnl,
                        'return_pct': position.get_return(),
                        'commission': commission_cost,
                        'slippage': slippage_cost
                    }
                    self.trades.append(trade)
            
            # Update portfolio value
            self._update_portfolio_value()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to execute order: {e}")
            return False
    
    def _update_portfolio_value(self) -> None:
        """Update total portfolio value."""
        positions_value = sum(
            pos.get_market_value() for pos in self.positions.values() if pos.is_open
        )
        self.portfolio_value = self.cash + positions_value
    
    def update_positions(self, current_prices: Dict[str, float]) -> None:
        """Update all positions with current prices.
        
        Args:
            current_prices: Current market prices
        """
        for symbol, position in self.positions.items():
            if position.is_open and symbol in current_prices:
                position.update_price(current_prices[symbol])
        
        self._update_portfolio_value()
    
    def check_risk_management(
        self,
        current_prices: Dict[str, float],
        timestamp: datetime
    ) -> List[Dict[str, Any]]:
        """Check risk management rules and generate exit orders.
        
        Args:
            current_prices: Current market prices
            timestamp: Current timestamp
            
        Returns:
            List of exit orders
        """
        exit_orders = []
        
        # Check portfolio drawdown
        current_drawdown = 1 - (self.portfolio_value / self.config.initial_capital)
        if current_drawdown > self.config.max_drawdown:
            # Close all positions
            for symbol, position in self.positions.items():
                if position.is_open and symbol in current_prices:
                    exit_orders.append({
                        'action': 'sell',
                        'symbol': symbol,
                        'quantity': position.quantity,
                        'price': current_prices[symbol],
                        'timestamp': timestamp,
                        'reason': 'max_drawdown'
                    })
        
        # Check individual position stop losses and take profits
        for symbol, position in self.positions.items():
            if not position.is_open or symbol not in current_prices:
                continue
            
            current_price = current_prices[symbol]
            return_pct = position.get_return()
            
            # Stop loss check
            if self.config.stop_loss and return_pct <= -self.config.stop_loss:
                exit_orders.append({
                    'action': 'sell',
                    'symbol': symbol,
                    'quantity': position.quantity,
                    'price': current_price,
                    'timestamp': timestamp,
                    'reason': 'stop_loss'
                })
            
            # Take profit check
            if self.config.take_profit and return_pct >= self.config.take_profit:
                exit_orders.append({
                    'action': 'sell',
                    'symbol': symbol,
                    'quantity': position.quantity,
                    'price': current_price,
                    'timestamp': timestamp,
                    'reason': 'take_profit'
                })
        
        return exit_orders
    
    def get_portfolio_summary(self) -> Dict[str, Any]:
        """Get current portfolio summary.
        
        Returns:
            Portfolio summary dictionary
        """
        open_positions = [pos for pos in self.positions.values() if pos.is_open]
        
        total_unrealized_pnl = sum(pos.unrealized_pnl for pos in open_positions)
        total_realized_pnl = sum(trade['realized_pnl'] for trade in self.trades)
        
        return {
            'cash': self.cash,
            'portfolio_value': self.portfolio_value,
            'initial_capital': self.config.initial_capital,
            'total_return': (self.portfolio_value - self.config.initial_capital) / self.config.initial_capital,
            'unrealized_pnl': total_unrealized_pnl,
            'realized_pnl': total_realized_pnl,
            'open_positions': len(open_positions),
            'total_trades': len(self.trades),
            'positions': [pos.to_dict() for pos in open_positions]
        }
    
    def get_performance_metrics(self) -> Dict[str, float]:
        """Calculate strategy performance metrics.
        
        Returns:
            Performance metrics dictionary
        """
        if not self.trades:
            return {}
        
        returns = [trade['return_pct'] for trade in self.trades]
        
        # Basic metrics
        total_return = (self.portfolio_value - self.config.initial_capital) / self.config.initial_capital
        win_rate = sum(1 for r in returns if r > 0) / len(returns)
        avg_return = np.mean(returns)
        
        # Risk metrics
        volatility = np.std(returns) if len(returns) > 1 else 0
        sharpe_ratio = avg_return / volatility if volatility > 0 else 0
        
        # Drawdown calculation
        portfolio_values = [self.config.initial_capital]
        for trade in self.trades:
            portfolio_values.append(portfolio_values[-1] + trade['realized_pnl'])
        
        peak = portfolio_values[0]
        max_drawdown = 0
        for value in portfolio_values:
            if value > peak:
                peak = value
            drawdown = (peak - value) / peak
            max_drawdown = max(max_drawdown, drawdown)
        
        return {
            'total_return': total_return,
            'win_rate': win_rate,
            'avg_return': avg_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'total_trades': len(self.trades)
        }
    
    def reset(self) -> None:
        """Reset strategy to initial state."""
        self.cash = self.config.initial_capital
        self.positions = {}
        self.portfolio_value = self.config.initial_capital
        self.trades = []
        self.portfolio_history = []
        self.signals_history = []
        self.is_initialized = False
        
        self.logger.info("Strategy reset to initial state")
    
    def save_state(self, filepath: str) -> None:
        """Save strategy state to file.
        
        Args:
            filepath: Path to save state
        """
        state = {
            'config': asdict(self.config),
            'cash': self.cash,
            'portfolio_value': self.portfolio_value,
            'positions': {k: v.to_dict() for k, v in self.positions.items()},
            'trades': self.trades,
            'portfolio_history': self.portfolio_history,
            'signals_history': [s.to_dict() for s in self.signals_history]
        }
        
        import json
        with open(filepath, 'w') as f:
            json.dump(state, f, indent=2, default=str)
        
        self.logger.info(f"Strategy state saved to {filepath}")
    
    def load_state(self, filepath: str) -> None:
        """Load strategy state from file.
        
        Args:
            filepath: Path to load state from
        """
        import json
        
        with open(filepath, 'r') as f:
            state = json.load(f)
        
        self.cash = state['cash']
        self.portfolio_value = state['portfolio_value']
        self.trades = state['trades']
        self.portfolio_history = state['portfolio_history']
        
        # Reconstruct positions
        self.positions = {}
        for symbol, pos_data in state['positions'].items():
            position = Position(
                symbol=pos_data['symbol'],
                quantity=pos_data['quantity'],
                entry_price=pos_data['entry_price'],
                entry_time=datetime.fromisoformat(pos_data['entry_time']),
                position_type=pos_data['position_type']
            )
            position.current_price = pos_data['current_price']
            position.unrealized_pnl = pos_data['unrealized_pnl']
            position.realized_pnl = pos_data['realized_pnl']
            position.is_open = pos_data['is_open']
            self.positions[symbol] = position
        
        self.logger.info(f"Strategy state loaded from {filepath}")