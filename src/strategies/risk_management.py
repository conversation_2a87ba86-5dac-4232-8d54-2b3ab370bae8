"""Risk management strategy for FinRL trading.

This module implements:
- Portfolio risk monitoring and control
- Dynamic stop-loss and take-profit management
- Position sizing based on risk metrics
- Correlation-based risk assessment
- Drawdown protection mechanisms
- Value at Risk (VaR) calculations
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass
from datetime import datetime, timedelta
import warnings
from scipy import stats

from .base_strategy import BaseStrategy, StrategyConfig, StrategySignal, Position
from config import settings, RISK_LIMITS
from utils import get_logger, log_performance, log_error


@dataclass
class RiskConfig(StrategyConfig):
    """Configuration for risk management strategy."""
    
    # Portfolio risk limits
    max_portfolio_risk: float = 0.02  # Maximum daily portfolio risk (2%)
    max_position_risk: float = 0.005  # Maximum single position risk (0.5%)
    max_sector_concentration: float = 0.3  # Maximum sector concentration (30%)
    max_correlation_exposure: float = 0.5  # Maximum correlated exposure
    
    # VaR parameters
    var_confidence_level: float = 0.95  # VaR confidence level
    var_lookback_period: int = 252  # VaR calculation period
    expected_shortfall_level: float = 0.99  # Expected Shortfall level
    
    # Dynamic stop-loss parameters
    base_stop_loss: float = 0.02  # Base stop-loss (2%)
    volatility_multiplier: float = 2.0  # Volatility-based stop adjustment
    trailing_stop_activation: float = 0.05  # Trailing stop activation (5% profit)
    trailing_stop_distance: float = 0.03  # Trailing stop distance (3%)
    
    # Position sizing parameters
    kelly_fraction: float = 0.25  # Kelly criterion fraction
    max_kelly_position: float = 0.1  # Maximum Kelly position size
    volatility_target: float = 0.15  # Target portfolio volatility
    
    # Correlation parameters
    correlation_lookback: int = 60  # Correlation calculation period
    correlation_threshold: float = 0.7  # High correlation threshold
    
    # Drawdown protection
    max_daily_drawdown: float = 0.03  # Maximum daily drawdown (3%)
    max_peak_drawdown: float = 0.15  # Maximum peak drawdown (15%)
    drawdown_reduction_factor: float = 0.5  # Position reduction during drawdown
    
    # Liquidity risk
    min_avg_volume: float = 1000000  # Minimum average daily volume
    max_position_vs_volume: float = 0.1  # Max position as % of avg volume
    
    def __post_init__(self):
        super().__post_init__()
        self.name = "RiskManagementStrategy"
        self.description = "Comprehensive risk management and portfolio protection"


class RiskMetrics:
    """Container for risk metrics calculations."""
    
    def __init__(self):
        self.portfolio_var = 0.0
        self.portfolio_expected_shortfall = 0.0
        self.portfolio_volatility = 0.0
        self.portfolio_beta = 0.0
        self.max_drawdown = 0.0
        self.current_drawdown = 0.0
        self.sharpe_ratio = 0.0
        self.sortino_ratio = 0.0
        self.correlation_matrix = pd.DataFrame()
        self.position_risks = {}
        self.sector_exposures = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert risk metrics to dictionary."""
        return {
            'portfolio_var': self.portfolio_var,
            'portfolio_expected_shortfall': self.portfolio_expected_shortfall,
            'portfolio_volatility': self.portfolio_volatility,
            'portfolio_beta': self.portfolio_beta,
            'max_drawdown': self.max_drawdown,
            'current_drawdown': self.current_drawdown,
            'sharpe_ratio': self.sharpe_ratio,
            'sortino_ratio': self.sortino_ratio,
            'position_risks': self.position_risks,
            'sector_exposures': self.sector_exposures
        }


class RiskManagementStrategy(BaseStrategy):
    """Risk management strategy implementation."""
    
    def __init__(self, config: RiskConfig):
        """Initialize risk management strategy.
        
        Args:
            config: Risk management configuration
        """
        super().__init__(config)
        self.config: RiskConfig = config
        
        # Risk tracking
        self.risk_metrics = RiskMetrics()
        self.returns_history = pd.Series(dtype=float)
        self.portfolio_values_history = []
        self.peak_portfolio_value = config.initial_capital
        
        # Position tracking
        self.position_entry_dates = {}
        self.trailing_stops = {}
        
        # Market data for risk calculations
        self.price_data = pd.DataFrame()
        self.volume_data = pd.DataFrame()
        
        self.logger.info("Risk management strategy initialized")
    
    def update_market_data(self, price_data: pd.DataFrame, volume_data: Optional[pd.DataFrame] = None) -> None:
        """Update market data for risk calculations.
        
        Args:
            price_data: Price data DataFrame
            volume_data: Volume data DataFrame (optional)
        """
        self.price_data = price_data
        if volume_data is not None:
            self.volume_data = volume_data
        
        # Update risk metrics
        self._calculate_risk_metrics()
    
    def generate_signals(
        self,
        data: pd.DataFrame,
        timestamp: datetime
    ) -> List[StrategySignal]:
        """Generate risk-adjusted trading signals.
        
        Args:
            data: Market data DataFrame
            timestamp: Current timestamp
            
        Returns:
            List of trading signals (mostly risk management signals)
        """
        signals = []
        
        # Update portfolio value and risk metrics
        self._update_portfolio_tracking(timestamp)
        
        # Check for risk limit breaches
        risk_signals = self._check_risk_limits(data, timestamp)
        signals.extend(risk_signals)
        
        # Generate position sizing signals
        sizing_signals = self._generate_position_sizing_signals(data, timestamp)
        signals.extend(sizing_signals)
        
        # Check stop-loss and take-profit levels
        stop_signals = self._check_stop_levels(data, timestamp)
        signals.extend(stop_signals)
        
        # Store signals for history
        self.signals_history.extend(signals)
        
        return signals
    
    def _update_portfolio_tracking(self, timestamp: datetime) -> None:
        """Update portfolio value tracking and drawdown calculations.
        
        Args:
            timestamp: Current timestamp
        """
        # Update portfolio value history
        self.portfolio_values_history.append({
            'timestamp': timestamp,
            'portfolio_value': self.portfolio_value,
            'cash': self.cash
        })
        
        # Update peak portfolio value
        if self.portfolio_value > self.peak_portfolio_value:
            self.peak_portfolio_value = self.portfolio_value
        
        # Calculate current drawdown
        self.risk_metrics.current_drawdown = (
            (self.peak_portfolio_value - self.portfolio_value) / self.peak_portfolio_value
        )
        
        # Update returns history
        if len(self.portfolio_values_history) > 1:
            prev_value = self.portfolio_values_history[-2]['portfolio_value']
            current_return = (self.portfolio_value - prev_value) / prev_value
            self.returns_history = pd.concat([
                self.returns_history,
                pd.Series([current_return], index=[timestamp])
            ])
        
        # Keep history manageable
        if len(self.portfolio_values_history) > 1000:
            self.portfolio_values_history = self.portfolio_values_history[-500:]
        
        if len(self.returns_history) > 1000:
            self.returns_history = self.returns_history.tail(500)
    
    def _calculate_risk_metrics(self) -> None:
        """Calculate comprehensive risk metrics."""
        try:
            # Portfolio volatility
            if len(self.returns_history) >= 20:
                self.risk_metrics.portfolio_volatility = (
                    self.returns_history.rolling(20).std().iloc[-1] * np.sqrt(252)
                )
            
            # VaR calculation
            if len(self.returns_history) >= self.config.var_lookback_period:
                returns_sample = self.returns_history.tail(self.config.var_lookback_period)
                self.risk_metrics.portfolio_var = np.percentile(
                    returns_sample, (1 - self.config.var_confidence_level) * 100
                )
                
                # Expected Shortfall (Conditional VaR)
                var_threshold = np.percentile(
                    returns_sample, (1 - self.config.expected_shortfall_level) * 100
                )
                tail_returns = returns_sample[returns_sample <= var_threshold]
                if len(tail_returns) > 0:
                    self.risk_metrics.portfolio_expected_shortfall = tail_returns.mean()
            
            # Sharpe ratio
            if len(self.returns_history) >= 20 and self.risk_metrics.portfolio_volatility > 0:
                avg_return = self.returns_history.tail(252).mean() * 252  # Annualized
                self.risk_metrics.sharpe_ratio = avg_return / self.risk_metrics.portfolio_volatility
            
            # Sortino ratio
            if len(self.returns_history) >= 20:
                negative_returns = self.returns_history[self.returns_history < 0]
                if len(negative_returns) > 0:
                    downside_deviation = negative_returns.std() * np.sqrt(252)
                    if downside_deviation > 0:
                        avg_return = self.returns_history.tail(252).mean() * 252
                        self.risk_metrics.sortino_ratio = avg_return / downside_deviation
            
            # Maximum drawdown
            if self.portfolio_values_history:
                values = [pv['portfolio_value'] for pv in self.portfolio_values_history]
                peak = values[0]
                max_dd = 0
                for value in values:
                    if value > peak:
                        peak = value
                    drawdown = (peak - value) / peak
                    max_dd = max(max_dd, drawdown)
                self.risk_metrics.max_drawdown = max_dd
            
            # Position-level risks
            self._calculate_position_risks()
            
            # Correlation analysis
            self._calculate_correlations()
            
        except Exception as e:
            self.logger.error(f"Failed to calculate risk metrics: {e}")
    
    def _calculate_position_risks(self) -> None:
        """Calculate risk metrics for individual positions."""
        self.risk_metrics.position_risks = {}
        
        for symbol, position in self.positions.items():
            if not position.is_open:
                continue
            
            try:
                # Position value as % of portfolio
                position_weight = position.get_market_value() / self.portfolio_value
                
                # Position volatility (if price data available)
                position_vol = 0
                if symbol in self.price_data.columns:
                    symbol_data = self.price_data[symbol].dropna()
                    if len(symbol_data) >= 20:
                        returns = symbol_data.pct_change().dropna()
                        position_vol = returns.rolling(20).std().iloc[-1] * np.sqrt(252)
                
                # Position VaR
                position_var = position_weight * self.risk_metrics.portfolio_var
                
                # Days held
                entry_date = self.position_entry_dates.get(symbol, datetime.now())
                days_held = (datetime.now() - entry_date).days
                
                self.risk_metrics.position_risks[symbol] = {
                    'weight': position_weight,
                    'volatility': position_vol,
                    'var': position_var,
                    'unrealized_pnl_pct': position.get_return(),
                    'days_held': days_held,
                    'market_value': position.get_market_value()
                }
                
            except Exception as e:
                self.logger.warning(f"Failed to calculate risk for {symbol}: {e}")
    
    def _calculate_correlations(self) -> None:
        """Calculate correlation matrix for portfolio positions."""
        try:
            if len(self.price_data.columns) < 2:
                return
            
            # Get returns for correlation calculation
            returns_data = self.price_data.pct_change().dropna()
            
            if len(returns_data) >= self.config.correlation_lookback:
                recent_returns = returns_data.tail(self.config.correlation_lookback)
                self.risk_metrics.correlation_matrix = recent_returns.corr()
            
        except Exception as e:
            self.logger.warning(f"Failed to calculate correlations: {e}")
    
    def _check_risk_limits(self, data: pd.DataFrame, timestamp: datetime) -> List[StrategySignal]:
        """Check for risk limit breaches and generate protective signals.
        
        Args:
            data: Market data DataFrame
            timestamp: Current timestamp
            
        Returns:
            List of risk management signals
        """
        signals = []
        
        # Check portfolio drawdown limits
        if self.risk_metrics.current_drawdown > self.config.max_daily_drawdown:
            # Generate sell signals for all positions
            for symbol, position in self.positions.items():
                if position.is_open:
                    signals.append(StrategySignal(
                        symbol=symbol,
                        signal_type="sell",
                        strength=1.0,
                        confidence=1.0,
                        timestamp=timestamp,
                        metadata={
                            'reason': 'daily_drawdown_limit',
                            'current_drawdown': self.risk_metrics.current_drawdown,
                            'limit': self.config.max_daily_drawdown
                        }
                    ))
        
        # Check maximum drawdown limit
        if self.risk_metrics.current_drawdown > self.config.max_peak_drawdown:
            # Emergency liquidation
            for symbol, position in self.positions.items():
                if position.is_open:
                    signals.append(StrategySignal(
                        symbol=symbol,
                        signal_type="sell",
                        strength=1.0,
                        confidence=1.0,
                        timestamp=timestamp,
                        metadata={
                            'reason': 'max_drawdown_breach',
                            'current_drawdown': self.risk_metrics.current_drawdown,
                            'limit': self.config.max_peak_drawdown
                        }
                    ))
        
        # Check position concentration limits
        for symbol, risk_data in self.risk_metrics.position_risks.items():
            if risk_data['weight'] > self.config.max_position_risk:
                # Reduce position size
                signals.append(StrategySignal(
                    symbol=symbol,
                    signal_type="sell",
                    strength=0.5,  # Partial reduction
                    confidence=0.8,
                    timestamp=timestamp,
                    metadata={
                        'reason': 'position_concentration',
                        'current_weight': risk_data['weight'],
                        'limit': self.config.max_position_risk
                    }
                ))
        
        # Check correlation limits
        if not self.risk_metrics.correlation_matrix.empty:
            high_corr_pairs = self._find_high_correlation_pairs()
            for symbol1, symbol2, correlation in high_corr_pairs:
                if symbol1 in self.positions and symbol2 in self.positions:
                    # Reduce position in the smaller position
                    pos1_value = self.positions[symbol1].get_market_value()
                    pos2_value = self.positions[symbol2].get_market_value()
                    
                    reduce_symbol = symbol1 if pos1_value < pos2_value else symbol2
                    
                    signals.append(StrategySignal(
                        symbol=reduce_symbol,
                        signal_type="sell",
                        strength=0.3,
                        confidence=0.6,
                        timestamp=timestamp,
                        metadata={
                            'reason': 'high_correlation',
                            'correlated_with': symbol2 if reduce_symbol == symbol1 else symbol1,
                            'correlation': correlation
                        }
                    ))
        
        return signals
    
    def _find_high_correlation_pairs(self) -> List[Tuple[str, str, float]]:
        """Find pairs of assets with high correlation.
        
        Returns:
            List of (symbol1, symbol2, correlation) tuples
        """
        high_corr_pairs = []
        
        if self.risk_metrics.correlation_matrix.empty:
            return high_corr_pairs
        
        corr_matrix = self.risk_metrics.correlation_matrix
        
        for i in range(len(corr_matrix.columns)):
            for j in range(i + 1, len(corr_matrix.columns)):
                symbol1 = corr_matrix.columns[i]
                symbol2 = corr_matrix.columns[j]
                correlation = corr_matrix.iloc[i, j]
                
                if abs(correlation) > self.config.correlation_threshold:
                    high_corr_pairs.append((symbol1, symbol2, correlation))
        
        return high_corr_pairs
    
    def _generate_position_sizing_signals(self, data: pd.DataFrame, timestamp: datetime) -> List[StrategySignal]:
        """Generate position sizing adjustment signals.
        
        Args:
            data: Market data DataFrame
            timestamp: Current timestamp
            
        Returns:
            List of position sizing signals
        """
        signals = []
        
        # Check if portfolio volatility exceeds target
        if self.risk_metrics.portfolio_volatility > self.config.volatility_target:
            # Reduce all position sizes
            reduction_factor = self.config.volatility_target / self.risk_metrics.portfolio_volatility
            
            for symbol, position in self.positions.items():
                if position.is_open:
                    signals.append(StrategySignal(
                        symbol=symbol,
                        signal_type="sell",
                        strength=1 - reduction_factor,
                        confidence=0.7,
                        timestamp=timestamp,
                        metadata={
                            'reason': 'volatility_target_breach',
                            'current_vol': self.risk_metrics.portfolio_volatility,
                            'target_vol': self.config.volatility_target,
                            'reduction_factor': reduction_factor
                        }
                    ))
        
        return signals
    
    def _check_stop_levels(self, data: pd.DataFrame, timestamp: datetime) -> List[StrategySignal]:
        """Check stop-loss and take-profit levels.
        
        Args:
            data: Market data DataFrame
            timestamp: Current timestamp
            
        Returns:
            List of stop-level signals
        """
        signals = []
        
        for symbol, position in self.positions.items():
            if not position.is_open:
                continue
            
            try:
                current_return = position.get_return()
                
                # Calculate dynamic stop-loss based on volatility
                symbol_vol = 0
                if symbol in self.price_data.columns:
                    symbol_data = self.price_data[symbol].dropna()
                    if len(symbol_data) >= 20:
                        returns = symbol_data.pct_change().dropna()
                        symbol_vol = returns.rolling(20).std().iloc[-1]
                
                dynamic_stop = self.config.base_stop_loss + (symbol_vol * self.config.volatility_multiplier)
                
                # Check stop-loss
                if current_return <= -dynamic_stop:
                    signals.append(StrategySignal(
                        symbol=symbol,
                        signal_type="sell",
                        strength=1.0,
                        confidence=1.0,
                        timestamp=timestamp,
                        metadata={
                            'reason': 'stop_loss',
                            'current_return': current_return,
                            'stop_level': -dynamic_stop
                        }
                    ))
                
                # Check trailing stop
                elif current_return >= self.config.trailing_stop_activation:
                    # Activate or update trailing stop
                    trailing_stop_level = current_return - self.config.trailing_stop_distance
                    
                    if symbol not in self.trailing_stops:
                        self.trailing_stops[symbol] = trailing_stop_level
                    else:
                        # Update trailing stop if profit increased
                        self.trailing_stops[symbol] = max(self.trailing_stops[symbol], trailing_stop_level)
                    
                    # Check if trailing stop is hit
                    if current_return <= self.trailing_stops[symbol]:
                        signals.append(StrategySignal(
                            symbol=symbol,
                            signal_type="sell",
                            strength=1.0,
                            confidence=0.9,
                            timestamp=timestamp,
                            metadata={
                                'reason': 'trailing_stop',
                                'current_return': current_return,
                                'trailing_stop_level': self.trailing_stops[symbol]
                            }
                        ))
                        
                        # Remove trailing stop after execution
                        del self.trailing_stops[symbol]
                
            except Exception as e:
                self.logger.warning(f"Failed to check stop levels for {symbol}: {e}")
        
        return signals
    
    def calculate_position_size(
        self,
        signal: StrategySignal,
        current_price: float,
        available_cash: float
    ) -> float:
        """Calculate risk-adjusted position size.
        
        Args:
            signal: Trading signal
            current_price: Current price
            available_cash: Available cash
            
        Returns:
            Position size (number of shares)
        """
        # Base position size using Kelly criterion (simplified)
        win_rate = 0.55  # Assume 55% win rate (should be calculated from historical data)
        avg_win = 0.05   # Assume 5% average win
        avg_loss = 0.03  # Assume 3% average loss
        
        if avg_loss > 0:
            kelly_fraction = (win_rate * avg_win - (1 - win_rate) * avg_loss) / avg_win
            kelly_fraction = min(kelly_fraction, self.config.kelly_fraction)
        else:
            kelly_fraction = self.config.kelly_fraction
        
        # Risk-based position sizing
        portfolio_risk_budget = self.config.max_portfolio_risk * self.portfolio_value
        position_risk_budget = self.config.max_position_risk * self.portfolio_value
        
        # Use the more conservative of Kelly and risk-based sizing
        kelly_size = kelly_fraction * self.portfolio_value / current_price
        risk_size = position_risk_budget / (current_price * self.config.base_stop_loss)
        
        base_size = min(kelly_size, risk_size)
        
        # Adjust for signal strength and confidence
        adjusted_size = base_size * signal.strength * signal.confidence
        
        # Apply maximum position size constraint
        max_size = self.config.max_kelly_position * self.portfolio_value / current_price
        adjusted_size = min(adjusted_size, max_size)
        
        # Check available cash
        max_affordable = available_cash / current_price * 0.95
        adjusted_size = min(adjusted_size, max_affordable)
        
        # Adjust for current portfolio risk
        if self.risk_metrics.current_drawdown > self.config.max_daily_drawdown * 0.5:
            adjusted_size *= self.config.drawdown_reduction_factor
        
        return max(0, adjusted_size)
    
    def execute_order(self, order: Dict[str, Any]) -> bool:
        """Execute order with risk management tracking.
        
        Args:
            order: Order dictionary
            
        Returns:
            True if order executed successfully
        """
        success = super().execute_order(order)
        
        if success and order['action'] == 'buy':
            # Track position entry date for risk calculations
            symbol = order['symbol']
            self.position_entry_dates[symbol] = order['timestamp']
        
        return success
    
    def get_risk_report(self) -> Dict[str, Any]:
        """Generate comprehensive risk report.
        
        Returns:
            Risk report dictionary
        """
        base_metrics = self.get_performance_metrics()
        risk_data = self.risk_metrics.to_dict()
        
        # Additional risk calculations
        risk_report = {
            **base_metrics,
            **risk_data,
            'risk_limits': {
                'max_portfolio_risk': self.config.max_portfolio_risk,
                'max_position_risk': self.config.max_position_risk,
                'max_drawdown': self.config.max_peak_drawdown,
                'volatility_target': self.config.volatility_target
            },
            'current_exposures': {
                'total_exposure': sum(pos.get_market_value() for pos in self.positions.values() if pos.is_open),
                'cash_percentage': self.cash / self.portfolio_value,
                'number_of_positions': len([pos for pos in self.positions.values() if pos.is_open])
            },
            'trailing_stops_active': len(self.trailing_stops),
            'regime_transitions': len(getattr(self, 'regime_transitions', []))
        }
        
        return risk_report