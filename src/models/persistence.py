"""Model persistence utilities for FinRL trading models.

This module provides:
- Model saving and loading with metadata
- Version control for models
- Model registry and management
- Backup and restore functionality
- Model comparison utilities
- Cloud storage integration
"""

import torch
import pickle
import json
import shutil
import hashlib
from typing import Dict, List, Optional, Any, Union, Tuple
from pathlib import Path
from datetime import datetime
import pandas as pd
from dataclasses import dataclass, asdict
import zipfile
import tempfile

from config import settings
from utils import get_logger, log_error, log_performance
from models.sac_agent import SACAgent


@dataclass
class ModelMetadata:
    """Model metadata container."""
    model_id: str
    model_type: str
    version: str
    created_at: str
    updated_at: str
    author: str
    description: str
    
    # Model configuration
    state_dim: int
    action_dim: int
    config: Dict[str, Any]
    
    # Training information
    training_timesteps: Optional[int] = None
    training_episodes: Optional[int] = None
    best_eval_return: Optional[float] = None
    training_duration: Optional[str] = None
    
    # Performance metrics
    sharpe_ratio: Optional[float] = None
    max_drawdown: Optional[float] = None
    win_rate: Optional[float] = None
    
    # Data information
    training_data_hash: Optional[str] = None
    training_period: Optional[str] = None
    symbols: Optional[List[str]] = None
    
    # File information
    file_size: Optional[int] = None
    checksum: Optional[str] = None
    
    # Tags and labels
    tags: Optional[List[str]] = None
    labels: Optional[Dict[str, str]] = None
    
    def __post_init__(self):
        if self.updated_at is None:
            self.updated_at = self.created_at
        if self.tags is None:
            self.tags = []
        if self.labels is None:
            self.labels = {}


class ModelRegistry:
    """Model registry for managing multiple models."""
    
    def __init__(self, registry_dir: str):
        """Initialize model registry.
        
        Args:
            registry_dir: Directory for model registry
        """
        self.registry_dir = Path(registry_dir)
        self.registry_dir.mkdir(parents=True, exist_ok=True)
        
        self.models_dir = self.registry_dir / "models"
        self.models_dir.mkdir(exist_ok=True)
        
        self.metadata_file = self.registry_dir / "registry.json"
        self.logger = get_logger(__name__)
        
        # Load existing registry
        self.registry = self._load_registry()
    
    def _load_registry(self) -> Dict[str, Dict[str, Any]]:
        """Load model registry from file.
        
        Returns:
            Registry dictionary
        """
        if self.metadata_file.exists():
            try:
                with open(self.metadata_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.error(f"Failed to load registry: {e}")
                return {}
        return {}
    
    def _save_registry(self) -> None:
        """Save model registry to file."""
        try:
            with open(self.metadata_file, 'w') as f:
                json.dump(self.registry, f, indent=2, default=str)
        except Exception as e:
            self.logger.error(f"Failed to save registry: {e}")
    
    def register_model(
        self,
        model_id: str,
        metadata: ModelMetadata,
        model_path: str
    ) -> None:
        """Register a model in the registry.
        
        Args:
            model_id: Unique model identifier
            metadata: Model metadata
            model_path: Path to model file
        """
        model_path = Path(model_path)
        
        if not model_path.exists():
            raise FileNotFoundError(f"Model file not found: {model_path}")
        
        # Calculate file info
        metadata.file_size = model_path.stat().st_size
        metadata.checksum = self._calculate_checksum(model_path)
        
        # Copy model to registry
        registry_model_path = self.models_dir / f"{model_id}.pkl"
        shutil.copy2(model_path, registry_model_path)
        
        # Update registry
        self.registry[model_id] = asdict(metadata)
        self._save_registry()
        
        self.logger.info(f"Model registered: {model_id}")
    
    def get_model_metadata(self, model_id: str) -> Optional[ModelMetadata]:
        """Get model metadata.
        
        Args:
            model_id: Model identifier
            
        Returns:
            Model metadata or None if not found
        """
        if model_id in self.registry:
            return ModelMetadata(**self.registry[model_id])
        return None
    
    def list_models(
        self,
        tags: Optional[List[str]] = None,
        model_type: Optional[str] = None,
        sort_by: str = 'created_at',
        ascending: bool = False
    ) -> List[ModelMetadata]:
        """List models in registry.
        
        Args:
            tags: Filter by tags
            model_type: Filter by model type
            sort_by: Sort by field
            ascending: Sort order
            
        Returns:
            List of model metadata
        """
        models = []
        
        for model_id, data in self.registry.items():
            metadata = ModelMetadata(**data)
            
            # Apply filters
            if tags and not any(tag in metadata.tags for tag in tags):
                continue
            
            if model_type and metadata.model_type != model_type:
                continue
            
            models.append(metadata)
        
        # Sort models
        if sort_by in ['created_at', 'updated_at']:
            models.sort(
                key=lambda x: datetime.fromisoformat(getattr(x, sort_by)),
                reverse=not ascending
            )
        elif hasattr(models[0] if models else ModelMetadata, sort_by):
            models.sort(
                key=lambda x: getattr(x, sort_by) or 0,
                reverse=not ascending
            )
        
        return models
    
    def delete_model(self, model_id: str) -> bool:
        """Delete model from registry.
        
        Args:
            model_id: Model identifier
            
        Returns:
            True if deleted successfully
        """
        if model_id not in self.registry:
            return False
        
        # Delete model file
        model_path = self.models_dir / f"{model_id}.pkl"
        if model_path.exists():
            model_path.unlink()
        
        # Remove from registry
        del self.registry[model_id]
        self._save_registry()
        
        self.logger.info(f"Model deleted: {model_id}")
        return True
    
    def get_model_path(self, model_id: str) -> Optional[Path]:
        """Get path to model file.
        
        Args:
            model_id: Model identifier
            
        Returns:
            Path to model file or None if not found
        """
        if model_id in self.registry:
            model_path = self.models_dir / f"{model_id}.pkl"
            if model_path.exists():
                return model_path
        return None
    
    def update_metadata(
        self,
        model_id: str,
        updates: Dict[str, Any]
    ) -> bool:
        """Update model metadata.
        
        Args:
            model_id: Model identifier
            updates: Metadata updates
            
        Returns:
            True if updated successfully
        """
        if model_id not in self.registry:
            return False
        
        # Update metadata
        self.registry[model_id].update(updates)
        self.registry[model_id]['updated_at'] = datetime.now().isoformat()
        
        self._save_registry()
        
        self.logger.info(f"Model metadata updated: {model_id}")
        return True
    
    def search_models(self, query: str) -> List[ModelMetadata]:
        """Search models by description or tags.
        
        Args:
            query: Search query
            
        Returns:
            List of matching models
        """
        query = query.lower()
        matching_models = []
        
        for model_id, data in self.registry.items():
            metadata = ModelMetadata(**data)
            
            # Search in description
            if query in metadata.description.lower():
                matching_models.append(metadata)
                continue
            
            # Search in tags
            if any(query in tag.lower() for tag in metadata.tags):
                matching_models.append(metadata)
                continue
            
            # Search in model ID
            if query in model_id.lower():
                matching_models.append(metadata)
        
        return matching_models
    
    def _calculate_checksum(self, file_path: Path) -> str:
        """Calculate file checksum.
        
        Args:
            file_path: Path to file
            
        Returns:
            MD5 checksum
        """
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def export_registry(self, export_path: str) -> None:
        """Export entire registry to zip file.
        
        Args:
            export_path: Path for exported zip file
        """
        export_path = Path(export_path)
        
        with zipfile.ZipFile(export_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # Add registry metadata
            zipf.write(self.metadata_file, "registry.json")
            
            # Add all model files
            for model_file in self.models_dir.glob("*.pkl"):
                zipf.write(model_file, f"models/{model_file.name}")
        
        self.logger.info(f"Registry exported to {export_path}")
    
    def import_registry(self, import_path: str, merge: bool = True) -> None:
        """Import registry from zip file.
        
        Args:
            import_path: Path to zip file
            merge: Whether to merge with existing registry
        """
        import_path = Path(import_path)
        
        if not import_path.exists():
            raise FileNotFoundError(f"Import file not found: {import_path}")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Extract zip file
            with zipfile.ZipFile(import_path, 'r') as zipf:
                zipf.extractall(temp_path)
            
            # Load imported registry
            imported_registry_file = temp_path / "registry.json"
            if imported_registry_file.exists():
                with open(imported_registry_file, 'r') as f:
                    imported_registry = json.load(f)
                
                # Copy model files
                imported_models_dir = temp_path / "models"
                if imported_models_dir.exists():
                    for model_file in imported_models_dir.glob("*.pkl"):
                        shutil.copy2(model_file, self.models_dir / model_file.name)
                
                # Merge or replace registry
                if merge:
                    self.registry.update(imported_registry)
                else:
                    self.registry = imported_registry
                
                self._save_registry()
                
                self.logger.info(f"Registry imported from {import_path}")
            else:
                raise ValueError("Invalid registry export file")


class ModelPersistence:
    """Main model persistence manager."""
    
    def __init__(self, base_dir: Optional[str] = None):
        """Initialize model persistence manager.
        
        Args:
            base_dir: Base directory for model storage
        """
        self.base_dir = Path(base_dir or settings.model.save_dir)
        self.base_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger = get_logger(__name__)
        
        # Initialize registry
        self.registry = ModelRegistry(str(self.base_dir / "registry"))
    
    @log_performance
    def save_model(
        self,
        agent: SACAgent,
        model_id: str,
        description: str = "",
        tags: Optional[List[str]] = None,
        training_info: Optional[Dict[str, Any]] = None,
        performance_metrics: Optional[Dict[str, Any]] = None
    ) -> str:
        """Save model with metadata.
        
        Args:
            agent: SAC agent to save
            model_id: Unique model identifier
            description: Model description
            tags: Model tags
            training_info: Training information
            performance_metrics: Performance metrics
            
        Returns:
            Path to saved model
        """
        # Create model directory
        model_dir = self.base_dir / model_id
        model_dir.mkdir(parents=True, exist_ok=True)
        
        # Save model
        model_path = model_dir / "model.pkl"
        agent.save_model(str(model_path))
        
        # Create metadata
        metadata = ModelMetadata(
            model_id=model_id,
            model_type="SAC",
            version="1.0.0",
            created_at=datetime.now().isoformat(),
            updated_at=datetime.now().isoformat(),
            author=settings.general.get("author", "unknown"),
            description=description,
            state_dim=agent.state_dim,
            action_dim=agent.action_dim,
            config=agent.config,
            tags=tags or []
        )
        
        # Add training info
        if training_info:
            metadata.training_timesteps = training_info.get('timesteps')
            metadata.training_episodes = training_info.get('episodes')
            metadata.best_eval_return = training_info.get('best_eval_return')
            metadata.training_duration = training_info.get('duration')
        
        # Add performance metrics
        if performance_metrics:
            metadata.sharpe_ratio = performance_metrics.get('sharpe_ratio')
            metadata.max_drawdown = performance_metrics.get('max_drawdown')
            metadata.win_rate = performance_metrics.get('win_rate')
        
        # Save metadata to model directory
        metadata_path = model_dir / "metadata.json"
        with open(metadata_path, 'w') as f:
            json.dump(asdict(metadata), f, indent=2, default=str)
        
        # Register model
        self.registry.register_model(model_id, metadata, str(model_path))
        
        self.logger.info(f"Model saved: {model_id} -> {model_path}")
        return str(model_path)
    
    def load_model(
        self,
        model_id: str,
        version: Optional[str] = None
    ) -> Tuple[SACAgent, ModelMetadata]:
        """Load model by ID.
        
        Args:
            model_id: Model identifier
            version: Model version (not implemented yet)
            
        Returns:
            Tuple of (agent, metadata)
        """
        # Get model path from registry
        model_path = self.registry.get_model_path(model_id)
        
        if model_path is None:
            raise FileNotFoundError(f"Model not found: {model_id}")
        
        # Load metadata
        metadata = self.registry.get_model_metadata(model_id)
        
        if metadata is None:
            raise ValueError(f"Model metadata not found: {model_id}")
        
        # Create and load agent
        agent = SACAgent(
            state_dim=metadata.state_dim,
            action_dim=metadata.action_dim,
            config=metadata.config
        )
        
        agent.load_model(str(model_path))
        
        self.logger.info(f"Model loaded: {model_id}")
        return agent, metadata
    
    def list_models(self, **kwargs) -> List[ModelMetadata]:
        """List available models.
        
        Args:
            **kwargs: Filter arguments for registry.list_models()
            
        Returns:
            List of model metadata
        """
        return self.registry.list_models(**kwargs)
    
    def delete_model(self, model_id: str) -> bool:
        """Delete model.
        
        Args:
            model_id: Model identifier
            
        Returns:
            True if deleted successfully
        """
        # Delete from registry
        success = self.registry.delete_model(model_id)
        
        # Delete model directory
        model_dir = self.base_dir / model_id
        if model_dir.exists():
            shutil.rmtree(model_dir)
        
        return success
    
    def backup_models(
        self,
        backup_path: str,
        model_ids: Optional[List[str]] = None
    ) -> None:
        """Backup models to zip file.
        
        Args:
            backup_path: Path for backup file
            model_ids: Specific models to backup (all if None)
        """
        backup_path = Path(backup_path)
        
        with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # Backup registry
            self.registry.export_registry("temp_registry.zip")
            zipf.write("temp_registry.zip", "registry.zip")
            Path("temp_registry.zip").unlink()
            
            # Backup model directories
            models_to_backup = model_ids or list(self.registry.registry.keys())
            
            for model_id in models_to_backup:
                model_dir = self.base_dir / model_id
                if model_dir.exists():
                    for file_path in model_dir.rglob("*"):
                        if file_path.is_file():
                            arcname = f"models/{model_id}/{file_path.relative_to(model_dir)}"
                            zipf.write(file_path, arcname)
        
        self.logger.info(f"Models backed up to {backup_path}")
    
    def restore_models(self, backup_path: str, merge: bool = True) -> None:
        """Restore models from backup.
        
        Args:
            backup_path: Path to backup file
            merge: Whether to merge with existing models
        """
        backup_path = Path(backup_path)
        
        if not backup_path.exists():
            raise FileNotFoundError(f"Backup file not found: {backup_path}")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Extract backup
            with zipfile.ZipFile(backup_path, 'r') as zipf:
                zipf.extractall(temp_path)
            
            # Restore registry
            registry_backup = temp_path / "registry.zip"
            if registry_backup.exists():
                self.registry.import_registry(str(registry_backup), merge=merge)
            
            # Restore model directories
            models_backup_dir = temp_path / "models"
            if models_backup_dir.exists():
                for model_dir in models_backup_dir.iterdir():
                    if model_dir.is_dir():
                        target_dir = self.base_dir / model_dir.name
                        
                        if not merge and target_dir.exists():
                            shutil.rmtree(target_dir)
                        
                        target_dir.mkdir(parents=True, exist_ok=True)
                        
                        for file_path in model_dir.rglob("*"):
                            if file_path.is_file():
                                target_file = target_dir / file_path.relative_to(model_dir)
                                target_file.parent.mkdir(parents=True, exist_ok=True)
                                shutil.copy2(file_path, target_file)
        
        self.logger.info(f"Models restored from {backup_path}")
    
    def compare_models(
        self,
        model_ids: List[str],
        metrics: Optional[List[str]] = None
    ) -> pd.DataFrame:
        """Compare multiple models.
        
        Args:
            model_ids: List of model IDs to compare
            metrics: Specific metrics to compare
            
        Returns:
            Comparison dataframe
        """
        comparison_data = []
        
        for model_id in model_ids:
            metadata = self.registry.get_model_metadata(model_id)
            if metadata:
                data = {
                    'model_id': model_id,
                    'created_at': metadata.created_at,
                    'best_eval_return': metadata.best_eval_return,
                    'sharpe_ratio': metadata.sharpe_ratio,
                    'max_drawdown': metadata.max_drawdown,
                    'win_rate': metadata.win_rate,
                    'training_timesteps': metadata.training_timesteps,
                    'file_size': metadata.file_size
                }
                
                if metrics:
                    data = {k: v for k, v in data.items() if k in metrics or k == 'model_id'}
                
                comparison_data.append(data)
        
        return pd.DataFrame(comparison_data)
    
    def get_model_info(self, model_id: str) -> Dict[str, Any]:
        """Get detailed model information.
        
        Args:
            model_id: Model identifier
            
        Returns:
            Model information dictionary
        """
        metadata = self.registry.get_model_metadata(model_id)
        
        if metadata is None:
            raise ValueError(f"Model not found: {model_id}")
        
        model_dir = self.base_dir / model_id
        
        info = asdict(metadata)
        info['model_directory'] = str(model_dir)
        info['files'] = []
        
        if model_dir.exists():
            for file_path in model_dir.rglob("*"):
                if file_path.is_file():
                    info['files'].append({
                        'name': file_path.name,
                        'path': str(file_path.relative_to(model_dir)),
                        'size': file_path.stat().st_size
                    })
        
        return info