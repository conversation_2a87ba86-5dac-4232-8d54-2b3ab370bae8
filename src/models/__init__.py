"""Models package for FinRL-based stock trading agent.

This package contains:
- SAC (Soft Actor-Critic) agent implementation using ElegantRL
- Training and evaluation utilities
- Hyperparameter optimization with Optuna
- Model persistence and versioning
- Performance monitoring and analysis
"""

from .sac_agent import SACAgent
from .training import ModelTrainer, TrainingConfig, TrainingMetrics, EarlyStopping, TrainingMonitor
from .optimization import (
    HyperparameterOptimizer, 
    OptimizationConfig, 
    ObjectiveFunction,
    SingleObjective,
    MultiObjective
)
from .persistence import ModelPersistence, ModelRegistry, ModelMetadata

__all__ = [
    'SACAgent',
    'ModelTrainer',
    'TrainingConfig', 
    'TrainingMetrics',
    'EarlyStopping',
    'TrainingMonitor',
    'HyperparameterOptimizer',
    'OptimizationConfig',
    'ObjectiveFunction',
    'SingleObjective',
    'MultiObjective',
    'ModelPersistence',
    'ModelRegistry',
    'ModelMetadata'
]