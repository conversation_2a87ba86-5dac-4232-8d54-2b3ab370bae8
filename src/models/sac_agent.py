"""SAC (Soft Actor-Critic) agent implementation using ElegantRL.

This module provides:
- SAC agent configuration and initialization
- Integration with FinRL trading environment
- Model training and inference capabilities
- Model persistence and loading
- Performance monitoring and logging
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
import pickle
from datetime import datetime

try:
    # from elegantrl.envs import StockTradingEnv # Corrected import path
    from trading.asymmetric_env import AsymmetricTradingEnv
    from elegantrl.agents import AgentSAC
    from elegantrl.train.run import train_agent # Use standard trainer
    from elegantrl.train.config import Config # Updated config class
except ImportError:
    # Fallback for development/testing
    AgentSAC = None
    AsymmetricTradingEnv = None
    train_agent = None
    Config = None

from config import settings, SAC_DEFAULTS
from utils import get_logger, log_performance, log_model_metrics, log_error


class SACAgent:
    """SAC agent for stock trading using ElegantRL."""
    
    def __init__(
        self,
        state_dim: int,
        action_dim: int,
        config: Optional[Dict[str, Any]] = None,
        asymmetric_config: Optional[Any] = None
    ):
        """Initialize SAC agent.
        
        Args:
            state_dim: Dimension of state space
            action_dim: Dimension of action space
            config: Agent configuration parameters
            asymmetric_config: Asymmetric strategy configuration
        """
        self.logger = get_logger(__name__)
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.asymmetric_config = asymmetric_config
        
        # Use hierarchical settings with optional config overrides
        # Note: config overrides should now be applied via settings.apply_overrides() before agent creation
        self.config = config or {}
        
        # Use settings directly for consistent configuration
        # Any overrides should have been applied to settings before agent creation
        self.sac_config = settings.sac.dict()
        
        # Apply any additional config overrides (for backward compatibility)
        if self.config:
            self.sac_config.update(self.config)
            self.logger.info(f"Applied additional config overrides: {self.config}")
        
        # Initialize agent
        self.agent = None
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Training state
        self.is_trained = False
        self.training_history = []
        self.model_path = None
        
        self.logger.info(
            f"SAC Agent initialized: state_dim={state_dim}, action_dim={action_dim}, "
            f"device={self.device}"
        )
    
    def _create_agent(self):
        """Initializes the SAC agent from ElegantRL."""
        self.logger.info("Creating SAC agent")
        # Ensure that the configuration uses the correct keys as expected by ElegantRL
        # For example, if ElegantRL expects 'net_dims' (plural) but config has 'net_dim' (singular)
        # ElegantRL's AgentSAC typically uses net_dims for actor network layers.
        # We will use actor_hidden_sizes from our config for this.
        erl_config = Config() # Already imported at the top of the file

        # Populate erl_config from self.sac_config, respecting Config's defaults
        erl_config.learning_rate = self.sac_config.get('learning_rate', erl_config.learning_rate)
        erl_config.gamma = self.sac_config.get('gamma', erl_config.gamma)
        erl_config.batch_size = self.sac_config.get('batch_size', erl_config.batch_size)
        erl_config.buffer_size = self.sac_config.get('buffer_size', erl_config.buffer_size)
        erl_config.break_step = self.sac_config.get('total_timesteps', erl_config.break_step) # Changed target_step to break_step
        erl_config.repeat_times = float(self.sac_config.get('gradient_steps', erl_config.repeat_times))
        erl_config.if_use_per = self.sac_config.get('if_per_or_gae', erl_config.if_use_per) # Changed if_per_or_gae to if_use_per
        
        # net_dims for actor/critic networks within AgentBase, taken from actor_hidden_sizes
        erl_config.net_dims = self.sac_config.get('actor_hidden_sizes', erl_config.net_dims)
        # If critic has different structure, ElegantRL might have erl_config.net_dims_cri
        # For now, assume critic uses same net_dims or actor_hidden_sizes implicitly

        # SAC specific parameters for Config
        # AgentSAC calculates target_entropy as `action_dim` if not provided in args
        # target_entropy = getattr(args, 'target_entropy', action_dim)
        # So, we can set it if specified, otherwise ElegantRL's default will be used.
        if 'target_entropy' in self.sac_config:
            erl_config.target_entropy = self.sac_config['target_entropy']
        else:
            erl_config.target_entropy = -float(self.action_dim) # Common default for SAC

        erl_config.soft_update_tau = self.sac_config.get('tau', erl_config.soft_update_tau)
        
        # Parameters for exploration and updates
        # Note: explore_step will be set dynamically in train() method based on total_timesteps
        # erl_config.explore_step = self.sac_config.get('learning_starts', getattr(erl_config, 'explore_step', 1000))
        # target_update_freq is often called target_network_update_freq or similar in ElegantRL's Config
        # Let's assume 'target_update_interval' maps to a similar concept or is handled by agent's internal logic.
        # ElegantRL's AgentSAC updates target critic via soft_update, not interval-based hard updates typically.
        # The 'target_update_interval' from sac_config might be for other agent types or a custom logic.
        # For standard SAC, tau is the key for target updates.

        # Determine gpu_id_to_pass based on self.sac_config.device
        device_setting = self.sac_config.get('device', 'cpu').lower()
        if device_setting == 'cpu':
            gpu_id_to_pass = -1
        elif device_setting.startswith('cuda'):
            try:
                gpu_id_to_pass = int(device_setting.split(':')[-1]) if ':' in device_setting else 0
            except ValueError:
                self.logger.warning(f"Invalid CUDA device format: {device_setting}. Defaulting to GPU 0.")
                gpu_id_to_pass = 0
        else:
            self.logger.warning(f"Unsupported device: {device_setting}. Defaulting to CPU.")
            gpu_id_to_pass = -1
        self.logger.info(f"SACAgent _create_agent: Determined gpu_id_to_pass: {gpu_id_to_pass} from device setting: {device_setting}")

        try:
            # AgentSAC is already imported at the top of the file
            agent = AgentSAC(
                net_dims=self.sac_config.get('net_dims', self.sac_config['actor_hidden_sizes']),  # Use net_dims if available, fallback to actor_hidden_sizes
                state_dim=self.state_dim,
                action_dim=self.action_dim,
                gpu_id=gpu_id_to_pass,
                args=erl_config
            )
            # agent.save_or_load_agent(cwd=self.model_dir, if_save=False) # Load existing model if available
            # The model_dir might not be set at this stage, let's defer loading or handle it in train
            net_dims_value = self.sac_config.get('net_dims', self.sac_config['actor_hidden_sizes'])
            self.logger.info(f"SAC agent created successfully with params: {{'net_dims': {net_dims_value}, 'state_dim': {self.state_dim}, 'action_dim': {self.action_dim}, 'gpu_id': {gpu_id_to_pass}, 'args': {erl_config.__dict__}}}")
            return agent
        except ImportError as e:
            self.logger.error(f"Failed to import AgentSAC: {e}. Ensure ElegantRL is correctly installed and accessible.")
            raise
        except Exception as e:
            self.logger.error(f"Error creating SAC agent: {e}")
            net_dims_value = self.sac_config.get('net_dims', self.sac_config['actor_hidden_sizes'])
            self.logger.error(f"Agent params: {{'net_dims': {net_dims_value}, 'state_dim': {self.state_dim}, 'action_dim': {self.action_dim}, 'gpu_id': {gpu_id_to_pass}, 'args': {erl_config.__dict__}}}")
            raise
    
    @log_performance
    def train(
        self,
        env,
        total_timesteps: int,
        eval_env=None,
        eval_freq: int = 10000,
        save_freq: int = 50000,
        model_dir: Optional[str] = None
    ) -> Dict[str, Any]:
        """Train the SAC agent.
        
        Args:
            env: Training environment
            total_timesteps: Total training timesteps
            eval_env: Evaluation environment
            eval_freq: Evaluation frequency
            save_freq: Model saving frequency
            model_dir: Directory to save models
            
        Returns:
            Training results dictionary
        """
        if self.agent is None:
            self.agent = self._create_agent()
        
        model_dir = model_dir or settings.sac.checkpoint_dir # Use corrected checkpoint_dir
        model_path = Path(model_dir)
        model_path.mkdir(parents=True, exist_ok=True)
        
        self.logger.info(f"Starting SAC training: {total_timesteps} timesteps")
        
        try:
            # Get the actual state space from the environment to ensure consistency
            actual_state_space = getattr(env, 'observation_space', None)
            if actual_state_space is not None and hasattr(actual_state_space, 'shape'):
                actual_state_dim = actual_state_space.shape[0]
                print(f"[SAC DEBUG] Got actual_state_dim from env.observation_space.shape[0]: {actual_state_dim}")
            else:
                actual_state_dim = self.state_dim
                print(f"[SAC DEBUG] Using fallback actual_state_dim from self.state_dim: {actual_state_dim}")
            
            # Debug: Test actual state from environment
            if hasattr(env, '_get_state'):
                try:
                    test_state = env._get_state()
                    print(f"[SAC DEBUG] Test state from env._get_state(): shape={test_state.shape}, dtype={test_state.dtype}")
                    if test_state.shape[0] != actual_state_dim:
                        print(f"[SAC DEBUG] CRITICAL MISMATCH! env._get_state() returns {test_state.shape[0]} but observation_space suggests {actual_state_dim}")
                        actual_state_dim = test_state.shape[0]  # Use the actual state size
                        print(f"[SAC DEBUG] Updated actual_state_dim to match actual state: {actual_state_dim}")
                except Exception as e:
                    print(f"[SAC DEBUG] Could not get test state from env._get_state(): {e}")
            
            print(f"[SAC DEBUG] Final actual_state_dim being used: {actual_state_dim}")
            print(f"[SAC DEBUG] Original self.state_dim: {self.state_dim}")
            
            # Configure training using Config object
            config = Config()
            config.agent_class = AgentSAC # Set the agent class
            config.agent = self.agent # Pass the instantiated agent
            
            # Set essential agent parameters
            config.net_dims = self.sac_config.get('net_dims', self.sac_config['actor_hidden_sizes'])
            config.state_dim = actual_state_dim  # Use actual state dimension from environment
            config.action_dim = self.action_dim

            # Determine gpu_id for ElegantRL's Config based on self.sac_config.device
            device_setting_train = self.sac_config.get('device', 'cpu').lower()
            if device_setting_train == 'cpu':
                config.gpu_id = -1
            elif device_setting_train.startswith('cuda'):
                try:
                    config.gpu_id = int(device_setting_train.split(':')[-1]) if ':' in device_setting_train else 0
                except ValueError:
                    self.logger.warning(f"Invalid CUDA device format in train: {device_setting_train}. Defaulting to GPU 0.")
                    config.gpu_id = 0
            else:
                self.logger.warning(f"Unsupported device in train: {device_setting_train}. Defaulting to CPU.")
                config.gpu_id = -1
            self.logger.info(f"SACAgent train: Set ElegantRL config.gpu_id: {config.gpu_id} from device setting: {device_setting_train}")

            # CRITICAL FIX: Use pre-instantiated environment instead of letting ElegantRL create new ones
            # This ensures our fixed AsymmetricTradingEnv with proper reward calculation is used
            config.env = env  # Pass the actual environment instance
            config.env_class = type(env)  # Pass the class for ElegantRL's build_env function

            # CRITICAL FIX: Provide complete environment configuration as env_args
            # If ElegantRL tries to create a new environment, it will have all required parameters
            config.env_args = {
                'df': env.df,
                'stock_dim': env.stock_dim,
                'hmax': env.hmax,
                'initial_amount': env.initial_amount,
                'num_stock_shares': env.num_stock_shares,
                'buy_cost_pct': env.buy_cost_pct,
                'sell_cost_pct': env.sell_cost_pct,
                'reward_scaling': getattr(env, 'reward_scaling', 1e-4),
                'state_space': getattr(env, 'state_space', env.observation_space.shape[0] if hasattr(env, 'observation_space') else None),
                'action_space': env.action_space.shape[0] if hasattr(env, 'action_space') and hasattr(env.action_space, 'shape') else env.stock_dim,
                'tech_indicator_list': getattr(env, 'tech_indicator_list', []),
                'turbulence_threshold': getattr(env, 'turbulence_threshold', None),
                'risk_indicator_col': getattr(env, 'risk_indicator_col', 'turbulence'),
                'make_plots': getattr(env, 'make_plots', False),
                'print_verbosity': getattr(env, 'print_verbosity', 10),
                'day': getattr(env, 'day', 0),
                'initial': getattr(env, 'initial', True),
                'previous_state': getattr(env, 'previous_state', None),
                'model_name': getattr(env, 'model_name', ''),
                'mode': getattr(env, 'mode', ''),
                'iteration': getattr(env, 'iteration', ''),
                'asymmetric_config': getattr(env, 'asymmetric_config', None),
                'log_level': getattr(env, 'log_level', 'INFO'),
                'env_name': getattr(env, 'env_name', 'AsymmetricTradingEnv-v1'),
                'state_dim': env.observation_space.shape[0] if hasattr(env, 'observation_space') else getattr(env, 'state_space', None),
                'action_dim': env.action_space.shape[0] if hasattr(env, 'action_space') and hasattr(env.action_space, 'shape') else env.stock_dim,
                'if_discrete': getattr(env, 'if_discrete', False)
            }

            # Set environment metadata that ElegantRL might need
            config.env_name = getattr(env, 'env_name', 'AsymmetricTradingEnv-v1')
            # FIXED: max_step is per-episode limit, completely independent of total training timesteps
            # max_step = maximum steps per trading episode (e.g., number of trading days)
            # This should NOT be related to total_timesteps which controls total training duration
            default_max_step = len(env.df.index.unique()) if hasattr(env, 'df') else 20000
            config.max_step = getattr(env, 'max_step', default_max_step)  # Per-episode limit only
            config.if_discrete = False  # SAC is for continuous actions

            if eval_env:
                # CRITICAL FIX: Use pre-instantiated evaluation environment as well
                # This ensures our fixed AsymmetricTradingEnv with proper reward calculation is used for evaluation
                config.eval_env = eval_env  # Pass the actual evaluation environment instance
                config.eval_env_class = type(eval_env)  # Pass the class for ElegantRL's build_env function



                # CRITICAL FIX: Provide complete evaluation environment configuration as eval_env_args
                # If ElegantRL tries to create a new evaluation environment, it will have all required parameters
                config.eval_env_args = {
                    'df': eval_env.df,
                    'stock_dim': eval_env.stock_dim,
                    'hmax': eval_env.hmax,
                    'initial_amount': eval_env.initial_amount,
                    'num_stock_shares': eval_env.num_stock_shares,
                    'buy_cost_pct': eval_env.buy_cost_pct,
                    'sell_cost_pct': eval_env.sell_cost_pct,
                    'reward_scaling': getattr(eval_env, 'reward_scaling', 1e-4),
                    'state_space': getattr(eval_env, 'state_space', eval_env.observation_space.shape[0] if hasattr(eval_env, 'observation_space') else None),
                    'action_space': eval_env.action_space.shape[0] if hasattr(eval_env, 'action_space') and hasattr(eval_env.action_space, 'shape') else eval_env.stock_dim,
                    'tech_indicator_list': getattr(eval_env, 'tech_indicator_list', []),
                    'turbulence_threshold': getattr(eval_env, 'turbulence_threshold', None),
                    'risk_indicator_col': getattr(eval_env, 'risk_indicator_col', 'turbulence'),
                    'make_plots': getattr(eval_env, 'make_plots', False),
                    'print_verbosity': getattr(eval_env, 'print_verbosity', 10),
                    'day': getattr(eval_env, 'day', 0),
                    'initial': getattr(eval_env, 'initial', True),
                    'previous_state': getattr(eval_env, 'previous_state', None),
                    'model_name': getattr(eval_env, 'model_name', ''),
                    'mode': getattr(eval_env, 'mode', ''),
                    'iteration': getattr(eval_env, 'iteration', ''),
                    'asymmetric_config': getattr(eval_env, 'asymmetric_config', None),
                    'log_level': getattr(eval_env, 'log_level', 'INFO'),
                    'env_name': getattr(eval_env, 'env_name', 'AsymmetricTradingEnv-v1'),
                    'state_dim': eval_env.observation_space.shape[0] if hasattr(eval_env, 'observation_space') else getattr(eval_env, 'state_space', None),
                    'action_dim': eval_env.action_space.shape[0] if hasattr(eval_env, 'action_space') and hasattr(eval_env.action_space, 'shape') else eval_env.stock_dim,
                    'if_discrete': getattr(eval_env, 'if_discrete', False)
                }
            else:
                config.eval_env = None
                config.eval_env_class = None
                config.eval_env_args = {}

            # General training parameters
            config.cwd = str(model_path)
            config.random_seed = self.sac_config.get('random_seed', 42)
            config.break_step = total_timesteps # Total training steps for the agent (FIXED: was target_step)
            
            # CLEAN CONFIGURATION: Set all timestep parameters with clear purposes
            self.logger.info(f"=== TRAINING CONFIGURATION ===")
            self.logger.info(f"total_timesteps = {total_timesteps} (main training control - when to stop)")
            self.logger.info(f"break_step = {total_timesteps} (ElegantRL equivalent of total_timesteps)")
            self.logger.info(f"max_step = {config.max_step} (per-episode limit - max steps per trading episode)")
            config.break_step = total_timesteps

            # CLEAN PARAMETER SETTING: Handle small total_timesteps by adjusting dependent parameters
            if total_timesteps < 1000:
                # For small timesteps, start learning early and evaluate frequently
                config.explore_step = max(1, total_timesteps // 4)  # Start learning after 25% of timesteps
                config.eval_gap = max(1, total_timesteps // 2)      # Evaluate at 50% of timesteps
                # CRITICAL FIX: Adjust horizon_len for small timesteps to prevent infinite training
                config.horizon_len = min(config.horizon_len, max(1, total_timesteps // 2))  # Ensure horizon_len doesn't exceed half of total_timesteps
                self.logger.info(f"explore_step = {config.explore_step} (learning starts - 25% of total for small timesteps)")
                self.logger.info(f"eval_gap = {config.eval_gap} (evaluation frequency - 50% of total for small timesteps)")
                self.logger.info(f"horizon_len = {config.horizon_len} (steps per iteration - adjusted for small timesteps)")
            else:
                # For normal timesteps, use standard configuration
                config.explore_step = self.sac_config.get('learning_starts', getattr(config, 'explore_step', 1000))
                config.eval_gap = eval_freq # Evaluate every 'eval_gap' steps
                self.logger.info(f"explore_step = {config.explore_step} (learning starts - from config)")
                self.logger.info(f"eval_gap = {config.eval_gap} (evaluation frequency - from eval_freq parameter)")
                self.logger.info(f"horizon_len = {config.horizon_len} (steps per iteration - default)")
            self.logger.info(f"=== END CONFIGURATION ===")

            config.eval_times = 1 # Number of episodes for each evaluation
            
            # Agent hyperparameters (often duplicated in Config or read from agent instance)
            # These should align with how self.agent was initialized.
            # Example: config.gamma = self.agent.gamma (if Config needs it explicitly)
            # For now, assume train_agent uses parameters from config.agent instance.

            # Model saving frequency (maps to Config's save_gap for periodic saves)
            if save_freq > 0 and eval_freq > 0:
                config.save_gap = max(1, int(save_freq / eval_freq)) # Save every 'save_gap' evaluations
            else:
                config.save_gap = int(8) # Default from Config if not specified or calculable

            config.if_remove = False # Whether to remove old experiment files in cwd, default False

            # Initialize config (sets seeds, creates envs if needed by the training function)
            config.init_before_training()

            # Start training with ElegantRL's train_agent (multiprocessing)
            # The pickle issue will be resolved by using standard Python logging
            training_results = train_agent(config)
            
            # Update training state
            self.is_trained = True
            self.model_path = model_path / 'actor.pth'
            
            # Log training metrics
            if training_results:
                log_model_metrics(
                    'SAC',
                    training_results.get('episode_return', 0),
                    training_results.get('episode_len', 0),
                    total_timesteps
                )
            
            self.logger.success(f"SAC training completed: {total_timesteps} timesteps")
            
            return {
                'success': True,
                'timesteps': total_timesteps,
                'model_path': str(self.model_path),
                'results': training_results
            }
            
        except Exception as e:
            log_error("SAC training failed", e)
            return {
                'success': False,
                'error': str(e)
            }



    def predict(
        self,
        state: np.ndarray,
        deterministic: bool = True
    ) -> Tuple[np.ndarray, Optional[Dict[str, Any]]]:
        """Predict action for given state.
        
        Args:
            state: Current state
            deterministic: Whether to use deterministic policy
            
        Returns:
            Tuple of (action, info_dict)
        """
        if self.agent is None:
            raise ValueError("Agent not initialized. Train or load a model first.")
        
        # Ensure state is proper format
        if isinstance(state, (list, tuple)):
            state = np.array(state, dtype=np.float32)
        
        if state.ndim == 1:
            state = state.reshape(1, -1)
        
        # Convert to tensor
        state_tensor = torch.FloatTensor(state).to(self.device)
        
        # Get action
        with torch.no_grad():
            if deterministic:
                action = self.agent.act(state_tensor)
            else:
                action = self.agent.select_action(state_tensor)

        # Convert back to numpy
        if isinstance(action, torch.Tensor):
            action = action.cpu().numpy()

        # FIX: Ensure action has correct shape for environment
        # The action should be a 1D array with length equal to action_dim (number of stocks)
        if action.ndim > 1:
            action = action.flatten()

        # Ensure action has correct length
        if len(action) != self.action_dim:
            self.logger.warning(f"Action shape mismatch: got {action.shape}, expected ({self.action_dim},). Reshaping...")
            if len(action) == 1 and self.action_dim > 1:
                # If we got a single action but need multiple, repeat it
                action = np.full(self.action_dim, action[0])
            elif len(action) > self.action_dim:
                # If we got too many actions, truncate
                action = action[:self.action_dim]
            else:
                # If we got too few actions, pad with zeros
                padded_action = np.zeros(self.action_dim)
                padded_action[:len(action)] = action
                action = padded_action

        # Ensure action is float32
        action = action.astype(np.float32)

        return action, None
    
    def save_model(self, filepath: str) -> None:
        """Save the trained model.
        
        Args:
            filepath: Path to save the model
        """
        if self.agent is None:
            raise ValueError("No agent to save")
        
        filepath = Path(filepath)
        filepath.parent.mkdir(parents=True, exist_ok=True)
        
        # Save agent state
        model_data = {
            'agent_state': self.agent.state_dict() if hasattr(self.agent, 'state_dict') else None,
            'config': self.config,
            'sac_config': self.sac_config,
            'state_dim': self.state_dim,
            'action_dim': self.action_dim,
            'is_trained': self.is_trained,
            'training_history': self.training_history,
            'timestamp': datetime.now().isoformat()
        }
        
        with open(filepath, 'wb') as f:
            pickle.dump(model_data, f)
        
        self.model_path = filepath
        self.logger.info(f"Model saved to {filepath}")
    
    def load_model(self, filepath: str) -> None:
        """Load a trained model.
        
        Args:
            filepath: Path to the saved model
        """
        filepath = Path(filepath)
        
        if not filepath.exists():
            raise FileNotFoundError(f"Model file not found: {filepath}")
        
        try:
            with open(filepath, 'rb') as f:
                model_data = pickle.load(f)
            
            # Restore configuration
            self.config = model_data['config']
            self.sac_config = model_data['sac_config']
            self.state_dim = model_data['state_dim']
            self.action_dim = model_data['action_dim']
            self.is_trained = model_data['is_trained']
            self.training_history = model_data.get('training_history', [])
            
            # Recreate agent
            self.agent = self._create_agent()
            
            # Load agent state if available
            if model_data.get('agent_state') and hasattr(self.agent, 'load_state_dict'):
                self.agent.load_state_dict(model_data['agent_state'])
            
            self.model_path = filepath
            self.logger.info(f"Model loaded from {filepath}")
            
        except Exception as e:
            log_error(f"Failed to load model from {filepath}", e)
            raise
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the current model.
        
        Returns:
            Dictionary with model information
        """
        return {
            'state_dim': self.state_dim,
            'action_dim': self.action_dim,
            'is_trained': self.is_trained,
            'model_path': str(self.model_path) if self.model_path else None,
            'config': self.config,
            'sac_config': self.sac_config,
            'device': str(self.device),
            'training_episodes': len(self.training_history)
        }
    
    def update_config(self, new_config: Dict[str, Any]) -> None:
        """Update agent configuration.
        
        Args:
            new_config: New configuration parameters
        """
        self.config.update(new_config)
        self.sac_config.update(new_config)
        
        # Recreate agent if it exists
        if self.agent is not None:
            self.agent = self._create_agent()
            self.is_trained = False  # Need to retrain with new config
        
        self.logger.info(f"Updated agent configuration: {new_config}")
    
    def get_action_distribution(self, state: np.ndarray) -> Dict[str, np.ndarray]:
        """Get action distribution for analysis.
        
        Args:
            state: Current state
            
        Returns:
            Dictionary with action statistics
        """
        if self.agent is None:
            raise ValueError("Agent not initialized")
        
        # Get multiple action samples for distribution analysis
        actions = []
        for _ in range(100):  # Sample 100 actions
            action, _ = self.predict(state, deterministic=False)
            actions.append(action.flatten())
        
        actions = np.array(actions)
        
        return {
            'mean': np.mean(actions, axis=0),
            'std': np.std(actions, axis=0),
            'min': np.min(actions, axis=0),
            'max': np.max(actions, axis=0),
            'samples': actions
        }
    
    def evaluate_policy(
        self,
        env,
        n_episodes: int = 10,
        deterministic: bool = True
    ) -> Dict[str, float]:
        """Evaluate the current policy.
        
        Args:
            env: Environment for evaluation
            n_episodes: Number of episodes to evaluate
            deterministic: Whether to use deterministic policy
            
        Returns:
            Evaluation metrics
        """
        if self.agent is None:
            raise ValueError("Agent not initialized")
        
        episode_returns = []
        episode_lengths = []
        
        for episode in range(n_episodes):
            # FIX: Handle environment reset properly - it returns (state, info)
            reset_result = env.reset()
            if isinstance(reset_result, tuple):
                state, _ = reset_result  # Unpack (state, info)
            else:
                state = reset_result  # Direct state return (fallback)

            episode_return = 0
            episode_length = 0
            done = False

            while not done:
                action, _ = self.predict(state, deterministic=deterministic)

                # DEBUG: Log action details for troubleshooting
                if episode == 0 and episode_length < 3:  # Only log first few steps of first episode
                    self.logger.debug(f"Evaluation episode {episode}, step {episode_length}: action shape={action.shape}, action={action}")

                step_result = env.step(action)

                # FIX: Handle step result properly - it might return 4 or 5 values
                if len(step_result) == 5:
                    state, reward, done, truncated, _ = step_result
                    # For compatibility, treat truncated as done
                    done = done or truncated
                elif len(step_result) == 4:
                    state, reward, done, _ = step_result
                else:
                    # Fallback for unexpected return format
                    state, reward, done = step_result[0], step_result[1], step_result[2]

                episode_return += reward
                episode_length += 1
            
            episode_returns.append(episode_return)
            episode_lengths.append(episode_length)
        
        return {
            'mean_return': np.mean(episode_returns),
            'std_return': np.std(episode_returns),
            'min_return': np.min(episode_returns),
            'max_return': np.max(episode_returns),
            'mean_length': np.mean(episode_lengths),
            'episodes_evaluated': n_episodes
        }
    
    def reset(self) -> None:
        """Reset the agent to initial state."""
        self.agent = None
        self.is_trained = False
        self.training_history = []
        self.model_path = None
        
        self.logger.info("Agent reset to initial state")
