"""Data acquisition and processing package for FinRL Trading Agent.

This package handles:
- Market data fetching from yfinance and Alpaca
- VIX data integration for volatility regime detection
- Technical indicator calculation using pandas_ta
- Data caching and validation
- Data preprocessing for RL training
"""

from .fetcher import DataFetcher
from .processor import DataProcessor
from .cache import DataCache
from .validator import DataValidator

__all__ = [
    'DataFetcher',
    'DataProcessor', 
    'DataCache',
    'DataValidator'
]