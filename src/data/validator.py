"""Data validation module for ensuring data quality and integrity.

This module provides:
- Comprehensive data quality checks
- OHLCV data validation
- Missing data detection and handling
- Outlier detection and filtering
- Data consistency validation
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime, timedelta

from config import DATA_QUALITY_THRESHOLDS, MARKET_HOURS
from utils import get_logger, log_data_quality, log_error


class DataValidator:
    """Comprehensive data validation for market data."""
    
    def __init__(self):
        """Initialize the data validator."""
        self.logger = get_logger(__name__)
        self.quality_thresholds = DATA_QUALITY_THRESHOLDS
    
    def validate_data_quality(self, data: pd.DataFrame, symbol: str) -> float:
        """Validate overall data quality and return a quality score.
        
        Args:
            data: DataFrame with OHLCV data
            symbol: Stock symbol for logging
            
        Returns:
            Quality score between 0 and 1 (1 = perfect quality)
        """
        if data.empty:
            self.logger.warning(f"Empty data for {symbol}")
            return 0.0
        
        quality_checks = {
            'completeness': self._check_completeness(data),
            'consistency': self._check_ohlcv_consistency(data),
            'outliers': self._check_outliers(data),
            'missing_data': self._check_missing_data(data),
            'volume_validity': self._check_volume_validity(data),
            'price_validity': self._check_price_validity(data),
            'date_continuity': self._check_date_continuity(data)
        }
        
        # Calculate weighted quality score
        weights = {
            'completeness': 0.20,
            'consistency': 0.20,
            'outliers': 0.15,
            'missing_data': 0.15,
            'volume_validity': 0.10,
            'price_validity': 0.10,
            'date_continuity': 0.10
        }
        
        quality_score = sum(
            quality_checks[check] * weights[check]
            for check in quality_checks
        )
        
        self.logger.debug(
            f"Data quality for {symbol}: {quality_score:.3f} - {quality_checks}"
        )
        
        return quality_score
    
    def _check_completeness(self, data: pd.DataFrame) -> float:
        """Check data completeness.
        
        Args:
            data: DataFrame to check
            
        Returns:
            Completeness score (0-1)
        """
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        available_columns = [col for col in required_columns if col in data.columns]
        
        if not available_columns:
            return 0.0
        
        # Check for non-null values in required columns
        non_null_ratio = data[available_columns].notna().all(axis=1).mean()
        
        return non_null_ratio
    
    def _check_ohlcv_consistency(self, data: pd.DataFrame) -> float:
        """Check OHLCV data consistency.
        
        Args:
            data: DataFrame to check
            
        Returns:
            Consistency score (0-1)
        """
        if not all(col in data.columns for col in ['open', 'high', 'low', 'close']):
            return 0.0
        
        consistency_checks = []
        
        # High should be >= Open, Close
        high_vs_open = (data['high'] >= data['open']).mean()
        high_vs_close = (data['high'] >= data['close']).mean()
        consistency_checks.extend([high_vs_open, high_vs_close])
        
        # Low should be <= Open, Close
        low_vs_open = (data['low'] <= data['open']).mean()
        low_vs_close = (data['low'] <= data['close']).mean()
        consistency_checks.extend([low_vs_open, low_vs_close])
        
        # High should be >= Low
        high_vs_low = (data['high'] >= data['low']).mean()
        consistency_checks.append(high_vs_low)
        
        return np.mean(consistency_checks)
    
    def _check_outliers(self, data: pd.DataFrame) -> float:
        """Check for price outliers using statistical methods.
        
        Args:
            data: DataFrame to check
            
        Returns:
            Outlier score (0-1, higher is better)
        """
        if 'close' not in data.columns or len(data) < 10:
            return 1.0
        
        # Calculate daily returns
        returns = data['close'].pct_change().dropna()
        
        if len(returns) == 0:
            return 1.0
        
        # Use IQR method to detect outliers
        q1 = returns.quantile(0.25)
        q3 = returns.quantile(0.75)
        iqr = q3 - q1
        
        lower_bound = q1 - 1.5 * iqr
        upper_bound = q3 + 1.5 * iqr
        
        outliers = ((returns < lower_bound) | (returns > upper_bound)).sum()
        outlier_ratio = outliers / len(returns)
        
        # Score is 1 - outlier_ratio, capped at reasonable threshold
        return max(0.0, 1.0 - min(outlier_ratio, 0.1) / 0.1)
    
    def _check_missing_data(self, data: pd.DataFrame) -> float:
        """Check for missing data patterns.
        
        Args:
            data: DataFrame to check
            
        Returns:
            Missing data score (0-1, higher is better)
        """
        if data.empty:
            return 0.0
        
        # Check overall missing data ratio
        total_cells = data.size
        missing_cells = data.isna().sum().sum()
        missing_ratio = missing_cells / total_cells
        
        # Penalize high missing data ratios
        if missing_ratio > 0.1:  # More than 10% missing
            return 0.0
        elif missing_ratio > 0.05:  # More than 5% missing
            return 0.5
        else:
            return 1.0 - missing_ratio * 10  # Linear penalty for small amounts
    
    def _check_volume_validity(self, data: pd.DataFrame) -> float:
        """Check volume data validity.
        
        Args:
            data: DataFrame to check
            
        Returns:
            Volume validity score (0-1)
        """
        if 'volume' not in data.columns:
            return 0.5  # Neutral score if no volume data
        
        volume = data['volume']
        
        # Check for negative volumes
        negative_volumes = (volume < 0).sum()
        if negative_volumes > 0:
            return 0.0
        
        # Check for zero volumes (suspicious for stocks)
        zero_volumes = (volume == 0).sum()
        zero_ratio = zero_volumes / len(volume)
        
        if zero_ratio > 0.1:  # More than 10% zero volumes
            return 0.3
        elif zero_ratio > 0.05:  # More than 5% zero volumes
            return 0.7
        else:
            return 1.0
    
    def _check_price_validity(self, data: pd.DataFrame) -> float:
        """Check price data validity.
        
        Args:
            data: DataFrame to check
            
        Returns:
            Price validity score (0-1)
        """
        price_columns = ['open', 'high', 'low', 'close']
        available_prices = [col for col in price_columns if col in data.columns]
        
        if not available_prices:
            return 0.0
        
        validity_checks = []
        
        for col in available_prices:
            prices = data[col]
            
            # Check for non-positive prices
            positive_prices = (prices > 0).mean()
            validity_checks.append(positive_prices)
            
            # Check for extremely small prices (likely data errors)
            reasonable_prices = (prices > 0.01).mean()
            validity_checks.append(reasonable_prices)
        
        return np.mean(validity_checks)
    
    def _check_date_continuity(self, data: pd.DataFrame) -> float:
        """Check date continuity and business day alignment.
        
        Args:
            data: DataFrame to check
            
        Returns:
            Date continuity score (0-1)
        """
        if 'Date' not in data.columns or len(data) < 2:
            return 1.0
        
        dates = pd.to_datetime(data['Date']).sort_values()
        
        # Check for duplicate dates
        duplicate_dates = dates.duplicated().sum()
        if duplicate_dates > 0:
            return 0.5
        
        # Check for reasonable date gaps
        date_diffs = dates.diff().dropna()
        
        # Most gaps should be 1 business day (allowing for weekends)
        reasonable_gaps = (
            (date_diffs >= timedelta(days=1)) & 
            (date_diffs <= timedelta(days=4))
        ).mean()
        
        return reasonable_gaps
    
    def validate_symbol_data(
        self,
        data: pd.DataFrame,
        symbol: str,
        min_records: int = 100
    ) -> Tuple[bool, List[str]]:
        """Validate data for a specific symbol.
        
        Args:
            data: DataFrame with symbol data
            symbol: Stock symbol
            min_records: Minimum number of records required
            
        Returns:
            Tuple of (is_valid, list_of_issues)
        """
        issues = []
        
        # Check minimum records
        if len(data) < min_records:
            issues.append(f"Insufficient data: {len(data)} < {min_records} records")
        
        # Check required columns
        required_columns = ['Date', 'open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in data.columns]
        if missing_columns:
            issues.append(f"Missing columns: {missing_columns}")
        
        # Check data quality score
        quality_score = self.validate_data_quality(data, symbol)
        min_quality = self.quality_thresholds.get('min_quality_score', 0.7)
        
        if quality_score < min_quality:
            issues.append(f"Low data quality: {quality_score:.3f} < {min_quality}")
        
        # Check for recent data
        if 'Date' in data.columns:
            latest_date = pd.to_datetime(data['Date']).max()
            days_old = (datetime.now() - latest_date).days
            
            if days_old > 7:  # Data older than a week
                issues.append(f"Stale data: latest date is {days_old} days old")
        
        is_valid = len(issues) == 0
        
        if not is_valid:
            self.logger.warning(f"Data validation failed for {symbol}: {issues}")
        
        return is_valid, issues
    
    def clean_data(self, data: pd.DataFrame, symbol: str) -> pd.DataFrame:
        """Clean data by removing/fixing common issues.
        
        Args:
            data: DataFrame to clean
            symbol: Stock symbol for logging
            
        Returns:
            Cleaned DataFrame
        """
        if data.empty:
            return data
        
        original_length = len(data)
        cleaned_data = data.copy()
        
        # Remove rows with invalid OHLCV relationships
        if all(col in cleaned_data.columns for col in ['open', 'high', 'low', 'close']):
            # Remove rows where High < Low
            invalid_hl = cleaned_data['high'] < cleaned_data['low']
            cleaned_data = cleaned_data[~invalid_hl]
            
            # Remove rows where High < Open or High < Close
            invalid_high = (
                (cleaned_data['high'] < cleaned_data['open']) |
                (cleaned_data['high'] < cleaned_data['close'])
            )
            cleaned_data = cleaned_data[~invalid_high]
            
            # Remove rows where Low > Open or Low > Close
            invalid_low = (
                (cleaned_data['low'] > cleaned_data['open']) |
                (cleaned_data['low'] > cleaned_data['close'])
            )
            cleaned_data = cleaned_data[~invalid_low]
        
        # Remove rows with non-positive prices
        price_columns = ['open', 'high', 'low', 'close']
        for col in price_columns:
            if col in cleaned_data.columns:
                cleaned_data = cleaned_data[cleaned_data[col] > 0]
        
        # Remove rows with negative volume
        if 'volume' in cleaned_data.columns:
            cleaned_data = cleaned_data[cleaned_data['volume'] >= 0]
        
        # Remove extreme outliers (returns > 50% in a day)
        if 'close' in cleaned_data.columns and len(cleaned_data) > 1:
            returns = cleaned_data['close'].pct_change().abs()
            extreme_returns = returns > 0.5
            cleaned_data = cleaned_data[~extreme_returns]
        
        # Remove duplicate dates
        if 'Date' in cleaned_data.columns:
            cleaned_data = cleaned_data.drop_duplicates(subset=['Date'], keep='first')
        
        removed_rows = original_length - len(cleaned_data)
        if removed_rows > 0:
            self.logger.info(
                f"Cleaned {symbol} data: removed {removed_rows} invalid rows "
                f"({removed_rows/original_length*100:.1f}%)"
            )
        
        return cleaned_data.reset_index(drop=True)
    
    def get_data_summary(self, data: pd.DataFrame, symbol: str) -> Dict[str, Any]:
        """Get comprehensive data summary.
        
        Args:
            data: DataFrame to summarize
            symbol: Stock symbol
            
        Returns:
            Dictionary with data summary
        """
        if data.empty:
            return {'symbol': symbol, 'empty': True}
        
        summary = {
            'symbol': symbol,
            'empty': False,
            'records': len(data),
            'columns': list(data.columns),
            'date_range': {},
            'quality_score': self.validate_data_quality(data, symbol),
            'missing_data': data.isna().sum().to_dict(),
            'data_types': data.dtypes.astype(str).to_dict()
        }
        
        # Date range information
        if 'Date' in data.columns:
            dates = pd.to_datetime(data['Date'])
            summary['date_range'] = {
                'start': dates.min().strftime('%Y-%m-%d'),
                'end': dates.max().strftime('%Y-%m-%d'),
                'days': (dates.max() - dates.min()).days,
                'business_days': len(pd.bdate_range(dates.min(), dates.max()))
            }
        
        # Price statistics
        if 'close' in data.columns:
            close_prices = data['close']
            summary['price_stats'] = {
                'min': float(close_prices.min()),
                'max': float(close_prices.max()),
                'mean': float(close_prices.mean()),
                'std': float(close_prices.std()),
                'latest': float(close_prices.iloc[-1]) if len(close_prices) > 0 else None
            }
        
        # Volume statistics
        if 'volume' in data.columns:
            volume = data['volume']
            summary['volume_stats'] = {
                'min': int(volume.min()),
                'max': int(volume.max()),
                'mean': float(volume.mean()),
                'zero_volume_days': int((volume == 0).sum())
            }
        
        return summary