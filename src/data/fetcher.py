"""Data fetcher module for market data acquisition using yfinance.

This module provides fast and reliable data fetching capabilities with:
- Primary data source: yfinance (fast, free, reliable)
- VIX data integration for volatility regime detection
- Smart caching with timestamp validation
- Comprehensive error handling and logging
- Data quality validation
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple, Union
from pathlib import Path
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime # Add datetime import
from typing import Union # Add Union for type hinting

from config import settings, TECH_STOCKS, VIX_SYMBOL, DATA_QUALITY
from utils import (
    get_logger, 
    log_performance, 
    log_data_quality, 
    log_error,
    log_error_with_context
)
from .cache import DataCache
from .validator import DataValidator


class DataFetcher:
    """High-performance data fetcher using yfinance with smart caching."""
    
    def __init__(self, cache_dir: Optional[str] = None):
        """Initialize the data fetcher.
        
        Args:
            cache_dir: Directory for data caching. Defaults to settings.data.cache_dir
        """
        self.logger = get_logger(__name__)
        self.cache_dir = Path(cache_dir or settings.data.cache_dir)
        self.cache = DataCache(self.cache_dir)
        self.validator = DataValidator()
        
        # Create cache directory if it doesn't exist
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger.info(f"DataFetcher initialized with cache dir: {self.cache_dir}")
    
    @log_performance
    def fetch_symbol_data(
        self,
        symbol: str,
        start_date: Union[str, datetime],
        end_date: Union[str, datetime],
        force_refresh: bool = False
    ) -> pd.DataFrame:
        """Fetch OHLCV data for a single symbol.
        
        Args:
            symbol: Stock symbol (e.g., 'AAPL')
            start_date: Start date in 'YYYY-MM-DD' format
            end_date: End date in 'YYYY-MM-DD' format
            force_refresh: Force refresh cached data
            
        Returns:
            DataFrame with OHLCV data and symbol column
            
        Raises:
            ValueError: If data fetch fails or data quality is poor
        """
        # Convert string dates to datetime objects if they are strings
        if isinstance(start_date, str):
            start_date_dt = datetime.strptime(start_date, '%Y-%m-%d')
            start_date_str = start_date
        else:
            start_date_dt = start_date
            start_date_str = start_date.strftime('%Y-%m-%d')

        if isinstance(end_date, str):
            end_date_dt = datetime.strptime(end_date, '%Y-%m-%d')
            end_date_str = end_date
        else:
            end_date_dt = end_date
            end_date_str = end_date.strftime('%Y-%m-%d')

        try:
            # Check cache first
            if not force_refresh:
                cached_data = self.cache.get_cached_data(symbol, start_date_str, end_date_str)
                if cached_data is not None:
                    self.logger.info(f"Using cached data for {symbol}")
                    return cached_data
            
            self.logger.info(f"Fetching data for {symbol} from {start_date_str} to {end_date_str}")
            
            # Fetch data using yfinance
            ticker = yf.Ticker(symbol)
            data = ticker.history(
                start=start_date_dt, # Use datetime object
                end=end_date_dt,   # Use datetime object
                interval=settings.data.interval,
                auto_adjust=True,
                prepost=False
            )
            
            if data.empty:
                raise ValueError(f"No data returned for {symbol}")
            
            # Clean and validate data
            data = self._clean_data(data, symbol)
            
            # Add symbol column
            data['symbol'] = symbol
            
            # Validate data quality
            quality_score = self.validator.validate_data_quality(data, symbol)
            log_data_quality(symbol, len(data), quality_score)
            
            # The quality score is a ratio (0 to 1), so we compare it to max_missing_ratio
            # A lower quality score means more missing data, so we should compare it to the max allowed missing ratio.
            # The validator calculates quality as 1 - missing_ratio, so lower quality means higher missing ratio.
            # The threshold should be max_missing_ratio, and we fail if quality_score < (1 - max_missing_ratio)
            # However, the error message suggests it's expecting a 'min_quality_score' key.
            # Let's assume there should be a 'min_quality_score' in DATA_QUALITY for now,
            # and the validator's quality_score is meant to be compared against it.
            # If the validator's quality_score is actually the missing ratio, this logic needs adjustment.
            # Based on the error and the variable name `quality_score`, it's more likely
            # `validate_data_quality` returns a score where higher is better.
            # Let's add a 'min_quality_score' to DATA_QUALITY in constants.py.
            # For now, I will fix the variable name reference in fetcher.py.
            if quality_score < DATA_QUALITY['min_quality_score']:
                raise ValueError(f"Data quality too low for {symbol}: {quality_score:.2f}")
            
            # Cache the data
            self.cache.cache_data(data, symbol, start_date_str, end_date_str)
            
            self.logger.success(f"Successfully fetched {len(data)} records for {symbol}")
            return data
            
        except Exception as e:
            log_error_with_context(
                f"Failed to fetch data for {symbol}",
                e,
                {
                    "symbol": symbol,
                    "start_date": start_date,
                    "end_date": end_date
                }
            )
            raise
    
    @log_performance
    def fetch_multiple_symbols(
        self,
        symbols: List[str],
        start_date: str,
        end_date: str,
        force_refresh: bool = False,
        max_workers: int = 5
    ) -> pd.DataFrame:
        """Fetch data for multiple symbols concurrently.
        
        Args:
            symbols: List of stock symbols
            start_date: Start date in 'YYYY-MM-DD' format
            end_date: End date in 'YYYY-MM-DD' format
            force_refresh: Force refresh cached data
            max_workers: Maximum number of concurrent workers
            
        Returns:
            Combined DataFrame with all symbols' data
        """
        self.logger.info(f"Fetching data for {len(symbols)} symbols: {symbols}")
        
        all_data = []
        failed_symbols = []
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all fetch tasks
            future_to_symbol = {
                executor.submit(
                    self.fetch_symbol_data, symbol, start_date, end_date, force_refresh
                ): symbol
                for symbol in symbols
            }
            
            # Collect results
            for future in as_completed(future_to_symbol):
                symbol = future_to_symbol[future]
                try:
                    data = future.result()
                    all_data.append(data)
                except Exception as e:
                    failed_symbols.append(symbol)
                    self.logger.error(f"Failed to fetch {symbol}: {e}")
        
        if failed_symbols:
            self.logger.warning(f"Failed to fetch data for: {failed_symbols}")
        
        if not all_data:
            raise ValueError("No data was successfully fetched for any symbol")
        
        # Combine all data
        combined_data = pd.concat(all_data, ignore_index=True)
        combined_data = combined_data.sort_values(['symbol', 'Date']).reset_index(drop=True)
        
        self.logger.success(
            f"Successfully fetched data for {len(all_data)} symbols, "
            f"total records: {len(combined_data)}"
        )
        
        return combined_data
    
    @log_performance
    def fetch_vix_data(
        self,
        start_date: Union[str, datetime],
        end_date: Union[str, datetime],
        force_refresh: bool = False
    ) -> pd.DataFrame:
        """Fetch VIX data for volatility regime detection.
        
        Args:
            start_date: Start date in 'YYYY-MM-DD' format
            end_date: End date in 'YYYY-MM-DD' format
            force_refresh: Force refresh cached data
            
        Returns:
            DataFrame with VIX data
        """
        # Convert string dates to datetime objects if they are strings
        if isinstance(start_date, str):
            start_date_dt = datetime.strptime(start_date, '%Y-%m-%d')
            start_date_str = start_date
        else:
            start_date_dt = start_date
            start_date_str = start_date.strftime('%Y-%m-%d')

        if isinstance(end_date, str):
            end_date_dt = datetime.strptime(end_date, '%Y-%m-%d')
            end_date_str = end_date
        else:
            end_date_dt = end_date
            end_date_str = end_date.strftime('%Y-%m-%d')

        self.logger.info(f"Fetching VIX data from {start_date_str} to {end_date_str}")
        
        try:
            # Check cache first
            if not force_refresh:
                cached_data = self.cache.get_cached_data(VIX_SYMBOL, start_date_str, end_date_str)
                if cached_data is not None:
                    self.logger.info("Using cached VIX data")
                    return cached_data
            
            # Fetch VIX data
            vix_ticker = yf.Ticker(VIX_SYMBOL)
            vix_data = vix_ticker.history(
                start=start_date_dt, # Use datetime object
                end=end_date_dt,   # Use datetime object
                interval=settings.data.interval,
                auto_adjust=True
            )
            
            if vix_data.empty:
                raise ValueError("No VIX data returned")
            
            # Clean data
            vix_data = self._clean_data(vix_data, VIX_SYMBOL)
            vix_data['symbol'] = VIX_SYMBOL
            
            # Cache the data
            self.cache.cache_data(vix_data, VIX_SYMBOL, start_date_str, end_date_str)
            
            self.logger.success(f"Successfully fetched {len(vix_data)} VIX records")
            return vix_data
            
        except Exception as e:
            log_error("Failed to fetch VIX data", e)
            raise
    
    def fetch_all_data(
        self,
        symbols: Optional[List[str]] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        include_vix: bool = True,
        force_refresh: bool = False
    ) -> Tuple[pd.DataFrame, Optional[pd.DataFrame]]:
        """Fetch all required data for trading.
        
        Args:
            symbols: List of symbols to fetch. Defaults to TECH_STOCKS
            start_date: Start date. Defaults to settings.data.start_date
            end_date: End date. Defaults to settings.data.end_date
            include_vix: Whether to include VIX data
            force_refresh: Force refresh cached data
            
        Returns:
            Tuple of (stock_data, vix_data)
        """
        symbols = symbols or settings.data.symbols or TECH_STOCKS
        start_date = start_date or settings.data.start_date
        end_date = end_date or settings.data.end_date
        
        self.logger.info(
            f"Fetching all data: {len(symbols)} symbols from {start_date} to {end_date}"
        )
        
        # Fetch stock data
        stock_data = self.fetch_multiple_symbols(
            symbols, start_date, end_date, force_refresh
        )
        
        # Fetch VIX data if requested
        vix_data = None
        if include_vix:
            try:
                vix_data = self.fetch_vix_data(start_date, end_date, force_refresh)
            except Exception as e:
                self.logger.warning(f"Failed to fetch VIX data: {e}")
        
        return stock_data, vix_data
    
    def _clean_data(self, data: pd.DataFrame, symbol: str) -> pd.DataFrame:
        """Clean and prepare raw market data.
        
        Args:
            data: Raw OHLCV data
            symbol: Stock symbol for logging
            
        Returns:
            Cleaned DataFrame
        """
        # Reset index to make Date a column
        if 'Date' not in data.columns:
            data = data.reset_index()
        
        # Ensure Date column is datetime
        if 'Date' in data.columns:
            data['Date'] = pd.to_datetime(data['Date'])
        
        # Remove any rows with all NaN values
        data = data.dropna(how='all')
        
        # Forward fill missing values (limited)
        numeric_columns = data.select_dtypes(include=[np.number]).columns
        data[numeric_columns] = data[numeric_columns].ffill(limit=3)
        
        # Remove rows with remaining NaN values in critical columns
        critical_columns = ['open', 'high', 'low', 'close', 'volume']
        available_critical = [col for col in critical_columns if col in data.columns]
        data = data.dropna(subset=available_critical)
        
        # Ensure positive prices and volumes
        price_columns = ['open', 'high', 'low', 'close']
        for col in price_columns:
            if col in data.columns:
                data = data[data[col] > 0]
        
        if 'volume' in data.columns:
            data = data[data['volume'] >= 0]
        
        # Sort by date
        if 'Date' in data.columns:
            data = data.sort_values('Date').reset_index(drop=True)
        
        # Standardize column names to lowercase
        data.columns = data.columns.str.lower()
        
        self.logger.debug(f"Cleaned {symbol} data: {len(data)} records")
        return data
    
    def get_latest_data(
        self,
        symbols: Optional[List[str]] = None,
        days: int = 30
    ) -> pd.DataFrame:
        """Get latest data for symbols.
        
        Args:
            symbols: List of symbols. Defaults to TECH_STOCKS
            days: Number of days of recent data
            
        Returns:
            DataFrame with latest data
        """
        symbols = symbols or TECH_STOCKS
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
        
        return self.fetch_multiple_symbols(symbols, start_date, end_date)
    
    def check_market_hours(self) -> bool:
        """Check if market is currently open.
        
        Returns:
            True if market is open, False otherwise
        """
        now = datetime.now()
        
        # Simple check for US market hours (9:30 AM - 4:00 PM ET, Mon-Fri)
        # Note: This doesn't account for holidays
        if now.weekday() >= 5:  # Weekend
            return False
        
        market_open = now.replace(hour=9, minute=30, second=0, microsecond=0)
        market_close = now.replace(hour=16, minute=0, second=0, microsecond=0)
        
        return market_open <= now <= market_close
    
    def get_data_info(self, symbol: str) -> Dict:
        """Get information about cached data for a symbol.
        
        Args:
            symbol: Stock symbol
            
        Returns:
            Dictionary with data information
        """
        return self.cache.get_data_info(symbol)
    
    def clear_cache(self, symbol: Optional[str] = None) -> None:
        """Clear cached data.
        
        Args:
            symbol: Specific symbol to clear. If None, clears all cache
        """
        self.cache.clear_cache(symbol)
        self.logger.info(f"Cleared cache for {symbol or 'all symbols'}")