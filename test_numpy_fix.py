#!/usr/bin/env python3
"""
Test script to verify the numpy string error fix in AsymmetricTradingEnv.

This script tests that the environment can be initialized with string parameters
without throwing the numpy string multiplication error.
"""

import sys
import os
sys.path.append('src')

import pandas as pd
import numpy as np
from trading.asymmetric_env import AsymmetricTradingEnv

def test_string_parameters():
    """Test that string parameters are properly converted to numeric types."""
    print("Testing AsymmetricTradingEnv with string parameters...")
    
    # Create minimal test data
    test_data = {
        'date': ['2023-01-01', '2023-01-02'],
        'tic': ['AAPL', 'AAPL'],
        'close': [150.0, 151.0],
        'open': [149.0, 150.5],
        'high': [152.0, 153.0],
        'low': [148.0, 149.5],
        'volume': [1000000, 1100000],
        'turbulence': [0.1, 0.2]
    }
    
    # Add some basic technical indicators
    for indicator in ['sma_5', 'sma_10', 'rsi_14', 'macd_12_26_9']:
        test_data[indicator] = [0.0, 0.1]
    
    df = pd.DataFrame(test_data)
    
    # Test with string parameters (this should now work)
    try:
        env = AsymmetricTradingEnv(
            df=df,
            stock_dim='1',  # String instead of int
            hmax='100',     # String instead of int  
            initial_amount='10000',  # String instead of int
            num_stock_shares=['0'],  # List of strings instead of ints
            buy_cost_pct=['0.001'], # List of strings instead of floats
            sell_cost_pct=['0.001'], # List of strings instead of floats
            reward_scaling='1e-4'    # String instead of float
        )
        print('✓ SUCCESS: Environment initialized with string parameters')
        print(f'✓ Environment state space: {env.observation_space.shape}')
        print(f'✓ Environment action space: {env.action_space.shape}')
        return True
    except Exception as e:
        print(f'✗ ERROR: {e}')
        import traceback
        traceback.print_exc()
        return False

def test_numeric_parameters():
    """Test that numeric parameters still work correctly."""
    print("\nTesting AsymmetricTradingEnv with numeric parameters...")
    
    # Create minimal test data
    test_data = {
        'date': ['2023-01-01', '2023-01-02'],
        'tic': ['AAPL', 'AAPL'],
        'close': [150.0, 151.0],
        'open': [149.0, 150.5],
        'high': [152.0, 153.0],
        'low': [148.0, 149.5],
        'volume': [1000000, 1100000],
        'turbulence': [0.1, 0.2]
    }
    
    # Add some basic technical indicators
    for indicator in ['sma_5', 'sma_10', 'rsi_14', 'macd_12_26_9']:
        test_data[indicator] = [0.0, 0.1]
    
    df = pd.DataFrame(test_data)
    
    # Test with numeric parameters
    try:
        env = AsymmetricTradingEnv(
            df=df,
            stock_dim=1,  # int
            hmax=100,     # int  
            initial_amount=10000,  # int
            num_stock_shares=[0],  # List of ints
            buy_cost_pct=[0.001], # List of floats
            sell_cost_pct=[0.001], # List of floats
            reward_scaling=1e-4    # float
        )
        print('✓ SUCCESS: Environment initialized with numeric parameters')
        print(f'✓ Environment state space: {env.observation_space.shape}')
        print(f'✓ Environment action space: {env.action_space.shape}')
        return True
    except Exception as e:
        print(f'✗ ERROR: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("Testing AsymmetricTradingEnv numpy string error fix")
    print("=" * 60)
    
    test1_passed = test_string_parameters()
    test2_passed = test_numeric_parameters()
    
    print("\n" + "=" * 60)
    print("SUMMARY:")
    print(f"String parameters test: {'PASSED' if test1_passed else 'FAILED'}")
    print(f"Numeric parameters test: {'PASSED' if test2_passed else 'FAILED'}")
    
    if test1_passed and test2_passed:
        print("✓ ALL TESTS PASSED - The numpy string error fix is working correctly!")
        sys.exit(0)
    else:
        print("✗ SOME TESTS FAILED - There are still issues to fix")
        sys.exit(1)
