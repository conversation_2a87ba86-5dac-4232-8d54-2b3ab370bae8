import numpy as np
import pandas as pd
import yfinance as yf
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# ElegantRL and FinRL imports
from finrl import config
from finrl.meta.env_stock_trading.env_stocktrading import StockTradingEnv
#from finrl.agent.elegantrl_models import DRLAgent
from elegantrl.agents import AgentSAC
from finrl.train import train
from finrl.test import test
from finrl.plot import backtest_stats, backtest_plot

class AsymmetricTradingBot:
    def __init__(self):
        self.TECH_TICKERS = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'META', 'NVDA', 'TSLA', 'AVGO', 'ADBE', 'ASML']
        self.start_date = '2020-01-01'
        self.end_date = '2024-01-01'
        self.initial_amount = 100000
        
        # Technical indicators for asymmetric strategy
        self.indicators = ['macd', 'rsi_30', 'cci_30', 'dx_30', 'bbands_upper', 'bbands_lower', 'vma', 'volatility']
        
    def fetch_data(self):
        """Fetch and prepare data for training"""
        data_list = []
        
        for ticker in self.TECH_TICKERS:
            try:
                stock_data = yf.download(ticker, start=self.start_date, end=self.end_date, progress=False)
                stock_data['tic'] = ticker
                stock_data['date'] = stock_data.index
                stock_data = stock_data.reset_index(drop=True)
                
                # Rename columns to match FinRL format
                stock_data.columns = ['open', 'high', 'low', 'close', 'adjcp', 'volume', 'tic', 'date']
                stock_data = stock_data[['date', 'open', 'high', 'low', 'close', 'adjcp', 'volume', 'tic']]
                
                data_list.append(stock_data)
            except Exception as e:
                print(f"Error fetching {ticker}: {e}")
                
        df = pd.concat(data_list, ignore_index=True)
        df = df.sort_values(['date', 'tic']).reset_index(drop=True)
        return df
    
    def add_technical_indicators(self, df):
        """Add technical indicators for asymmetric strategy"""
        from finrl.finrl_meta.preprocessor.preprocessors import FeatureEngineer
        
        fe = FeatureEngineer(
            use_technical_indicator=True,
            tech_indicator_list=self.indicators,
            use_vix=True,
            use_turbulence=True,
            user_defined_feature=False
        )
        
        processed_df = fe.preprocess_data(df)
        
        # Add custom asymmetric indicators
        processed_df = self.add_asymmetric_features(processed_df)
        
        return processed_df
    
    def add_asymmetric_features(self, df):
        """Add features specifically for asymmetric return profile"""
        df_copy = df.copy()
        
        for ticker in self.TECH_TICKERS:
            ticker_data = df_copy[df_copy.tic == ticker].copy()
            
            # Market regime detection
            ticker_data['sma_20'] = ticker_data['close'].rolling(20).mean()
            ticker_data['sma_50'] = ticker_data['close'].rolling(50).mean()
            ticker_data['market_regime'] = np.where(ticker_data['sma_20'] > ticker_data['sma_50'], 1, -1)
            
            # Downside protection indicator
            ticker_data['max_drawdown_20'] = ticker_data['close'].rolling(20).apply(
                lambda x: (x.iloc[-1] - x.max()) / x.max()
            )
            
            # Volatility clustering
            ticker_data['volatility_regime'] = np.where(ticker_data['volatility'] > ticker_data['volatility'].rolling(20).mean(), 1, 0)
            
            # Fear index (combining multiple signals)
            ticker_data['fear_index'] = (
                (ticker_data['rsi_30'] < 30).astype(int) +
                (ticker_data['market_regime'] == -1).astype(int) +
                (ticker_data['max_drawdown_20'] < -0.1).astype(int) +
                (ticker_data['volatility_regime'] == 1).astype(int)
            ) / 4
            
            # Update main dataframe
            df_copy.loc[df_copy.tic == ticker, 'market_regime'] = ticker_data['market_regime']
            df_copy.loc[df_copy.tic == ticker, 'max_drawdown_20'] = ticker_data['max_drawdown_20']
            df_copy.loc[df_copy.tic == ticker, 'volatility_regime'] = ticker_data['volatility_regime']
            df_copy.loc[df_copy.tic == ticker, 'fear_index'] = ticker_data['fear_index']
        
        return df_copy.fillna(0)
    
    def create_custom_reward(self):
        """Custom reward function for asymmetric returns"""
        def asymmetric_reward(actions, market_returns, portfolio_return, market_volatility):
            """
            Reward function that prioritizes:
            1. Positive returns during market upturns
            2. Capital preservation during downturns
            3. Lower volatility overall
            """
            # Market regime detection
            market_up = market_returns > 0
            market_down = market_returns <= 0
            
            # Base reward
            reward = portfolio_return
            
            # Asymmetric bonus/penalty
            if market_up:
                # Reward capturing upside
                reward += max(0, portfolio_return) * 2
            else:
                # Penalize downside more heavily
                reward -= max(0, -portfolio_return) * 3
                # Bonus for capital preservation during downturns
                if portfolio_return >= -0.01:  # Less than 1% loss
                    reward += 0.5
            
            # Volatility penalty
            reward -= market_volatility * 0.1
            
            return reward
        
        return asymmetric_reward
    
    def setup_environment(self, df):
        """Setup trading environment with asymmetric reward"""
        # Split data
        train_end = int(len(df) * 0.7)
        val_end = int(len(df) * 0.85)
        
        train_df = df[:train_end]
        val_df = df[train_end:val_end]
        test_df = df[val_end:]
        
        # Environment parameters for asymmetric strategy
        env_kwargs = {
            "hmax": 100,  # Maximum holdings per stock
            "initial_amount": self.initial_amount,
            "num_stock_shares": [0] * len(self.TECH_TICKERS),
            "buy_cost_pct": [0.001] * len(self.TECH_TICKERS),  # 0.1% transaction cost
            "sell_cost_pct": [0.001] * len(self.TECH_TICKERS),
            "reward_scaling": 1e-4,
            "state_space": len(train_df.columns) - 2,  # Exclude 'date' and 'tic'
            "action_space": len(self.TECH_TICKERS),
            "tech_indicator_list": self.indicators + ['market_regime', 'max_drawdown_20', 'volatility_regime', 'fear_index'],
            "print_verbosity": 1
        }
        
        return train_df, val_df, test_df, env_kwargs
    
    def train_sac_agent(self, train_df, env_kwargs):
        """Train SAC agent with ElegantRL"""
        # SAC hyperparameters optimized for asymmetric strategy
        sac_params = {
            'learning_rate': 0.0001,
            'batch_size': 256,
            'buffer_size': 100000,
            'tau': 0.005,
            'gamma': 0.99,
            'train_every': 1,
            'gradient_steps': 1,
            'ent_coef': 'auto',
            'target_update_interval': 1,
            'policy_kwargs': {'net_arch': [512, 512, 256]},  # Larger network for complex patterns
        }
        
        # Create environment
        env_train = StockTradingEnv(df=train_df, **env_kwargs)
        
        # Initialize DRL agent
        agent = DRLAgent(env=env_train)
        
        # Train SAC model
        model_sac = agent.get_model("sac", model_kwargs=sac_params)
        
        # Training parameters
        trained_sac = agent.train_model(
            model=model_sac,
            tb_log_name='sac_asymmetric',
            total_timesteps=80000  # Increased for better convergence
        )
        
        return trained_sac
    
    def backtest_strategy(self, model, test_df, env_kwargs):
        """Backtest the asymmetric strategy"""
        # Create test environment
        env_test = StockTradingEnv(df=test_df, **env_kwargs, mode="test")
        
        # Run backtest
        df_account_value, df_actions = DRLAgent.DRL_prediction(
            model=model,
            environment=env_test
        )
        
        return df_account_value, df_actions
    
    def analyze_asymmetry(self, account_values, market_returns):
        """Analyze the asymmetric return profile"""
        portfolio_returns = account_values.pct_change().dropna()
        
        # Separate bull and bear periods
        bull_periods = market_returns > 0
        bear_periods = market_returns <= 0
        
        bull_returns = portfolio_returns[bull_periods]
        bear_returns = portfolio_returns[bear_periods]
        
        # Calculate asymmetric metrics
        asymmetry_metrics = {
            'Bull Market Avg Return': bull_returns.mean(),
            'Bear Market Avg Return': bear_returns.mean(),
            'Bull Market Volatility': bull_returns.std(),
            'Bear Market Volatility': bear_returns.std(),
            'Asymmetry Ratio': bull_returns.mean() / abs(bear_returns.mean()) if bear_returns.mean() != 0 else float('inf'),
            'Downside Capture': bear_returns.mean() / market_returns[bear_periods].mean(),
            'Upside Capture': bull_returns.mean() / market_returns[bull_periods].mean(),
            'Max Drawdown': (account_values / account_values.expanding().max() - 1).min(),
            'Sharpe Ratio': portfolio_returns.mean() / portfolio_returns.std() * np.sqrt(252)
        }
        
        return asymmetry_metrics
    
    def run_complete_strategy(self):
        """Run the complete asymmetric trading strategy"""
        print("🚀 Starting Asymmetric Return Profile Trading Bot")
        print(f"📊 Trading stocks: {', '.join(self.TECH_TICKERS)}")
        
        # 1. Fetch and prepare data
        print("\n📈 Fetching market data...")
        df = self.fetch_data()
        
        # 2. Add technical indicators
        print("🔧 Adding technical indicators...")
        df_processed = self.add_technical_indicators(df)
        
        # 3. Setup environment
        print("🏗️ Setting up trading environment...")
        train_df, val_df, test_df, env_kwargs = self.setup_environment(df_processed)
        
        # 4. Train SAC agent
        print("🤖 Training SAC agent...")
        trained_model = self.train_sac_agent(train_df, env_kwargs)
        
        # 5. Backtest
        print("📊 Running backtest...")
        account_values, actions = self.backtest_strategy(trained_model, test_df, env_kwargs)
        
        # 6. Analyze results
        print("📈 Analyzing asymmetric performance...")
        
        # Calculate market benchmark (equal weight tech stocks)
        market_data = test_df.groupby('date')['close'].mean().pct_change().dropna()
        asymmetry_metrics = self.analyze_asymmetry(account_values['account_value'], market_data)
        
        # Print results
        print("\n" + "="*50)
        print("🎯 ASYMMETRIC STRATEGY RESULTS")
        print("="*50)
        
        for metric, value in asymmetry_metrics.items():
            if isinstance(value, float):
                print(f"{metric}: {value:.4f}")
            else:
                print(f"{metric}: {value}")
        
        print(f"\n💰 Final Portfolio Value: ${account_values['account_value'].iloc[-1]:,.2f}")
        print(f"📈 Total Return: {(account_values['account_value'].iloc[-1] / self.initial_amount - 1) * 100:.2f}%")
        
        return trained_model, account_values, actions, asymmetry_metrics

# Usage example
if __name__ == "__main__":
    # Initialize and run the asymmetric trading bot
    bot = AsymmetricTradingBot()
    
    try:
        model, results, actions, metrics = bot.run_complete_strategy()
        print("\n✅ Asymmetric trading strategy completed successfully!")
        
        # Additional analysis can be added here
        print("\n📋 Strategy successfully optimized for:")
        print("  • Flat returns during market downturns")
        print("  • Positive returns during market upturns") 
        print("  • Lower overall portfolio volatility")
        
    except Exception as e:
        print(f"❌ Error running strategy: {e}")
        print("💡 Make sure you have all required packages installed:")
        print("   pip install finrl elegantrl yfinance")
        