# System Patterns - FinRL Trading Agent

## Architecture Overview

### High-Level Design
```
finrl-bot3/
├── main.py                 # CLI entry point
├── config/
│   ├── __init__.py
│   ├── settings.py         # Centralized configuration
│   └── constants.py        # Static constants
├── src/
│   ├── __init__.py
│   ├── data/
│   │   ├── __init__.py
│   │   ├── fetcher.py      # Data acquisition
│   │   ├── processor.py    # Data preprocessing
│   │   └── cache.py        # Caching mechanism
│   ├── models/
│   │   ├── __init__.py
│   │   ├── sac_agent.py    # SAC implementation
│   │   └── environment.py  # Trading environment
│   ├── strategies/
│   │   ├── __init__.py
│   │   └── asymmetric.py   # Asymmetric return strategy
│   ├── trading/
│   │   ├── __init__.py
│   │   ├── alpaca_client.py # Alpaca integration
│   │   └── executor.py     # Trade execution
│   ├── backtesting/
│   │   ├── __init__.py
│   │   ├── engine.py       # Backtesting framework
│   │   └── metrics.py      # Performance metrics
│   └── utils/
│       ├── __init__.py
│       ├── logging.py      # Logging utilities
│       └── helpers.py      # Common utilities
├── data/
│   └── cache/              # CSV cache files
├── models/
│   ├── checkpoints/        # Training artifacts and intermediate models
│   └── saved/              # Production-ready, validated model artifacts
├── results/
│   ├── backtests/          # Backtest results
│   └── reports/            # Performance reports
├── tests/
├── requirements.txt
├── environment.yml         # Conda environment
├── .env.example
└── README.md
```

## Core Design Patterns

### 1. Command Pattern
**Implementation**: CLI interface with distinct commands
```python
# main.py structure
class CommandHandler:
    def execute_get_data(self)
    def execute_process_data(self)
    def execute_tune(self)
    def execute_train(self)
    def execute_backtest(self)
    def execute_papertrade(self)
```

### 2. Strategy Pattern
**Implementation**: Pluggable trading strategies
```python
class TradingStrategy(ABC):
    @abstractmethod
    def generate_signals(self, state) -> Dict[str, float]
    
class AsymmetricStrategy(TradingStrategy):
    def generate_signals(self, state) -> Dict[str, float]
```

### 3. Factory Pattern
**Implementation**: Environment and model creation
```python
class EnvironmentFactory:
    @staticmethod
    def create_trading_env(config: Config) -> TradingEnvironment
    
class ModelFactory:
    @staticmethod
    def create_sac_agent(env_config: Dict) -> SACAgent
```

### 4. Observer Pattern
**Implementation**: Event-driven logging and monitoring
```python
class TradingEventObserver(ABC):
    @abstractmethod
    def on_trade_executed(self, trade_info: Dict)
    
class LoggingObserver(TradingEventObserver):
    def on_trade_executed(self, trade_info: Dict)
```

### 5. Singleton Pattern
**Implementation**: Configuration and cache management
```python
class ConfigManager:
    _instance = None
    
class CacheManager:
    _instance = None
```

### 6. Model Management Pattern
**Implementation**: Separation of training artifacts and production models
```python
class ModelManager:
    def save_model(self, agent, model_name: str, metadata: Dict)
    def load_model(self, model_name: str) -> Tuple[Agent, Dict]
    def promote_model(self, checkpoint_path: str, model_name: str)
    
class ModelRegistry:
    def register_model(self, model_path: str, metadata: ModelMetadata)
    def get_model_metadata(self, model_name: str) -> ModelMetadata
    def list_models(self) -> List[str]
```

**Directory Structure**:
- `models/checkpoints/`: Training artifacts, intermediate saves, recovery points
- `models/saved/`: Production-ready models with metadata and versioning

**Workflow**:
1. Training saves models to `checkpoints/` directory
2. Backtesting validates model performance
3. Successful models promoted to `saved/` directory via `ModelManager.save_model()`
4. Production systems load from `saved/` directory only

## Component Relationships

### Data Flow Architecture
```
Alpaca API → DataFetcher → CacheManager → DataProcessor → TradingEnvironment
                                                              ↓
TradingExecutor ← SACAgent ← AsymmetricStrategy ← EnvironmentState
```

### Training Pipeline
```
HistoricalData → FeatureEngineering → TradingEnvironment → SACAgent → TrainedModel
                                            ↓
                                    HyperparameterTuning
```

### Backtesting Pipeline
```
TrainedModel → BacktestEngine → PerformanceMetrics → ReportGenerator
                    ↓
              TradingSimulator
```

## Key Technical Decisions

### 1. ElegantRL Integration
- **Rationale**: Superior SAC implementation for continuous action spaces
- **Implementation**: Custom wrapper for FinRL compatibility
- **Benefits**: Better convergence, more stable training
- **Dimension Handling**: ElegantRL's `build_env` function automatically corrects dimension mismatches
- **Critical Pattern**: ElegantRL prefers `env.state_space` over `env.observation_space.shape[0]`
- **Validation**: Diagnostic scripts confirm dimension consistency between SAC agent and ElegantRL

### 2. Asymmetric Return Implementation
- **Approach**: Dynamic position sizing based on market regime detection
- **Mechanism**: Volatility-adjusted allocation with downside protection
- **Formula**: `position_size = base_size * regime_multiplier * volatility_factor`

### 3. Caching Strategy
- **Format**: CSV files with timestamp-based invalidation
- **Structure**: Separate files per symbol and timeframe
- **Benefits**: Reduced API calls, faster development iteration

### 4. Environment Design
- **State Space**: OHLCV + Technical indicators + Market regime
- **Action Space**: Continuous allocation weights [-1, 1] per asset
- **Reward Function**: Risk-adjusted returns with asymmetric penalty

### 5. Configuration Management
- **Centralized**: Single settings.py file
- **Environment-aware**: Development/production configurations
- **Type-safe**: Pydantic models for validation

## Critical Implementation Paths

### 1. Data Pipeline
```python
# Critical path: Data consistency and quality
raw_data → validation → feature_engineering → normalization → environment_state
# Status: ✅ VALIDATED - Successfully processes 18,690 training, 4,680 validation records
```

### 2. Training Loop
```python
# Critical path: Model convergence
initialization → episode_loop → experience_collection → model_update → evaluation
# Status: ⚠️ HANGING - Fails silently during environment creation phase
```

### 3. Environment Creation (Critical Debug Path)
```python
# Critical path: Environment initialization debugging
data_preparation → nan_fill → type_checks → environment_creation → agent_initialization
# Status: 🔍 INVESTIGATING - Hangs after type_checks, before environment_creation
# Debug Points:
#   - AsymmetricTradingEnv.__init__() validation
#   - state_space and action_space calculation
#   - FinRL StockTradingEnv compatibility
#   - Resource usage during initialization
```

### 4. Trading Execution
```python
# Critical path: Real-time decision making
market_data → state_preparation → model_inference → signal_generation → order_execution
# Status: ⏳ PENDING - Awaiting successful training completion
```

## Error Handling Patterns

### 1. Graceful Degradation
- **Data Failures**: Use cached data with staleness warnings
- **API Failures**: Retry with exponential backoff
- **Model Failures**: Fallback to conservative strategy

### 2. Circuit Breaker
- **Implementation**: Stop trading on consecutive failures
- **Thresholds**: 3 consecutive API failures, 5% daily loss
- **Recovery**: Manual intervention required

### 3. Comprehensive Logging
- **Levels**: DEBUG, INFO, WARNING, ERROR, CRITICAL
- **Structured**: JSON format for machine parsing
- **Rotation**: Daily rotation with 30-day retention

### 4. Silent Failure Detection (Latest Pattern)
- **Problem**: Training processes hanging without error messages
- **Detection**: Timeout mechanisms and progress monitoring
- **Implementation**: Step-by-step validation with explicit logging
- **Recovery**: Detailed error reporting and graceful shutdown

### 5. Environment Creation Validation
- **Pattern**: Validate each step of environment initialization
- **Implementation**: Try-catch blocks around critical operations
- **Logging**: Debug-level logging for environment setup steps
- **Validation**: State space, action space, and data consistency checks

### 4. Dimension Mismatch Prevention
- **ElegantRL Integration**: Automatic dimension correction in `build_env` function
- **Environment Validation**: Ensure `env.state_space` equals `env.observation_space.shape[0]`
- **Diagnostic Tools**: Scripts to verify dimension consistency across components
- **Error Detection**: Clear logging when dimension corrections are applied
- **Prevention**: AsymmetricTradingEnv synchronizes both dimension values during initialization

## Performance Optimization

### 1. Vectorized Operations
- **NumPy/Pandas**: Batch processing for feature engineering
- **GPU Acceleration**: CUDA support for model training

### 2. Memory Management
- **Lazy Loading**: Load data on demand
- **Chunked Processing**: Process large datasets in chunks
- **Memory Monitoring**: Track and limit memory usage

### 3. Parallel Processing
- **Multiprocessing**: Parallel backtesting scenarios
- **Async I/O**: Non-blocking API calls
- **Thread Safety**: Proper synchronization for shared resources