# Tech Context - FinRL Trading Agent

## Technology Stack

### Core Technologies

#### Python 3.11
- **Rationale**: Latest stable version with performance improvements
- **Features Used**: Type hints, dataclasses, async/await, pattern matching
- **Compatibility**: Windows 11, Conda environment

#### FinRL
- **Source**: GitHub repository installation
- **Version**: Latest from main branch
- **Purpose**: Financial reinforcement learning framework
- **Key Components**: Environment wrappers, data processors, evaluation metrics

#### ElegantRL
- **Source**: GitHub repository installation
- **Version**: Latest from main branch
- **Purpose**: Advanced RL algorithms implementation
- **Key Components**: SAC agent, neural networks, training utilities
- **Advantage**: Superior performance over Stable-Baselines3 for continuous control

### Machine Learning Stack

#### SAC (Soft Actor-Critic)
- **Implementation**: ElegantRL's SAC agent
- **Architecture**: Actor-Critic with entropy regularization
- **Benefits**: Sample efficiency, stability in continuous action spaces
- **Hyperparameters**: Learning rates, network sizes, entropy coefficients

#### Neural Networks
- **Framework**: PyTorch (via ElegantRL)
- **Architecture**: Fully connected networks
- **Actor Network**: State → Action probabilities
- **Critic Networks**: State-Action → Q-values (twin networks)

### Data & APIs

#### Alpaca API
- **Purpose**: Market data and trade execution
- **Endpoints**: 
  - Market data: `/v2/stocks/bars`
  - Account info: `/v2/account`
  - Orders: `/v2/orders`
- **Authentication**: API key and secret in .env
- **Rate Limits**: 200 requests/minute

#### Data Sources
- **Primary**: yfinance (fast and reliable)
- **Secondary**: Alpaca market data API (for trading execution)
- **VIX Data**: Yahoo Finance VIX index for market volatility
- **Format**: OHLCV bars with 1-day frequency
- **Storage**: CSV cache files with timestamp validation

### Development Environment

#### Conda Environment
**⚠️ CRITICAL: Always activate environment before running any commands:**
```bash
conda activate finrl-trading-agent
```

**Environment Configuration:**
```yaml
name: finrl-trading-agent
channels:
  - conda-forge
  - pytorch
dependencies:
  - python=3.11
  - numpy>=1.24.0
  - pandas>=2.0.0
  - matplotlib>=3.7.0
  - seaborn>=0.12.0
  - jupyter>=1.0.0
  - pytest>=7.0.0
  - pip
  - pip:
    - alpaca-trade-api>=3.0.0
    - python-dotenv>=1.0.0
    - pydantic>=2.0.0
    - click>=8.0.0
    - loguru>=0.7.0
    - yfinance>=0.2.0
    - pandas-ta>=0.3.14b
    - optuna>=3.0.0
    - gym>=0.26.0
    - torch>=2.0.0
    - git+https://github.com/AI4Finance-Foundation/FinRL.git
    - git+https://github.com/AI4Finance-Foundation/ElegantRL.git
```

#### Development Tools
- **IDE**: VS Code with Python extension
- **Linting**: Black, flake8, mypy
- **Testing**: pytest, pytest-cov
- **Documentation**: Sphinx with autodoc
- **Version Control**: Git with conventional commits

#### Debugging Tools (Latest Additions)
- **Log Analysis**: Manual log file inspection for training issues
- **Dimension Validation**: Custom scripts for environment dimension checking
- **Process Monitoring**: Task manager for resource usage during training
- **Step-by-step Validation**: Incremental environment creation testing
- **Error Isolation**: Separate testing of environment components
- **Data Quality Checks**: Validation scripts for technical indicators and VIX data

### Dependencies Management

#### Core Dependencies
```python
# Data manipulation
numpy>=1.24.0
pandas>=2.0.0
yfinance>=0.2.0
pandas-ta>=0.3.14b

# Hyperparameter optimization
optuna>=3.0.0

# Logging
loguru>=0.7.0

# Machine learning
torch>=2.0.0
gym>=0.26.0

# Financial libraries
finrl @ git+https://github.com/AI4Finance-Foundation/FinRL.git
elegantrl @ git+https://github.com/AI4Finance-Foundation/ElegantRL.git
alpaca-trade-api>=3.0.0
yfinance>=0.2.0
ta>=0.10.0

# Utilities
python-dotenv>=1.0.0
click>=8.0.0
loguru>=0.7.0
pydantic>=2.0.0

# Visualization
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.0.0
```

#### Development Dependencies
```python
# Testing
pytest>=7.0.0
pytest-cov>=4.0.0
pytest-mock>=3.0.0

# Code quality
black>=23.0.0
flake8>=6.0.0
mypy>=1.0.0
isort>=5.0.0

# Documentation
sphinx>=6.0.0
sphinx-autodoc-typehints>=1.0.0
```

## Technical Constraints

### Platform Constraints
- **Operating System**: Windows 11
- **Python Version**: 3.11.x
- **Memory**: Minimum 8GB RAM for training
- **Storage**: 5GB for data cache and models

### API Constraints
- **Alpaca Rate Limits**: 200 requests/minute
- **Data Frequency**: Daily bars (no intraday)
- **Market Hours**: NYSE trading hours (9:30 AM - 4:00 PM ET)
- **Paper Trading**: Alpaca paper trading environment

### Model Constraints
- **Training Time**: Maximum 24 hours per training session
- **Memory Usage**: Maximum 4GB during training
- **Model Size**: Maximum 100MB saved model
- **Inference Time**: Maximum 1 second per decision

### Training Pipeline Constraints (Latest Findings)
- **Environment Creation**: Complex initialization causing silent failures
- **Data Quality**: "UNKNOWN" symbol errors in technical indicators
- **Feature Consistency**: Duplicate VIX columns causing confusion
- **Resource Usage**: Potential memory exhaustion during environment setup
- **Error Handling**: Insufficient logging for debugging silent failures
- **Validation**: Need step-by-step environment creation validation

## Configuration Management

### Environment Variables (.env)
```bash
# Alpaca API credentials
ALPACA_API_KEY=your_api_key_here
ALPACA_SECRET_KEY=your_secret_key_here
ALPACA_BASE_URL=https://paper-api.alpaca.markets  # Paper trading

# Logging Configuration
LOG_LEVEL=INFO
LOG_TO_CONSOLE=true
LOG_TO_FILE=true
LOG_FILE_PATH=./logs/trading_agent.log
LOG_ROTATION=10MB
LOG_RETENTION=30 days

# Application settings
CACHE_DIR=./data/cache
MODELS_DIR=./models/saved
RESULTS_DIR=./results
VIX_SYMBOL=^VIX

# Trading parameters
MAX_POSITION_SIZE=0.1
RISK_FREE_RATE=0.02
TRANSACTION_COST=0.001

# Optuna Tuning
OPTUNA_N_TRIALS=100
OPTUNA_TIMEOUT=3600
OPTUNA_STUDY_NAME=sac_trading_optimization
```

### Configuration Schema
```python
from pydantic import BaseSettings, Field
from typing import List, Optional, Dict, Any
from pathlib import Path

class LoggingConfig(BaseSettings):
    """Comprehensive logging configuration"""
    log_level: str = Field("INFO", env="LOG_LEVEL")
    log_to_console: bool = Field(True, env="LOG_TO_CONSOLE")
    log_to_file: bool = Field(True, env="LOG_TO_FILE")
    log_file_path: Path = Field(Path("./logs/trading_agent.log"), env="LOG_FILE_PATH")
    log_rotation: str = Field("10MB", env="LOG_ROTATION")
    log_retention: str = Field("30 days", env="LOG_RETENTION")
    log_format: str = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
    log_serialize: bool = True  # JSON format for structured logging

class DataConfig(BaseSettings):
    """Data pipeline configuration with yfinance and VIX"""
    tech_tickers: List[str] = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'META', 'NVDA', 'TSLA', 'AVGO', 'ADBE', 'ASML']
    vix_symbol: str = Field("^VIX", env="VIX_SYMBOL")
    cache_dir: Path = Field(Path("./data/cache"), env="CACHE_DIR")
    
    # Date Ranges
    train_start_date: str = '2016-01-01'
    train_end_date: str = '2021-12-31'
    valid_start_date: str = '2022-01-01'
    valid_end_date: str = '2022-12-31'
    test_start_date: str = '2023-01-01'
    test_end_date: str = '2025-04-19'
    
    # Data fetching settings
    yfinance_threads: bool = True
    yfinance_progress: bool = False
    cache_expiry_hours: int = 24

class TechnicalIndicatorsConfig(BaseSettings):
    """pandas_ta technical indicators configuration"""
    # Trend Indicators
    sma_periods: List[int] = [5, 10, 20, 50, 200]
    ema_periods: List[int] = [12, 26, 50]
    macd_fast: int = 12
    macd_slow: int = 26
    macd_signal: int = 9
    
    # Momentum Indicators
    rsi_period: int = 14
    stoch_k: int = 14
    stoch_d: int = 3
    williams_r_period: int = 14
    
    # Volatility Indicators
    bb_period: int = 20
    bb_std: float = 2.0
    atr_period: int = 14
    
    # Volume Indicators
    obv_enabled: bool = True
    ad_enabled: bool = True
    cmf_period: int = 20
    
    # VIX-related features
    vix_ma_periods: List[int] = [5, 10, 20]
    vix_percentile_window: int = 252  # 1 year

class OptunaTuningConfig(BaseSettings):
    """Optuna hyperparameter optimization configuration"""
    n_trials: int = Field(100, env="OPTUNA_N_TRIALS")
    timeout: Optional[int] = Field(3600, env="OPTUNA_TIMEOUT")  # 1 hour
    n_jobs: int = 1
    study_name: str = Field("sac_trading_optimization", env="OPTUNA_STUDY_NAME")
    storage_url: Optional[str] = None  # Use in-memory by default
    
    # SAC Hyperparameter Search Spaces
    learning_rate_range: tuple = (1e-5, 1e-2)
    batch_size_choices: List[int] = [64, 128, 256, 512]
    gamma_range: tuple = (0.9, 0.999)
    tau_range: tuple = (0.001, 0.01)
    alpha_range: tuple = (0.1, 0.5)
    buffer_size_choices: List[int] = [100000, 500000, 1000000]
    
    # Network architecture search
    hidden_sizes_choices: List[List[int]] = [[128, 128], [256, 256], [512, 256], [256, 256, 128]]

class SACModelConfig(BaseSettings):
    """SAC model configuration for ElegantRL"""
    # Network Architecture
    actor_hidden_sizes: List[int] = [256, 256]
    critic_hidden_sizes: List[int] = [256, 256]
    activation: str = "relu"
    
    # Training Parameters
    learning_rate: float = 3e-4
    batch_size: int = 256
    buffer_size: int = 1000000
    gamma: float = 0.99
    tau: float = 0.005
    alpha: float = 0.2
    
    # Training Schedule
    total_timesteps: int = 100000
    learning_starts: int = 1000
    train_freq: int = 1
    gradient_steps: int = 1
    
    # Model Persistence
    models_dir: Path = Field(Path("./models/saved"), env="MODELS_DIR")
    save_freq: int = 10000
    checkpoint_freq: int = 5000

class Settings(BaseSettings):
    """Main configuration combining all sub-configs"""
    # API Configuration
    alpaca_api_key: str = Field(..., env="ALPACA_API_KEY")
    alpaca_secret_key: str = Field(..., env="ALPACA_SECRET_KEY")
    alpaca_base_url: str = Field("https://paper-api.alpaca.markets", env="ALPACA_BASE_URL")
    
    # Sub-configurations
    logging: LoggingConfig = LoggingConfig()
    data: DataConfig = DataConfig()
    indicators: TechnicalIndicatorsConfig = TechnicalIndicatorsConfig()
    tuning: OptunaTuningConfig = OptunaTuningConfig()
    model: SACModelConfig = SACModelConfig()
    
    # Trading Parameters
    initial_amount: float = 1000000
    max_position_size: float = Field(0.1, env="MAX_POSITION_SIZE")
    risk_free_rate: float = Field(0.02, env="RISK_FREE_RATE")
    transaction_cost: float = Field(0.001, env="TRANSACTION_COST")
    
    # Results Directory
    results_dir: Path = Field(Path("./results"), env="RESULTS_DIR")
    
    class Config:
        env_file = ".env"
        env_nested_delimiter = "__"
```

## Technical Indicators & VIX Integration

### pandas_ta Integration
```python
import pandas_ta as ta
import yfinance as yf
from config.settings import Settings

def add_technical_indicators(df: pd.DataFrame, config: TechnicalIndicatorsConfig) -> pd.DataFrame:
    """Add comprehensive technical indicators using pandas_ta"""
    
    # Trend Indicators
    for period in config.sma_periods:
        df[f'SMA_{period}'] = ta.sma(df['Close'], length=period)
    
    for period in config.ema_periods:
        df[f'EMA_{period}'] = ta.ema(df['Close'], length=period)
    
    # MACD
    macd_data = ta.macd(df['Close'], fast=config.macd_fast, slow=config.macd_slow, signal=config.macd_signal)
    df = pd.concat([df, macd_data], axis=1)
    
    # Momentum Indicators
    df['RSI'] = ta.rsi(df['Close'], length=config.rsi_period)
    stoch_data = ta.stoch(df['High'], df['Low'], df['Close'], k=config.stoch_k, d=config.stoch_d)
    df = pd.concat([df, stoch_data], axis=1)
    df['WILLR'] = ta.willr(df['High'], df['Low'], df['Close'], length=config.williams_r_period)
    
    # Volatility Indicators
    bb_data = ta.bbands(df['Close'], length=config.bb_period, std=config.bb_std)
    df = pd.concat([df, bb_data], axis=1)
    df['ATR'] = ta.atr(df['High'], df['Low'], df['Close'], length=config.atr_period)
    
    # Volume Indicators
    if config.obv_enabled:
        df['OBV'] = ta.obv(df['Close'], df['Volume'])
    if config.ad_enabled:
        df['AD'] = ta.ad(df['High'], df['Low'], df['Close'], df['Volume'])
    df['CMF'] = ta.cmf(df['High'], df['Low'], df['Close'], df['Volume'], length=config.cmf_period)
    
    return df

def fetch_vix_data(start_date: str, end_date: str, config: TechnicalIndicatorsConfig) -> pd.DataFrame:
    """Fetch and process VIX data with technical indicators"""
    vix = yf.download('^VIX', start=start_date, end=end_date, progress=False)
    
    # Add VIX moving averages
    for period in config.vix_ma_periods:
        vix[f'VIX_MA_{period}'] = ta.sma(vix['Close'], length=period)
    
    # VIX percentile (fear/greed indicator)
    vix['VIX_Percentile'] = vix['Close'].rolling(window=config.vix_percentile_window).rank(pct=True)
    
    # VIX regime classification
    vix['VIX_Regime'] = pd.cut(vix['VIX_Percentile'], 
                              bins=[0, 0.2, 0.8, 1.0], 
                              labels=['Low_Vol', 'Normal_Vol', 'High_Vol'])
    
    return vix[['Close', 'VIX_MA_5', 'VIX_MA_10', 'VIX_MA_20', 'VIX_Percentile', 'VIX_Regime']].rename(
        columns={'Close': 'VIX'})
```

### Optuna Hyperparameter Tuning
```python
import optuna
from optuna.integration import PyTorchLightningPruningCallback
from config.settings import OptunaTuningConfig

def create_optuna_study(config: OptunaTuningConfig) -> optuna.Study:
    """Create Optuna study for SAC hyperparameter optimization"""
    
    study = optuna.create_study(
        study_name=config.study_name,
        direction='maximize',  # Maximize Sharpe ratio
        storage=config.storage_url,
        load_if_exists=True,
        pruner=optuna.pruners.MedianPruner(n_startup_trials=10, n_warmup_steps=20)
    )
    
    return study

def objective(trial: optuna.Trial, config: OptunaTuningConfig) -> float:
    """Optuna objective function for SAC hyperparameter tuning"""
    
    # Suggest hyperparameters
    learning_rate = trial.suggest_float('learning_rate', *config.learning_rate_range, log=True)
    batch_size = trial.suggest_categorical('batch_size', config.batch_size_choices)
    gamma = trial.suggest_float('gamma', *config.gamma_range)
    tau = trial.suggest_float('tau', *config.tau_range, log=True)
    alpha = trial.suggest_float('alpha', *config.alpha_range)
    buffer_size = trial.suggest_categorical('buffer_size', config.buffer_size_choices)
    hidden_sizes = trial.suggest_categorical('hidden_sizes', config.hidden_sizes_choices)
    
    # Create model with suggested hyperparameters
    model_config = SACModelConfig(
        learning_rate=learning_rate,
        batch_size=batch_size,
        gamma=gamma,
        tau=tau,
        alpha=alpha,
        buffer_size=buffer_size,
        actor_hidden_sizes=hidden_sizes,
        critic_hidden_sizes=hidden_sizes
    )
    
    # Train and evaluate model
    try:
        sharpe_ratio = train_and_evaluate_sac(model_config, trial)
        return sharpe_ratio
    except Exception as e:
        logger.error(f"Trial {trial.number} failed: {e}")
        raise optuna.TrialPruned()

def run_hyperparameter_tuning(config: OptunaTuningConfig) -> optuna.Study:
    """Run complete hyperparameter tuning process"""
    study = create_optuna_study(config)
    
    study.optimize(
        lambda trial: objective(trial, config),
        n_trials=config.n_trials,
        timeout=config.timeout,
        n_jobs=config.n_jobs,
        callbacks=[lambda study, trial: logger.info(f"Trial {trial.number} completed with value {trial.value}")]
    )
    
    logger.info(f"Best trial: {study.best_trial.value} with params: {study.best_trial.params}")
    return study
```

## Tool Usage Patterns

### Command Line Interface
```bash
# Data operations with VIX integration
python main.py get-data --symbols AAPL,MSFT --include-vix --start-date 2020-01-01
python main.py process-data --cache-dir ./data/cache --add-indicators

# Model operations with Optuna tuning
python main.py tune --trials 100 --timeout 3600 --study-name sac_optimization
python main.py train --config-from-study --episodes 1000 --save-interval 100

# Evaluation
python main.py backtest --model-path ./models/saved/sac_model.pth --include-vix
python main.py papertrade --duration 30 --log-level DEBUG  # 30 days with detailed logging
```

### Logging Configuration
```python
from loguru import logger
from pathlib import Path
from config.settings import Settings

def setup_logging(config: Settings):
    """Setup comprehensive logging with console and file output"""
    # Remove default handler
    logger.remove()
    
    # Console logging
    if config.logging.log_to_console:
        logger.add(
            sink=sys.stdout,
            level=config.logging.log_level,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | <level>{message}</level>",
            colorize=True
        )
    
    # File logging
    if config.logging.log_to_file:
        # Ensure log directory exists
        config.logging.log_file_path.parent.mkdir(parents=True, exist_ok=True)
        
        logger.add(
            sink=config.logging.log_file_path,
            level=config.logging.log_level,
            format=config.logging.log_format,
            rotation=config.logging.log_rotation,
            retention=config.logging.log_retention,
            serialize=config.logging.log_serialize,
            backtrace=True,
            diagnose=True
        )
    
    # Add structured fields for trading context
    logger.configure(extra={"service": "trading_agent", "version": "1.0.0"})
    
    return logger

# Usage in main application
config = Settings()
logger = setup_logging(config)

# Structured logging examples
logger.info("Starting data fetch", symbol="AAPL", start_date="2023-01-01")
logger.warning("High VIX detected", vix_value=35.2, threshold=30.0)
logger.error("Model training failed", epoch=150, loss=0.85, error="CUDA out of memory")
```

### Error Handling Patterns
```python
# API error handling
try:
    data = alpaca_client.get_bars(symbols, timeframe, start, end)
except APIError as e:
    logger.error(f"Alpaca API error: {e}")
    # Fallback to cached data
    data = cache_manager.get_cached_data(symbols, start, end)

# Model training error handling
try:
    agent.train(episodes=1000)
except RuntimeError as e:
    logger.error(f"Training failed: {e}")
    # Save partial model and resume
    agent.save_checkpoint("emergency_checkpoint.pth")
```

## Performance Considerations

### Memory Optimization
- **Data Loading**: Lazy loading with chunked processing
- **Model Training**: Gradient accumulation for large batches
- **Cache Management**: LRU cache with size limits

### Compute Optimization
- **Vectorization**: NumPy/Pandas operations
- **GPU Utilization**: CUDA for neural network training
- **Parallel Processing**: Multiprocessing for backtesting

### I/O Optimization
- **Async API Calls**: Non-blocking HTTP requests
- **Batch Operations**: Bulk data fetching
- **Compression**: Gzip for large cache files