# FinRL Stock Trading Agent - Project Brief

## Project Overview
A sophisticated stock trading agent application that trades the top 10 tech stocks using reinforcement learning algorithms. The system implements an Asymmetric Return Profile strategy to achieve flat returns during market downturns and positive returns during market upturns.

## Core Requirements

### Target Assets
- **Tech Stocks**: ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'META', 'NVDA', 'TSLA', 'AVGO', 'ADBE', 'ASML']

### Technology Stack
- **Language**: Python 3.11
- **Environment**: Conda
- **ML Libraries**: FinRL, ElegantRL (from GitHub repos)
- **Algorithm**: SAC (Soft Actor-Critic) model
- **Data Sources**: yfinance (primary), Alpaca API (trading)
- **Technical Indicators**: pandas_ta library
- **Market Indicators**: VIX integration for volatility regime detection
- **Hyperparameter Tuning**: Optuna optimization framework
- **Logging**: loguru with console and file output
- **Configuration**: .env file for API keys, comprehensive config management

### Operational Modes
Command-line interface with distinct operational phases:
1. `get-data` - Fetch market data with caching
2. `process-data` - Data preprocessing and feature engineering
3. `tune` - Hyperparameter optimization
4. `train` - Model training
5. `backtest` - Historical performance evaluation
6. `papertrade` - Live paper trading

### Data Management
- **Primary Data Source**: yfinance (fast and reliable)
- **Secondary Data Source**: Alpaca API (for trading execution)
- **VIX Integration**: Market volatility regime detection
- **Technical Indicators**: Comprehensive set via pandas_ta
- **Caching Strategy**: CSV-based cache with timestamp validation
- **Data Ranges**:
  - Training: 2016-01-01 to 2021-12-31
  - Validation: 2022-01-01 to 2022-12-31
  - Testing: 2023-01-01 to 2025-04-19

### Trading Strategy
- **Asymmetric Return Profile**: Designed to minimize losses during market downturns while capturing gains during upturns
- **Risk Management**: Built-in position sizing and risk controls

### Architecture Requirements
- **Modular Design**: Clean separation of concerns
- **Modern Python Practices**: Type hints, proper packaging, configuration management
- **Centralized Configuration**: Comprehensive settings with Pydantic validation
- **Logging Infrastructure**: Structured logging to console and files
- **Hyperparameter Optimization**: Automated tuning with Optuna
- **Technical Analysis**: Rich indicator set with pandas_ta
- **Market Regime Awareness**: VIX-based volatility detection
- **Extensible Framework**: Easy to add new features and strategies

## Success Criteria
1. Successful data acquisition and caching system
2. Robust preprocessing pipeline
3. Effective SAC model training with ElegantRL
4. Comprehensive backtesting framework
5. Reliable paper trading implementation
6. Asymmetric return profile achievement
7. Clean, maintainable codebase

## Constraints
- Must use ElegantRL and SAC model (not Stable-Baselines3 or PPO)
- Windows environment compatibility
- Conda environment management
- Alpaca API integration requirements