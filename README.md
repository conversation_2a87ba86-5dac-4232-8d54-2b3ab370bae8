# FinRL Stock Trading Agent

A sophisticated stock trading agent that trades the top 10 tech stocks using reinforcement learning algorithms with an **Asymmetric Return Profile** strategy. The system is designed to achieve flat returns during market downturns and positive returns during market upturns.

## 🎯 Project Overview

This trading agent implements:
- **ElegantRL SAC (Soft Actor-Critic)** algorithm for continuous action spaces
- **Asymmetric Return Profile** strategy for market regime adaptation
- **VIX-based volatility regime detection** for market awareness
- **Comprehensive technical indicators** using pandas_ta
- **Hyperparameter optimization** with Optuna
- **Production-grade logging** with loguru
- **Modular architecture** for easy extension and maintenance

### Target Assets
```
AAPL, MSFT, GOOGL, AMZN, META, NVDA, TSLA, AVGO, ADBE, ASML
```

## 🏗️ Architecture

```
finrl-bot3/
├── main.py                 # CLI entry point
├── config/                 # Configuration management
│   ├── settings.py         # Pydantic settings with validation
│   └── constants.py        # Static constants
├── src/
│   ├── data/              # Data acquisition and processing
│   ├── models/            # SAC agent and training
│   ├── strategies/        # Trading strategies
│   ├── trading/           # Trade execution
│   ├── backtesting/       # Performance evaluation
│   └── utils/             # Utilities and logging
├── data/cache/            # Cached market data
├── models/saved/          # Trained model artifacts
├── results/               # Backtest results and reports
└── tests/                 # Test suite
```

## 🚀 Quick Start

### 1. Environment Setup

#### Option A: Conda (Recommended)
```bash
# Create conda environment
conda env create -f environment.yml
conda activate finrl-trading-agent
```

#### Option B: Pip
```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

### 2. Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit .env with your API keys
# Required: ALPACA_API_KEY, ALPACA_API_SECRET
```

### 3. Basic Usage

```bash
# Fetch market data
python main.py get-data

# Process data with technical indicators
python main.py process-data

# Optimize hyperparameters
python main.py tune --trials 50

# Train the model
python main.py train

# Run backtest
python main.py backtest

# Start paper trading
python main.py papertrade --duration 30
```

## 📊 Features

### Data Pipeline
- **Primary Source**: yfinance (fast and reliable)
- **Secondary Source**: Alpaca API (for trading execution)
- **VIX Integration**: Market volatility regime detection
- **Smart Caching**: CSV-based cache with timestamp validation
- **Data Quality**: Comprehensive validation and cleaning

### Technical Indicators (pandas_ta)
- **Trend**: SMA, EMA, MACD
- **Momentum**: RSI, Stochastic, Williams %R
- **Volatility**: Bollinger Bands, ATR
- **Volume**: OBV, A/D Line, CMF
- **VIX Indicators**: Moving averages, percentiles, regime classification

### Machine Learning
- **Algorithm**: ElegantRL SAC (Soft Actor-Critic)
- **Environment**: Custom FinRL-compatible trading environment
- **Optimization**: Optuna hyperparameter tuning
- **Features**: OHLCV + Technical Indicators + VIX + Market Regime

### Risk Management
- **Asymmetric Return Profile**: Adaptive allocation based on market conditions
- **Position Sizing**: Maximum 20% per asset
- **Stop Loss**: Configurable risk limits
- **Drawdown Control**: Maximum 15% drawdown protection

## 🔧 CLI Commands

### Data Management
```bash
# Fetch data for specific symbols
python main.py get-data --symbols AAPL,MSFT --start-date 2023-01-01

# Force refresh cached data
python main.py get-data --force-refresh

# Process data with custom directories
python main.py process-data --input-dir data/raw --output-dir data/processed
```

### Model Training
```bash
# Hyperparameter optimization
python main.py tune --trials 100 --study-name my_optimization

# Train with custom config
python main.py train --config-override '{"sac": {"learning_rate": 0.001}}'

# Resume training from checkpoint
python main.py train --resume --checkpoint-path models/checkpoints/latest.pkl
```

### Evaluation
```bash
# Backtest specific period
python main.py backtest --start-date 2023-01-01 --end-date 2023-12-31

# Paper trading with custom model
python main.py papertrade --model models/saved/best_model.pkl --duration 60

# Dry run (simulation only)
python main.py papertrade --dry-run
```

## 📈 Performance Metrics

The system tracks comprehensive performance metrics:

- **Returns**: Total, Annual, Risk-adjusted
- **Risk**: Volatility, Maximum Drawdown, VaR
- **Ratios**: Sharpe, Sortino, Calmar
- **Trading**: Win Rate, Profit Factor, Average Trade
- **Benchmark**: Comparison against SPY

## 🔍 Monitoring and Logging

### Structured Logging
```python
# Automatic logging of all operations
2024-01-15 10:30:15 | INFO | data.fetcher:fetch_symbol_data:45 | Fetching data for AAPL
2024-01-15 10:30:16 | SUCCESS | data.fetcher:fetch_symbol_data:67 | Successfully fetched 1,250 records for AAPL
```

### Performance Monitoring
- Function execution times
- Memory usage tracking
- Data quality metrics
- Model training progress
- Trading action logs

## 🧪 Testing

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=src --cov-report=html

# Run specific test module
pytest tests/test_data_fetcher.py -v
```

## 📋 Configuration

The system uses a hierarchical configuration system:

1. **Default values** in `config/settings.py`
2. **Environment variables** from `.env` file
3. **Command-line overrides** via CLI arguments
4. **Config file overrides** via `--config` parameter

### Key Configuration Sections

- **Data**: Sources, symbols, date ranges, caching
- **Indicators**: Technical indicator parameters
- **SAC**: Model architecture and training parameters
- **Optuna**: Hyperparameter optimization settings
- **Trading**: Portfolio and risk management
- **Logging**: Output levels and formats

## 🔐 Security

- **API Keys**: Stored in `.env` file (never committed)
- **Paper Trading**: Default to paper trading environment
- **Input Validation**: Pydantic schema validation
- **Error Handling**: Graceful degradation and logging

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Setup

```bash
# Install development dependencies
pip install -e .[dev]

# Install pre-commit hooks
pre-commit install

# Run code formatting
black src/ tests/
flake8 src/ tests/
mypy src/
```

## 📚 Documentation

- **API Documentation**: Generated with Sphinx
- **Code Examples**: In `examples/` directory
- **Architecture Docs**: In `docs/` directory
- **Memory Bank**: Project documentation in `memory-bank/`

## 🐛 Troubleshooting

### Common Issues

1. **API Key Errors**
   ```bash
   # Check your .env file
   cat .env | grep ALPACA
   ```

2. **Data Fetch Failures**
   ```bash
   # Clear cache and retry
   rm -rf data/cache/*
   python main.py get-data --force-refresh
   ```

3. **Memory Issues**
   ```bash
   # Reduce batch size in config
   export SAC_BATCH_SIZE=128
   ```

4. **Windows Path Issues**
   ```bash
   # Use forward slashes in paths
   python main.py get-data --log-file logs/trading.log
   ```

### Getting Help

- Check the logs in `logs/trading_agent.log`
- Review configuration in `.env`
- Validate data quality with `process-data`
- Use `--verbose` flag for detailed output

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **FinRL**: Financial Reinforcement Learning framework
- **ElegantRL**: High-performance RL algorithms
- **Alpaca**: Commission-free trading API
- **pandas_ta**: Technical analysis indicators
- **Optuna**: Hyperparameter optimization

---

**Disclaimer**: This software is for educational and research purposes only. Trading involves substantial risk of loss and is not suitable for all investors. Past performance does not guarantee future results.